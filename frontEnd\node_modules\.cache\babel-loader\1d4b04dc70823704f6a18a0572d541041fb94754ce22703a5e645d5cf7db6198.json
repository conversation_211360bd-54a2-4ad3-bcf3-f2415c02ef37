{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction ExpandedRow(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var prefixCls = props.prefixCls,\n    children = props.children,\n    Component = props.component,\n    cellComponent = props.cellComponent,\n    className = props.className,\n    expanded = props.expanded,\n    colSpan = props.colSpan,\n    isEmpty = props.isEmpty;\n  var _useContext = useContext(TableContext, ['scrollbarSize', 'fixHeader', 'fixColumn', 'componentWidth', 'horizonScroll']),\n    scrollbarSize = _useContext.scrollbarSize,\n    fixHeader = _useContext.fixHeader,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth,\n    horizonScroll = _useContext.horizonScroll;\n\n  // Cache render node\n  var contentNode = children;\n  if (isEmpty ? horizonScroll : fixColumn) {\n    contentNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: componentWidth - (fixHeader ? scrollbarSize : 0),\n        position: 'sticky',\n        left: 0,\n        overflow: 'hidden'\n      },\n      className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n    }, componentWidth !== 0 && contentNode);\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: className,\n    style: {\n      display: expanded ? null : 'none'\n    }\n  }, /*#__PURE__*/React.createElement(Cell, {\n    component: cellComponent,\n    prefixCls: prefixCls,\n    colSpan: colSpan\n  }, contentNode));\n}\nexport default ExpandedRow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}