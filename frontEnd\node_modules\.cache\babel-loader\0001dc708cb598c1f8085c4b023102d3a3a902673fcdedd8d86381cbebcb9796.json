{"ast": null, "code": "import * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = _ref => {\n  let {\n    enabledEllipsis,\n    isEllipsis,\n    children,\n    tooltipProps\n  } = _ref;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}