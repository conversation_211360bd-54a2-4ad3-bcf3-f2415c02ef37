{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport Portal from '@rc-component/portal';\nimport classnames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nvar Operations = function Operations(props) {\n  var visible = props.visible,\n    maskTransitionName = props.maskTransitionName,\n    getContainer = props.getContainer,\n    prefixCls = props.prefixCls,\n    rootClassName = props.rootClassName,\n    icons = props.icons,\n    countRender = props.countRender,\n    showSwitch = props.showSwitch,\n    showProgress = props.showProgress,\n    current = props.current,\n    transform = props.transform,\n    count = props.count,\n    scale = props.scale,\n    minScale = props.minScale,\n    maxScale = props.maxScale,\n    closeIcon = props.closeIcon,\n    onSwitchLeft = props.onSwitchLeft,\n    onSwitchRight = props.onSwitchRight,\n    onClose = props.onClose,\n    onZoomIn = props.onZoomIn,\n    onZoomOut = props.onZoomOut,\n    onRotateRight = props.onRotateRight,\n    onRotateLeft = props.onRotateLeft,\n    onFlipX = props.onFlipX,\n    onFlipY = props.onFlipY,\n    toolbarRender = props.toolbarRender;\n  var groupContext = useContext(PreviewGroupContext);\n  var rotateLeft = icons.rotateLeft,\n    rotateRight = icons.rotateRight,\n    zoomIn = icons.zoomIn,\n    zoomOut = icons.zoomOut,\n    close = icons.close,\n    left = icons.left,\n    right = icons.right,\n    flipX = icons.flipX,\n    flipY = icons.flipY;\n  var toolClassName = \"\".concat(prefixCls, \"-operations-operation\");\n  React.useEffect(function () {\n    var onKeyDown = function onKeyDown(e) {\n      if (e.keyCode === KeyCode.ESC) {\n        onClose();\n      }\n    };\n    if (visible) {\n      window.addEventListener('keydown', onKeyDown);\n    }\n    return function () {\n      window.removeEventListener('keydown', onKeyDown);\n    };\n  }, [visible]);\n  var tools = [{\n    icon: flipY,\n    onClick: onFlipY,\n    type: 'flipY'\n  }, {\n    icon: flipX,\n    onClick: onFlipX,\n    type: 'flipX'\n  }, {\n    icon: rotateLeft,\n    onClick: onRotateLeft,\n    type: 'rotateLeft'\n  }, {\n    icon: rotateRight,\n    onClick: onRotateRight,\n    type: 'rotateRight'\n  }, {\n    icon: zoomOut,\n    onClick: onZoomOut,\n    type: 'zoomOut',\n    disabled: scale === minScale\n  }, {\n    icon: zoomIn,\n    onClick: onZoomIn,\n    type: 'zoomIn',\n    disabled: scale === maxScale\n  }];\n  var toolsNode = tools.map(function (_ref) {\n    var _classnames;\n    var icon = _ref.icon,\n      onClick = _ref.onClick,\n      type = _ref.type,\n      disabled = _ref.disabled;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(toolClassName, (_classnames = {}, _defineProperty(_classnames, \"\".concat(prefixCls, \"-operations-operation-\").concat(type), true), _defineProperty(_classnames, \"\".concat(prefixCls, \"-operations-operation-disabled\"), !!disabled), _classnames)),\n      onClick: onClick,\n      key: type\n    }, icon);\n  });\n  var toolbarNode = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-operations\")\n  }, toolsNode);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: maskTransitionName\n  }, function (_ref2) {\n    var className = _ref2.className,\n      style = _ref2.style;\n    return /*#__PURE__*/React.createElement(Portal, {\n      open: true,\n      getContainer: getContainer !== null && getContainer !== void 0 ? getContainer : document.body\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(\"\".concat(prefixCls, \"-operations-wrapper\"), className, rootClassName),\n      style: style\n    }, closeIcon === null ? null : /*#__PURE__*/React.createElement(\"button\", {\n      className: \"\".concat(prefixCls, \"-close\"),\n      onClick: onClose\n    }, closeIcon || close), showSwitch && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(\"\".concat(prefixCls, \"-switch-left\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-left-disabled\"), current === 0)),\n      onClick: onSwitchLeft\n    }, left), /*#__PURE__*/React.createElement(\"div\", {\n      className: classnames(\"\".concat(prefixCls, \"-switch-right\"), _defineProperty({}, \"\".concat(prefixCls, \"-switch-right-disabled\"), current === count - 1)),\n      onClick: onSwitchRight\n    }, right)), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-footer\")\n    }, showProgress && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(prefixCls, \"-progress\")\n    }, countRender ? countRender(current + 1, count) : \"\".concat(current + 1, \" / \").concat(count)), toolbarRender ? toolbarRender(toolbarNode, _objectSpread({\n      icons: {\n        flipYIcon: toolsNode[0],\n        flipXIcon: toolsNode[1],\n        rotateLeftIcon: toolsNode[2],\n        rotateRightIcon: toolsNode[3],\n        zoomOutIcon: toolsNode[4],\n        zoomInIcon: toolsNode[5]\n      },\n      actions: {\n        onFlipY: onFlipY,\n        onFlipX: onFlipX,\n        onRotateLeft: onRotateLeft,\n        onRotateRight: onRotateRight,\n        onZoomOut: onZoomOut,\n        onZoomIn: onZoomIn\n      },\n      transform: transform\n    }, groupContext ? {\n      current: current,\n      total: count\n    } : {})) : toolbarNode)));\n  });\n};\nexport default Operations;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}