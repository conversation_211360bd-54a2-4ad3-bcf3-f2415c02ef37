{"ast": null, "code": "const getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}