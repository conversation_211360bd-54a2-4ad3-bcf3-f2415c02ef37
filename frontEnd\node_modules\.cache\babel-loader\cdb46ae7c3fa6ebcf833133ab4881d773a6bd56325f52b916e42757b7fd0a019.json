{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"draggingIndex\"];\nimport * as React from 'react';\nimport Handle from './Handle';\nimport { getIndex } from '../util';\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    draggingIndex = props.draggingIndex,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 ? void 0 : _handlesRef$current$i.focus();\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: draggingIndex === index,\n      prefixCls: prefixCls,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index,\n      onStartMove: onStartMove,\n      onOffsetChange: onOffsetChange,\n      render: handleRender\n    }, restProps));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}