{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport TransBtn from \"../TransBtn\";\nimport React from 'react';\nexport function useAllowClear(prefixCls, onClearMouseDown, displayValues, allowClear, clearIcon) {\n  var disabled = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  var mergedSearchValue = arguments.length > 6 ? arguments[6] : undefined;\n  var mode = arguments.length > 7 ? arguments[7] : undefined;\n  var mergedClearIcon = React.useMemo(function () {\n    if (_typeof(allowClear) === \"object\") {\n      return allowClear.clearIcon;\n    }\n    if (!!clearIcon) return clearIcon;\n  }, [allowClear, clearIcon]);\n  var mergedAllowClear = React.useMemo(function () {\n    if (!disabled && !!allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n      return true;\n    }\n    return false;\n  }, [allowClear, disabled, displayValues.length, mergedSearchValue, mode]);\n  return {\n    allowClear: mergedAllowClear,\n    clearIcon: /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: mergedClearIcon\n    }, \"\\xD7\")\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}