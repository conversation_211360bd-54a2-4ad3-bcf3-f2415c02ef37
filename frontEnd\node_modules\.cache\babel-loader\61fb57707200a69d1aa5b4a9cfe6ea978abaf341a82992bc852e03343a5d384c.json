{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = scopeTab;\nvar _tabbable = require(\"./tabbable\");\nvar _tabbable2 = _interopRequireDefault(_tabbable);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction getActiveElement() {\n  var el = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  return el.activeElement.shadowRoot ? getActiveElement(el.activeElement.shadowRoot) : el.activeElement;\n}\nfunction scopeTab(node, event) {\n  var tabbable = (0, _tabbable2.default)(node);\n  if (!tabbable.length) {\n    // Do nothing, since there are no elements that can receive focus.\n    event.preventDefault();\n    return;\n  }\n  var target = void 0;\n  var shiftKey = event.shiftKey;\n  var head = tabbable[0];\n  var tail = tabbable[tabbable.length - 1];\n  var activeElement = getActiveElement();\n\n  // proceed with default browser behavior on tab.\n  // Focus on last element on shift + tab.\n  if (node === activeElement) {\n    if (!shiftKey) return;\n    target = tail;\n  }\n  if (tail === activeElement && !shiftKey) {\n    target = head;\n  }\n  if (head === activeElement && shiftKey) {\n    target = tail;\n  }\n  if (target) {\n    event.preventDefault();\n    target.focus();\n    return;\n  }\n\n  // Safari radio issue.\n  //\n  // Safari does not move the focus to the radio button,\n  // so we need to force it to really walk through all elements.\n  //\n  // This is very error prone, since we are trying to guess\n  // if it is a safari browser from the first occurence between\n  // chrome or safari.\n  //\n  // The chrome user agent contains the first ocurrence\n  // as the 'chrome/version' and later the 'safari/version'.\n  var checkSafari = /(\\bChrome\\b|\\bSafari\\b)\\//.exec(navigator.userAgent);\n  var isSafariDesktop = checkSafari != null && checkSafari[1] != \"Chrome\" && /\\biPod\\b|\\biPad\\b/g.exec(navigator.userAgent) == null;\n\n  // If we are not in safari desktop, let the browser control\n  // the focus\n  if (!isSafariDesktop) return;\n  var x = tabbable.indexOf(activeElement);\n  if (x > -1) {\n    x += shiftKey ? -1 : 1;\n  }\n  target = tabbable[x];\n\n  // If the tabbable element does not exist,\n  // focus head/tail based on shiftKey\n  if (typeof target === \"undefined\") {\n    event.preventDefault();\n    target = shiftKey ? tail : head;\n    target.focus();\n    return;\n  }\n  event.preventDefault();\n  target.focus();\n}\nmodule.exports = exports[\"default\"];", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}