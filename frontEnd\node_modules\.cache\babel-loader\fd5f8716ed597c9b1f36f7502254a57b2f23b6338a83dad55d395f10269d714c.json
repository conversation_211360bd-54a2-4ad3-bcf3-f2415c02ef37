{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown, { flattenKeys } from './FilterDropdown';\nfunction collectFilterStates(columns, init, pos) {\n  let filterStates = [];\n  (columns || []).forEach((column, index) => {\n    var _a;\n    const columnPos = getColumnPos(index, pos);\n    if (column.filters || 'filterDropdown' in column || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        let filteredValues = column.filteredValue;\n        if (!('filterDropdown' in column)) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n}\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, locale, triggerFilter, getPopupContainer, pos) {\n  return columns.map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    const {\n      filterMultiple = true,\n      filterMode,\n      filterSearch\n    } = column;\n    let newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const filterState = filterStates.find(_ref => {\n        let {\n          key\n        } = _ref;\n        return columnKey === key;\n      });\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        title: renderProps => /*#__PURE__*/React.createElement(FilterDropdown, {\n          tablePrefixCls: prefixCls,\n          prefixCls: `${prefixCls}-filter`,\n          dropdownPrefixCls: dropdownPrefixCls,\n          column: newColumn,\n          columnKey: columnKey,\n          filterState: filterState,\n          filterMultiple: filterMultiple,\n          filterMode: filterMode,\n          filterSearch: filterSearch,\n          triggerFilter: triggerFilter,\n          locale: locale,\n          getPopupContainer: getPopupContainer\n        }, renderColumnTitle(column.title, renderProps))\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, locale, triggerFilter, getPopupContainer, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction generateFilterInfo(filterStates) {\n  const currentFilters = {};\n  filterStates.forEach(_ref2 => {\n    let {\n      key,\n      filteredKeys,\n      column\n    } = _ref2;\n    const {\n      filters,\n      filterDropdown\n    } = column;\n    if (filterDropdown) {\n      currentFilters[key] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      const keys = flattenKeys(filters);\n      currentFilters[key] = keys.filter(originKey => filteredKeys.includes(String(originKey)));\n    } else {\n      currentFilters[key] = null;\n    }\n  });\n  return currentFilters;\n}\nexport function getFilterData(data, filterStates) {\n  return filterStates.reduce((currentData, filterState) => {\n    const {\n      column: {\n        onFilter,\n        filters\n      },\n      filteredKeys\n    } = filterState;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData.filter(record => filteredKeys.some(key => {\n        const keys = flattenKeys(filters);\n        const keyIndex = keys.findIndex(k => String(k) === String(key));\n        const realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n        return onFilter(realKey, record);\n      }));\n    }\n    return currentData;\n  }, data);\n}\nconst getMergedColumns = rawMergedColumns => rawMergedColumns.flatMap(column => {\n  if ('children' in column) {\n    return [column].concat(_toConsumableArray(getMergedColumns(column.children || [])));\n  }\n  return [column];\n});\nfunction useFilter(_ref3) {\n  let {\n    prefixCls,\n    dropdownPrefixCls,\n    mergedColumns: rawMergedColumns,\n    onFilterChange,\n    getPopupContainer,\n    locale: tableLocale\n  } = _ref3;\n  const mergedColumns = React.useMemo(() => getMergedColumns(rawMergedColumns || []), [rawMergedColumns]);\n  const [filterStates, setFilterStates] = React.useState(() => collectFilterStates(mergedColumns, true));\n  const mergedFilterStates = React.useMemo(() => {\n    const collectedStates = collectFilterStates(mergedColumns, false);\n    if (collectedStates.length === 0) {\n      return collectedStates;\n    }\n    let filteredKeysIsAllNotControlled = true;\n    let filteredKeysIsAllControlled = true;\n    collectedStates.forEach(_ref4 => {\n      let {\n        filteredKeys\n      } = _ref4;\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      // Filter column may have been removed\n      const keyList = (mergedColumns || []).map((column, index) => getColumnKey(column, getColumnPos(index)));\n      return filterStates.filter(_ref5 => {\n        let {\n          key\n        } = _ref5;\n        return keyList.includes(key);\n      }).map(item => {\n        const col = mergedColumns[keyList.findIndex(key => key === item.key)];\n        return Object.assign(Object.assign({}, item), {\n          column: Object.assign(Object.assign({}, item.column), col),\n          forceFiltered: col.filtered\n        });\n      });\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'Table', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  const filters = React.useMemo(() => generateFilterInfo(mergedFilterStates), [mergedFilterStates]);\n  const triggerFilter = filterState => {\n    const newFilterStates = mergedFilterStates.filter(_ref6 => {\n      let {\n        key\n      } = _ref6;\n      return key !== filterState.key;\n    });\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  const transformColumns = innerColumns => injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, tableLocale, triggerFilter, getPopupContainer);\n  return [transformColumns, mergedFilterStates, filters];\n}\nexport { flattenKeys };\nexport default useFilter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}