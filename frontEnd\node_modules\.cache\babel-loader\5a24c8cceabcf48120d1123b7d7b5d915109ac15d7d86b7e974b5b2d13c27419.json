{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n  warning(canUseDom() || !props.open, \"Drawer with 'open' in SSR is not work since no place to createPortal. Please move to 'useEffect' instead.\");\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}