{"ast": null, "code": "import useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Select from '../../select';\nimport { ColorFormat } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [ColorFormat.hex, ColorFormat.hsb, ColorFormat.rgb].map(format => ({\n  value: format,\n  label: format.toLocaleUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(ColorFormat.hex, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = `${prefixCls}-input`;\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case ColorFormat.hsb:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case ColorFormat.rgb:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      case ColorFormat.hex:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorInputPrefixCls}-container`\n  }, /*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    bordered: false,\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: `${prefixCls}-format-select`,\n    size: \"small\",\n    options: selectOptions\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && /*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  }));\n};\nexport default ColorInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}