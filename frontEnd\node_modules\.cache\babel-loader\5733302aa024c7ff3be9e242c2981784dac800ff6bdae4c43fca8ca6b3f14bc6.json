{"ast": null, "code": "import Trigger from '@rc-component/trigger';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport DropdownMenu from \"./DropdownMenu\";\nvar BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 1,\n      adjustY: 1\n    }\n  }\n};\nvar KeywordTrigger = function KeywordTrigger(props) {\n  var prefixCls = props.prefixCls,\n    options = props.options,\n    children = props.children,\n    visible = props.visible,\n    transitionName = props.transitionName,\n    getPopupContainer = props.getPopupContainer,\n    dropdownClassName = props.dropdownClassName,\n    direction = props.direction,\n    placement = props.placement;\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var dropdownElement = /*#__PURE__*/React.createElement(DropdownMenu, {\n    prefixCls: dropdownPrefix,\n    options: options\n  });\n  var dropdownPlacement = useMemo(function () {\n    var popupPlacement;\n    if (direction === 'rtl') {\n      popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n    } else {\n      popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n    }\n    return popupPlacement;\n  }, [direction, placement]);\n  return /*#__PURE__*/React.createElement(Trigger, {\n    prefixCls: dropdownPrefix,\n    popupVisible: visible,\n    popup: dropdownElement,\n    popupPlacement: dropdownPlacement,\n    popupTransitionName: transitionName,\n    builtinPlacements: BUILT_IN_PLACEMENTS,\n    getPopupContainer: getPopupContainer,\n    popupClassName: dropdownClassName\n  }, children);\n};\nexport default KeywordTrigger;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}