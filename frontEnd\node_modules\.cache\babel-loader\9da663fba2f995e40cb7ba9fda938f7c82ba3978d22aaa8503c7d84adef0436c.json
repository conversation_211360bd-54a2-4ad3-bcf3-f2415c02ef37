{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// [times, realValue]\n\nvar SPLIT = '%';\nvar Entity = /*#__PURE__*/function () {\n  function Entity(instanceId) {\n    _classCallCheck(this, Entity);\n    _defineProperty(this, \"instanceId\", void 0);\n    /** @private Internal cache map. Do not access this directly */\n    _defineProperty(this, \"cache\", new Map());\n    this.instanceId = instanceId;\n  }\n  _createClass(Entity, [{\n    key: \"get\",\n    value: function get(keys) {\n      return this.cache.get(keys.join(SPLIT)) || null;\n    }\n  }, {\n    key: \"update\",\n    value: function update(keys, valueFn) {\n      var path = keys.join(SPLIT);\n      var prevValue = this.cache.get(path);\n      var nextValue = valueFn(prevValue);\n      if (nextValue === null) {\n        this.cache.delete(path);\n      } else {\n        this.cache.set(path, nextValue);\n      }\n    }\n  }]);\n  return Entity;\n}();\nexport default Entity;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}