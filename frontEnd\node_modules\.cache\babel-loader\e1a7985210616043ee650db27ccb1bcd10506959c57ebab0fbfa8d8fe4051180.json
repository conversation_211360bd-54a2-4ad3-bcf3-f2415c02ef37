{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport { getCellFixedInfo } from \"../utils/fixUtil\";\nexport default function useFixedInfo(flattenColumns, stickyOffsets, direction, columns) {\n  var fixedInfoList = flattenColumns.map(function (_, colIndex) {\n    return getCellFixedInfo(colIndex, colIndex, flattenColumns, stickyOffsets, direction, columns === null || columns === void 0 ? void 0 : columns[colIndex]);\n  });\n  return useMemo(function () {\n    return fixedInfoList;\n  }, [fixedInfoList], function (prev, next) {\n    return !isEqual(prev, next);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}