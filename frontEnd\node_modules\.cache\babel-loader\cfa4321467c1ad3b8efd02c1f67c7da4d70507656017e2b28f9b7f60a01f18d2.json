{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nexport var SEARCH_MARK = '__rc_cascader_search_mark__';\nvar defaultFilter = function defaultFilter(search, options, _ref) {\n  var label = _ref.label;\n  return options.some(function (opt) {\n    return String(opt[label]).toLowerCase().includes(search.toLowerCase());\n  });\n};\nvar defaultRender = function defaultRender(inputValue, path, prefixCls, fieldNames) {\n  return path.map(function (opt) {\n    return opt[fieldNames.label];\n  }).join(' / ');\n};\nexport default (function (search, options, fieldNames, prefixCls, config, changeOnSelect) {\n  var _config$filter = config.filter,\n    filter = _config$filter === void 0 ? defaultFilter : _config$filter,\n    _config$render = config.render,\n    render = _config$render === void 0 ? defaultRender : _config$render,\n    _config$limit = config.limit,\n    limit = _config$limit === void 0 ? 50 : _config$limit,\n    sort = config.sort;\n  return React.useMemo(function () {\n    var filteredOptions = [];\n    if (!search) {\n      return [];\n    }\n    function dig(list, pathOptions) {\n      list.forEach(function (option) {\n        // Perf saving when `sort` is disabled and `limit` is provided\n        if (!sort && limit > 0 && filteredOptions.length >= limit) {\n          return;\n        }\n        var connectedPathOptions = [].concat(_toConsumableArray(pathOptions), [option]);\n        var children = option[fieldNames.children];\n\n        // If current option is filterable\n        if (\n        // If is leaf option\n        !children || children.length === 0 ||\n        // If is changeOnSelect\n        changeOnSelect) {\n          if (filter(search, connectedPathOptions, {\n            label: fieldNames.label\n          })) {\n            var _objectSpread2;\n            filteredOptions.push(_objectSpread(_objectSpread({}, option), {}, (_objectSpread2 = {}, _defineProperty(_objectSpread2, fieldNames.label, render(search, connectedPathOptions, prefixCls, fieldNames)), _defineProperty(_objectSpread2, SEARCH_MARK, connectedPathOptions), _defineProperty(_objectSpread2, fieldNames.children, undefined), _objectSpread2)));\n          }\n        }\n        if (children) {\n          dig(option[fieldNames.children], connectedPathOptions);\n        }\n      });\n    }\n    dig(options, []);\n\n    // Do sort\n    if (sort) {\n      filteredOptions.sort(function (a, b) {\n        return sort(a[SEARCH_MARK], b[SEARCH_MARK], search, fieldNames);\n      });\n    }\n    return limit > 0 ? filteredOptions.slice(0, limit) : filteredOptions;\n  }, [search, options, fieldNames, prefixCls, render, changeOnSelect, filter, sort, limit]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}