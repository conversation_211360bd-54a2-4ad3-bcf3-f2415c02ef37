{"ast": null, "code": "const genLayoutLightStyle = token => {\n  const {\n    componentCls,\n    colorBgContainer,\n    colorBgBody,\n    colorText\n  } = token;\n  return {\n    [`${componentCls}-sider-light`]: {\n      background: colorBgContainer,\n      [`${componentCls}-sider-trigger`]: {\n        color: colorText,\n        background: colorBgContainer\n      },\n      [`${componentCls}-sider-zero-width-trigger`]: {\n        color: colorText,\n        background: colorBgContainer,\n        border: `1px solid ${colorBgBody}`,\n        borderInlineStart: 0\n      }\n    }\n  };\n};\nexport default genLayoutLightStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}