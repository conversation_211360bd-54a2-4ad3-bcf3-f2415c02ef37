[{"E:\\20\\BRAINWAVE\\frontEnd\\src\\index.js": "1", "E:\\20\\BRAINWAVE\\frontEnd\\src\\reportWebVitals.js": "2", "E:\\20\\BRAINWAVE\\frontEnd\\src\\App.js": "3", "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\store.js": "4", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\Loader.js": "5", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ProtectedRoute.js": "6", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminProtectedRoute.js": "7", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\MathTest.js": "8", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\RankingErrorBoundary.js": "9", "E:\\20\\BRAINWAVE\\frontEnd\\src\\contexts\\ThemeContext.js": "10", "E:\\20\\BRAINWAVE\\frontEnd\\src\\contexts\\LanguageContext.js": "11", "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\usersSlice.js": "12", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\DebugAuth.jsx": "13", "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\subscriptionSlice.js": "14", "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\loaderSlice.js": "15", "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\paymentSlice.js": "16", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\RankingDemo.js": "17", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\QuizPlay.js": "18", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "19", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\QuizResult.js": "20", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\AddEditExam.js": "21", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\index.js": "22", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\index.js": "23", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\index.js": "24", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Forum\\index.js": "25", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Profile\\index.js": "26", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\VideoLessons\\index.js": "27", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Videos\\index.js": "28", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\AdminReports\\index.js": "29", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\index.js": "30", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Dashboard\\index.js": "31", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Users\\index.js": "32", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Skills\\index.js": "33", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Test\\index.js": "34", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Subscription\\index.js": "35", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Hub\\index.js": "36", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Ranking\\index.js": "37", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Skills\\index.js": "38", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\VideoLessons\\index.js": "39", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Login\\index.js": "40", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\StudyMaterial\\index.js": "41", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Register\\index.js": "42", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Home\\index.js": "43", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\UserReports\\index.js": "44", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\WriteExam\\index.js": "45", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Forum\\index.js": "46", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Profile\\index.js": "47", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminNavigation.js": "48", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\payment.js": "49", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ContentRenderer.js": "50", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\FloatingBrainwaveAI.js": "51", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\users.js": "52", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\notifications.js": "53", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\aiQuestions.js": "54", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\syllabus.js": "55", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\exams.js": "56", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\chat.js": "57", "E:\\20\\BRAINWAVE\\frontEnd\\src\\localization\\kiswahili.js": "58", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\PageTitle.js": "59", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\reports.js": "60", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\forum.js": "61", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\videoComments.js": "62", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminLayout.js": "63", "E:\\20\\BRAINWAVE\\frontEnd\\src\\data\\Subjects.jsx": "64", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\UserRankingList.js": "65", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\BrainwaveHeader.js": "66", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Card.js": "67", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Input.js": "68", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Button.js": "69", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Loading.js": "70", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizQuestion.js": "71", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizCard.js": "72", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ThemeToggle.js": "73", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\LazyImage.js": "74", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ErrorBoundary.js": "75", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\PerformanceMonitor.js": "76", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\UserRankingCard.js": "77", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\ProfilePicture.js": "78", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ResponsiveContainer.js": "79", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizTimer.js": "80", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "81", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminCard.js": "82", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\skills.js": "83", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\study.js": "84", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\plans.js": "85", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\OnlineStatusIndicator.js": "86", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "87", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "88", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "89", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "90", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\LiteratureForm.js": "91", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\VideoLessons\\VideoGrid.js": "92", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "93", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AnimatedCounter.js": "94", "E:\\20\\BRAINWAVE\\frontEnd\\src\\utils\\quizDataUtils.js": "95", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\index.js": "96", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\XPResultDisplay.js": "97", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx": "98", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\NotificationBell.js": "99", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx": "100", "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\WriteExam\\Instructions.js": "101", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminTopNavigation.js": "102", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ModernSidebar.js": "103", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\LevelBadge.js": "104", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\AchievementBadge.js": "105", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\EnhancedAchievementBadge.js": "106", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\MathPreview.js": "107", "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\subtitles.js": "108", "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\XPProgressBar.js": "109"}, {"size": 395, "mtime": 1696247250000, "results": "110", "hashOfConfig": "111"}, {"size": 362, "mtime": 1696247250000, "results": "112", "hashOfConfig": "111"}, {"size": 14687, "mtime": 1753210005073, "results": "113", "hashOfConfig": "111"}, {"size": 430, "mtime": 1736735017645, "results": "114", "hashOfConfig": "111"}, {"size": 180, "mtime": 1696247250000, "results": "115", "hashOfConfig": "111"}, {"size": 15412, "mtime": 1754850306667, "results": "116", "hashOfConfig": "111"}, {"size": 1976, "mtime": 1753125406498, "results": "117", "hashOfConfig": "111"}, {"size": 3893, "mtime": 1752428949596, "results": "118", "hashOfConfig": "111"}, {"size": 3109, "mtime": 1751260973778, "results": "119", "hashOfConfig": "111"}, {"size": 1410, "mtime": 1751140352157, "results": "120", "hashOfConfig": "111"}, {"size": 3773, "mtime": 1752336238191, "results": "121", "hashOfConfig": "111"}, {"size": 334, "mtime": 1696247250000, "results": "122", "hashOfConfig": "111"}, {"size": 9770, "mtime": 1751495320007, "results": "123", "hashOfConfig": "111"}, {"size": 404, "mtime": 1736731932223, "results": "124", "hashOfConfig": "111"}, {"size": 416, "mtime": 1696247250000, "results": "125", "hashOfConfig": "111"}, {"size": 449, "mtime": 1736732007232, "results": "126", "hashOfConfig": "111"}, {"size": 12711, "mtime": 1751260271529, "results": "127", "hashOfConfig": "111"}, {"size": 45468, "mtime": 1753219925434, "results": "128", "hashOfConfig": "111"}, {"size": 10989, "mtime": 1751586090664, "results": "129", "hashOfConfig": "111"}, {"size": 51877, "mtime": 1753020678847, "results": "130", "hashOfConfig": "111"}, {"size": 17963, "mtime": 1752929888663, "results": "131", "hashOfConfig": "111"}, {"size": 1140, "mtime": 1751426583568, "results": "132", "hashOfConfig": "111"}, {"size": 10464, "mtime": 1752187297118, "results": "133", "hashOfConfig": "111"}, {"size": 55270, "mtime": 1753225790967, "results": "134", "hashOfConfig": "111"}, {"size": 13629, "mtime": 1752410363528, "results": "135", "hashOfConfig": "111"}, {"size": 10039, "mtime": 1752412121501, "results": "136", "hashOfConfig": "111"}, {"size": 14559, "mtime": 1752410607882, "results": "137", "hashOfConfig": "111"}, {"size": 8721, "mtime": 1752407120937, "results": "138", "hashOfConfig": "111"}, {"size": 16063, "mtime": 1751870755172, "results": "139", "hashOfConfig": "111"}, {"size": 9390, "mtime": 1753253984736, "results": "140", "hashOfConfig": "111"}, {"size": 12328, "mtime": 1754852122158, "results": "141", "hashOfConfig": "111"}, {"size": 22409, "mtime": 1754853083007, "results": "142", "hashOfConfig": "111"}, {"size": 16536, "mtime": 1752343895497, "results": "143", "hashOfConfig": "111"}, {"size": 1327, "mtime": 1709427669270, "results": "144", "hashOfConfig": "111"}, {"size": 56231, "mtime": 1752999773421, "results": "145", "hashOfConfig": "111"}, {"size": 10399, "mtime": 1752778041163, "results": "146", "hashOfConfig": "111"}, {"size": 136154, "mtime": 1752777153203, "results": "147", "hashOfConfig": "111"}, {"size": 18710, "mtime": 1752774608687, "results": "148", "hashOfConfig": "111"}, {"size": 35979, "mtime": 1753224718241, "results": "149", "hashOfConfig": "111"}, {"size": 4986, "mtime": 1753125734373, "results": "150", "hashOfConfig": "111"}, {"size": 30171, "mtime": 1753136561782, "results": "151", "hashOfConfig": "111"}, {"size": 12071, "mtime": 1752998333438, "results": "152", "hashOfConfig": "111"}, {"size": 67286, "mtime": 1753653545755, "results": "153", "hashOfConfig": "111"}, {"size": 28312, "mtime": 1752776522375, "results": "154", "hashOfConfig": "111"}, {"size": 48440, "mtime": 1752427431675, "results": "155", "hashOfConfig": "111"}, {"size": 30303, "mtime": 1754854378829, "results": "156", "hashOfConfig": "111"}, {"size": 41459, "mtime": 1754853003571, "results": "157", "hashOfConfig": "111"}, {"size": 16167, "mtime": 1752411576158, "results": "158", "hashOfConfig": "111"}, {"size": 2046, "mtime": 1752102606942, "results": "159", "hashOfConfig": "111"}, {"size": 11067, "mtime": 1753010068391, "results": "160", "hashOfConfig": "111"}, {"size": 24509, "mtime": 1752597935437, "results": "161", "hashOfConfig": "111"}, {"size": 3447, "mtime": 1752950227370, "results": "162", "hashOfConfig": "111"}, {"size": 3632, "mtime": 1751487806125, "results": "163", "hashOfConfig": "111"}, {"size": 6337, "mtime": 1751558223480, "results": "164", "hashOfConfig": "111"}, {"size": 7315, "mtime": 1751495843287, "results": "165", "hashOfConfig": "111"}, {"size": 2455, "mtime": 1751479784424, "results": "166", "hashOfConfig": "111"}, {"size": 1104, "mtime": 1749936905424, "results": "167", "hashOfConfig": "111"}, {"size": 13619, "mtime": 1752346804847, "results": "168", "hashOfConfig": "111"}, {"size": 388, "mtime": 1703845955779, "results": "169", "hashOfConfig": "111"}, {"size": 3391, "mtime": 1751304153158, "results": "170", "hashOfConfig": "111"}, {"size": 1833, "mtime": 1753239696152, "results": "171", "hashOfConfig": "111"}, {"size": 2438, "mtime": 1752385967903, "results": "172", "hashOfConfig": "111"}, {"size": 2200, "mtime": 1751563008113, "results": "173", "hashOfConfig": "111"}, {"size": 1813, "mtime": 1753195576383, "results": "174", "hashOfConfig": "111"}, {"size": 29870, "mtime": 1752145669266, "results": "175", "hashOfConfig": "111"}, {"size": 17367, "mtime": 1754852974140, "results": "176", "hashOfConfig": "111"}, {"size": 1857, "mtime": 1751140385464, "results": "177", "hashOfConfig": "111"}, {"size": 2324, "mtime": 1751140401815, "results": "178", "hashOfConfig": "111"}, {"size": 2913, "mtime": 1751140370241, "results": "179", "hashOfConfig": "111"}, {"size": 3119, "mtime": 1751164996340, "results": "180", "hashOfConfig": "111"}, {"size": 13299, "mtime": 1751249005755, "results": "181", "hashOfConfig": "111"}, {"size": 16632, "mtime": 1753021137264, "results": "182", "hashOfConfig": "111"}, {"size": 3904, "mtime": 1751143777976, "results": "183", "hashOfConfig": "111"}, {"size": 2504, "mtime": 1751957740575, "results": "184", "hashOfConfig": "111"}, {"size": 5088, "mtime": 1751143254906, "results": "185", "hashOfConfig": "111"}, {"size": 6304, "mtime": 1751188593099, "results": "186", "hashOfConfig": "111"}, {"size": 18256, "mtime": 1751482855935, "results": "187", "hashOfConfig": "111"}, {"size": 6419, "mtime": 1752780771309, "results": "188", "hashOfConfig": "111"}, {"size": 4989, "mtime": 1751143312418, "results": "189", "hashOfConfig": "111"}, {"size": 7165, "mtime": 1752195834207, "results": "190", "hashOfConfig": "111"}, {"size": 15334, "mtime": 1753011746108, "results": "191", "hashOfConfig": "111"}, {"size": 1717, "mtime": 1751561083661, "results": "192", "hashOfConfig": "111"}, {"size": 2718, "mtime": 1752343803321, "results": "193", "hashOfConfig": "111"}, {"size": 7973, "mtime": 1753290657772, "results": "194", "hashOfConfig": "111"}, {"size": 279, "mtime": 1736719733927, "results": "195", "hashOfConfig": "111"}, {"size": 3307, "mtime": 1751855844189, "results": "196", "hashOfConfig": "111"}, {"size": 9494, "mtime": 1750995979612, "results": "197", "hashOfConfig": "111"}, {"size": 11421, "mtime": 1753298066120, "results": "198", "hashOfConfig": "111"}, {"size": 30047, "mtime": 1753296413588, "results": "199", "hashOfConfig": "111"}, {"size": 18423, "mtime": 1753131199110, "results": "200", "hashOfConfig": "111"}, {"size": 10160, "mtime": 1753118373176, "results": "201", "hashOfConfig": "111"}, {"size": 16470, "mtime": 1752774503700, "results": "202", "hashOfConfig": "111"}, {"size": 18956, "mtime": 1753118148814, "results": "203", "hashOfConfig": "111"}, {"size": 3412, "mtime": 1752407264475, "results": "204", "hashOfConfig": "111"}, {"size": 9114, "mtime": 1751691985112, "results": "205", "hashOfConfig": "111"}, {"size": 3742, "mtime": 1753126110105, "results": "206", "hashOfConfig": "111"}, {"size": 16372, "mtime": 1751479340474, "results": "207", "hashOfConfig": "111"}, {"size": 6486, "mtime": 1752078868080, "results": "208", "hashOfConfig": "111"}, {"size": 15303, "mtime": 1754853888642, "results": "209", "hashOfConfig": "111"}, {"size": 9653, "mtime": 1752084006410, "results": "210", "hashOfConfig": "111"}, {"size": 3835, "mtime": 1751478376207, "results": "211", "hashOfConfig": "111"}, {"size": 6949, "mtime": 1754852059227, "results": "212", "hashOfConfig": "111"}, {"size": 13727, "mtime": 1752777834573, "results": "213", "hashOfConfig": "111"}, {"size": 7685, "mtime": 1751244700154, "results": "214", "hashOfConfig": "111"}, {"size": 11901, "mtime": 1751236424130, "results": "215", "hashOfConfig": "111"}, {"size": 10081, "mtime": 1751244608756, "results": "216", "hashOfConfig": "111"}, {"size": 1711, "mtime": 1752428921115, "results": "217", "hashOfConfig": "111"}, {"size": 1524, "mtime": 1750994293078, "results": "218", "hashOfConfig": "111"}, {"size": 8429, "mtime": 1751244672688, "results": "219", "hashOfConfig": "111"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xf9p1e", {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\20\\BRAINWAVE\\frontEnd\\src\\index.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\reportWebVitals.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\App.js", ["547"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\store.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\Loader.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ProtectedRoute.js", ["548", "549", "550", "551", "552", "553", "554", "555"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminProtectedRoute.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\MathTest.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\RankingErrorBoundary.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\contexts\\ThemeContext.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\contexts\\LanguageContext.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\usersSlice.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\DebugAuth.jsx", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\subscriptionSlice.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\loaderSlice.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\redux\\paymentSlice.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\RankingDemo.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\QuizPlay.js", ["556"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["557"], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\QuizResult.js", ["558"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\AddEditExam.js", ["559", "560"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\index.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\index.js", ["561"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Quiz\\index.js", ["562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Forum\\index.js", ["574", "575", "576"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Profile\\index.js", ["577", "578"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\VideoLessons\\index.js", ["579", "580", "581"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Videos\\index.js", ["582", "583"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\AdminReports\\index.js", ["584", "585", "586"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Dashboard\\index.js", ["587", "588", "589", "590", "591"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Users\\index.js", ["592", "593", "594", "595"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Skills\\index.js", ["596", "597", "598"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Test\\index.js", ["599", "600"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Subscription\\index.js", ["601", "602"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Hub\\index.js", ["603", "604", "605", "606", "607", "608", "609", "610"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Ranking\\index.js", ["611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\Skills\\index.js", ["629", "630"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\VideoLessons\\index.js", ["631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Login\\index.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\StudyMaterial\\index.js", ["658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Register\\index.js", ["677"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Home\\index.js", ["678"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\UserReports\\index.js", ["679", "680", "681", "682", "683", "684", "685", "686", "687", "688"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\WriteExam\\index.js", ["689", "690", "691"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Forum\\index.js", ["692", "693", "694"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\common\\Profile\\index.js", ["695", "696", "697", "698", "699", "700", "701", "702", "703"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminNavigation.js", ["704", "705", "706"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\payment.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ContentRenderer.js", ["707", "708", "709"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\FloatingBrainwaveAI.js", ["710", "711", "712"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\users.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\notifications.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\aiQuestions.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\syllabus.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\exams.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\chat.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\localization\\kiswahili.js", ["713"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\PageTitle.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\reports.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\forum.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\videoComments.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminLayout.js", ["714"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\data\\Subjects.jsx", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\UserRankingList.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\BrainwaveHeader.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Card.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Input.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Button.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\Loading.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizQuestion.js", ["715"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizCard.js", ["716", "717", "718"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ThemeToggle.js", ["719"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\LazyImage.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ErrorBoundary.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\PerformanceMonitor.js", ["720", "721"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\UserRankingCard.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\ProfilePicture.js", ["722"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\ResponsiveContainer.js", ["723"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\QuizTimer.js", ["724"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\Exams\\AddEditQuestion.js", ["725", "726"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminCard.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\skills.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\study.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\plans.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["727", "728", "729", "730"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\admin\\StudyMaterials\\LiteratureForm.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\VideoLessons\\VideoGrid.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\StudyMaterial\\PDFModal.js", ["731"], ["732"], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AnimatedCounter.js", ["733"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\utils\\quizDataUtils.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\index.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\XPResultDisplay.js", ["734"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\common\\NotificationBell.js", ["735", "736", "737"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\pages\\user\\WriteExam\\Instructions.js", ["738"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\AdminTopNavigation.js", ["739", "740"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\ModernSidebar.js", ["741", "742", "743", "744", "745", "746", "747", "748", "749", "750"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\LevelBadge.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\AchievementBadge.js", ["751", "752", "753"], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\MathPreview.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\apicalls\\subtitles.js", [], [], "E:\\20\\BRAINWAVE\\frontEnd\\src\\components\\modern\\XPProgressBar.js", ["754"], [], {"ruleId": "755", "severity": 1, "message": "756", "line": 44, "column": 7, "nodeType": "757", "messageId": "758", "endLine": 44, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "759", "line": 2, "column": 46, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 61}, {"ruleId": "755", "severity": 1, "message": "760", "line": 7, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 7, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "761", "line": 7, "column": 23, "nodeType": "757", "messageId": "758", "endLine": 7, "endColumn": 34}, {"ruleId": "755", "severity": 1, "message": "762", "line": 12, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 23}, {"ruleId": "763", "severity": 1, "message": "764", "line": 118, "column": 6, "nodeType": "765", "endLine": 118, "endColumn": 8, "suggestions": "766"}, {"ruleId": "763", "severity": 1, "message": "767", "line": 210, "column": 6, "nodeType": "765", "endLine": 210, "endColumn": 53, "suggestions": "768"}, {"ruleId": "763", "severity": 1, "message": "769", "line": 293, "column": 6, "nodeType": "765", "endLine": 293, "endColumn": 39, "suggestions": "770"}, {"ruleId": "763", "severity": 1, "message": "771", "line": 304, "column": 6, "nodeType": "765", "endLine": 304, "endColumn": 25, "suggestions": "772"}, {"ruleId": "763", "severity": 1, "message": "773", "line": 432, "column": 6, "nodeType": "765", "endLine": 432, "endColumn": 57, "suggestions": "774"}, {"ruleId": "763", "severity": 1, "message": "775", "line": 46, "column": 6, "nodeType": "765", "endLine": 46, "endColumn": 8, "suggestions": "776", "suppressions": "777"}, {"ruleId": "778", "severity": 1, "message": "779", "line": 1151, "column": 29, "nodeType": "780", "endLine": 1160, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "781", "line": 1, "column": 35, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 41}, {"ruleId": "763", "severity": 1, "message": "782", "line": 110, "column": 6, "nodeType": "765", "endLine": 110, "endColumn": 8, "suggestions": "783"}, {"ruleId": "763", "severity": 1, "message": "784", "line": 191, "column": 6, "nodeType": "765", "endLine": 191, "endColumn": 8, "suggestions": "785"}, {"ruleId": "755", "severity": 1, "message": "786", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "787", "line": 17, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 17, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "788", "line": 18, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "789", "line": 101, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 101, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "790", "line": 101, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 101, "endColumn": 39}, {"ruleId": "755", "severity": 1, "message": "791", "line": 128, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 128, "endColumn": 28}, {"ruleId": "763", "severity": 1, "message": "792", "line": 424, "column": 6, "nodeType": "765", "endLine": 424, "endColumn": 22, "suggestions": "793"}, {"ruleId": "763", "severity": 1, "message": "794", "line": 466, "column": 6, "nodeType": "765", "endLine": 466, "endColumn": 8, "suggestions": "795"}, {"ruleId": "763", "severity": 1, "message": "796", "line": 633, "column": 6, "nodeType": "765", "endLine": 633, "endColumn": 70, "suggestions": "797"}, {"ruleId": "763", "severity": 1, "message": "798", "line": 649, "column": 6, "nodeType": "765", "endLine": 649, "endColumn": 21, "suggestions": "799"}, {"ruleId": "755", "severity": 1, "message": "790", "line": 1033, "column": 24, "nodeType": "757", "messageId": "758", "endLine": 1033, "endColumn": 36}, {"ruleId": "755", "severity": 1, "message": "800", "line": 1042, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 1042, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "801", "line": 2, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 32}, {"ruleId": "755", "severity": 1, "message": "802", "line": 2, "column": 34, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 38}, {"ruleId": "763", "severity": 1, "message": "803", "line": 114, "column": 6, "nodeType": "765", "endLine": 114, "endColumn": 8, "suggestions": "804"}, {"ruleId": "755", "severity": 1, "message": "805", "line": 11, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 15}, {"ruleId": "763", "severity": 1, "message": "806", "line": 21, "column": 6, "nodeType": "765", "endLine": 21, "endColumn": 8, "suggestions": "807"}, {"ruleId": "755", "severity": 1, "message": "808", "line": 8, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 8, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "809", "line": 8, "column": 28, "nodeType": "757", "messageId": "758", "endLine": 8, "endColumn": 47}, {"ruleId": "763", "severity": 1, "message": "810", "line": 83, "column": 6, "nodeType": "765", "endLine": 83, "endColumn": 8, "suggestions": "811"}, {"ruleId": "755", "severity": 1, "message": "812", "line": 3, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 3, "endColumn": 17}, {"ruleId": "763", "severity": 1, "message": "813", "line": 47, "column": 6, "nodeType": "765", "endLine": 47, "endColumn": 8, "suggestions": "814"}, {"ruleId": "755", "severity": 1, "message": "815", "line": 5, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "816", "line": 31, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 31, "endColumn": 17}, {"ruleId": "763", "severity": 1, "message": "817", "line": 241, "column": 6, "nodeType": "765", "endLine": 241, "endColumn": 35, "suggestions": "818"}, {"ruleId": "755", "severity": 1, "message": "819", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "820", "line": 14, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 14, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "821", "line": 16, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 16, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "822", "line": 40, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 40, "endColumn": 17}, {"ruleId": "763", "severity": 1, "message": "823", "line": 44, "column": 6, "nodeType": "765", "endLine": 44, "endColumn": 8, "suggestions": "824"}, {"ruleId": "755", "severity": 1, "message": "825", "line": 12, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "826", "line": 31, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 31, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "827", "line": 43, "column": 19, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 29}, {"ruleId": "763", "severity": 1, "message": "828", "line": 182, "column": 6, "nodeType": "765", "endLine": 182, "endColumn": 8, "suggestions": "829"}, {"ruleId": "755", "severity": 1, "message": "830", "line": 8, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 8, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "831", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "832", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "833", "line": 1, "column": 38, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 46}, {"ruleId": "755", "severity": 1, "message": "834", "line": 8, "column": 12, "nodeType": "757", "messageId": "758", "endLine": 8, "endColumn": 19}, {"ruleId": "763", "severity": 1, "message": "835", "line": 139, "column": 6, "nodeType": "765", "endLine": 139, "endColumn": 8, "suggestions": "836"}, {"ruleId": "763", "severity": 1, "message": "837", "line": 193, "column": 6, "nodeType": "765", "endLine": 193, "endColumn": 24, "suggestions": "838"}, {"ruleId": "755", "severity": 1, "message": "839", "line": 9, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 9, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "840", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "841", "line": 15, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 15, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "842", "line": 16, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 16, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "843", "line": 21, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "844", "line": 22, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 22, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "789", "line": 29, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 29, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "845", "line": 114, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 114, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "846", "line": 2, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 33}, {"ruleId": "755", "severity": 1, "message": "847", "line": 21, "column": 53, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 67}, {"ruleId": "755", "severity": 1, "message": "848", "line": 24, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 24, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "816", "line": 71, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 71, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "849", "line": 75, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 75, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "850", "line": 75, "column": 20, "nodeType": "757", "messageId": "758", "endLine": 75, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "851", "line": 76, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 76, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "852", "line": 76, "column": 21, "nodeType": "757", "messageId": "758", "endLine": 76, "endColumn": 33}, {"ruleId": "755", "severity": 1, "message": "853", "line": 77, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 77, "endColumn": 24}, {"ruleId": "755", "severity": 1, "message": "854", "line": 80, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 80, "endColumn": 27}, {"ruleId": "755", "severity": 1, "message": "855", "line": 82, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 82, "endColumn": 24}, {"ruleId": "755", "severity": 1, "message": "856", "line": 90, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 90, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "857", "line": 91, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 91, "endColumn": 23}, {"ruleId": "763", "severity": 1, "message": "858", "line": 896, "column": 6, "nodeType": "765", "endLine": 896, "endColumn": 8, "suggestions": "859"}, {"ruleId": "755", "severity": 1, "message": "860", "line": 917, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 917, "endColumn": 24}, {"ruleId": "755", "severity": 1, "message": "861", "line": 1072, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 1072, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "862", "line": 1550, "column": 39, "nodeType": "757", "messageId": "758", "endLine": 1550, "endColumn": 48}, {"ruleId": "863", "severity": 1, "message": "864", "line": 2146, "column": 27, "nodeType": "865", "messageId": "866", "endLine": 2146, "endColumn": 28}, {"ruleId": "755", "severity": 1, "message": "789", "line": 23, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "867", "line": 39, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 39, "endColumn": 20}, {"ruleId": "755", "severity": 1, "message": "868", "line": 1, "column": 60, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 66}, {"ruleId": "755", "severity": 1, "message": "869", "line": 3, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 3, "endColumn": 16}, {"ruleId": "755", "severity": 1, "message": "846", "line": 3, "column": 18, "nodeType": "757", "messageId": "758", "endLine": 3, "endColumn": 33}, {"ruleId": "755", "severity": 1, "message": "870", "line": 11, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 20}, {"ruleId": "755", "severity": 1, "message": "871", "line": 33, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 33, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "872", "line": 35, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "873", "line": 36, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "874", "line": 37, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 37, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "875", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "876", "line": 45, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 45, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "789", "line": 50, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 50, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "790", "line": 50, "column": 27, "nodeType": "757", "messageId": "758", "endLine": 50, "endColumn": 39}, {"ruleId": "763", "severity": 1, "message": "877", "line": 126, "column": 6, "nodeType": "765", "endLine": 126, "endColumn": 8, "suggestions": "878"}, {"ruleId": "755", "severity": 1, "message": "879", "line": 153, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 153, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "880", "line": 166, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 166, "endColumn": 20}, {"ruleId": "755", "severity": 1, "message": "881", "line": 168, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 168, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "882", "line": 168, "column": 24, "nodeType": "757", "messageId": "758", "endLine": 168, "endColumn": 39}, {"ruleId": "763", "severity": 1, "message": "883", "line": 304, "column": 6, "nodeType": "765", "endLine": 304, "endColumn": 31, "suggestions": "884"}, {"ruleId": "755", "severity": 1, "message": "885", "line": 416, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 416, "endColumn": 24}, {"ruleId": "755", "severity": 1, "message": "886", "line": 426, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 426, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "887", "line": 530, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 530, "endColumn": 23}, {"ruleId": "755", "severity": 1, "message": "888", "line": 539, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 539, "endColumn": 29}, {"ruleId": "755", "severity": 1, "message": "889", "line": 627, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 627, "endColumn": 23}, {"ruleId": "755", "severity": 1, "message": "890", "line": 702, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 702, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "891", "line": 707, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 707, "endColumn": 30}, {"ruleId": "755", "severity": 1, "message": "892", "line": 736, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 736, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "893", "line": 774, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 774, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "894", "line": 17, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 17, "endColumn": 16}, {"ruleId": "755", "severity": 1, "message": "895", "line": 18, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "896", "line": 26, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 26, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "897", "line": 27, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 27, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "898", "line": 28, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 28, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "899", "line": 29, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 29, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "821", "line": 30, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 30, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "900", "line": 31, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 31, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "901", "line": 32, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 32, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "902", "line": 34, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 34, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "903", "line": 35, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 6}, {"ruleId": "755", "severity": 1, "message": "904", "line": 36, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 36, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "905", "line": 37, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 37, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "906", "line": 38, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "907", "line": 39, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 39, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "789", "line": 46, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 46, "endColumn": 12}, {"ruleId": "763", "severity": 1, "message": "877", "line": 84, "column": 6, "nodeType": "765", "endLine": 84, "endColumn": 8, "suggestions": "908"}, {"ruleId": "763", "severity": 1, "message": "909", "line": 106, "column": 6, "nodeType": "765", "endLine": 106, "endColumn": 39, "suggestions": "910"}, {"ruleId": "763", "severity": 1, "message": "909", "line": 156, "column": 6, "nodeType": "765", "endLine": 156, "endColumn": 36, "suggestions": "911"}, {"ruleId": "755", "severity": 1, "message": "912", "line": 8, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 8, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "816", "line": 32, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 32, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "825", "line": 3, "column": 8, "nodeType": "757", "messageId": "758", "endLine": 3, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "900", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "820", "line": 14, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 14, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "913", "line": 15, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 15, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "914", "line": 16, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 16, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "899", "line": 17, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 17, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "849", "line": 35, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 18}, {"ruleId": "755", "severity": 1, "message": "850", "line": 35, "column": 20, "nodeType": "757", "messageId": "758", "endLine": 35, "endColumn": 31}, {"ruleId": "763", "severity": 1, "message": "915", "line": 137, "column": 6, "nodeType": "765", "endLine": 137, "endColumn": 8, "suggestions": "916"}, {"ruleId": "763", "severity": 1, "message": "917", "line": 141, "column": 6, "nodeType": "765", "endLine": 141, "endColumn": 60, "suggestions": "918"}, {"ruleId": "755", "severity": 1, "message": "919", "line": 18, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 31}, {"ruleId": "755", "severity": 1, "message": "920", "line": 18, "column": 33, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 43}, {"ruleId": "763", "severity": 1, "message": "921", "line": 781, "column": 6, "nodeType": "765", "endLine": 781, "endColumn": 81, "suggestions": "922"}, {"ruleId": "755", "severity": 1, "message": "868", "line": 1, "column": 38, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 44}, {"ruleId": "763", "severity": 1, "message": "923", "line": 74, "column": 8, "nodeType": "765", "endLine": 74, "endColumn": 10, "suggestions": "924"}, {"ruleId": "763", "severity": 1, "message": "803", "line": 81, "column": 8, "nodeType": "765", "endLine": 81, "endColumn": 40, "suggestions": "925"}, {"ruleId": "755", "severity": 1, "message": "926", "line": 43, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 43, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "927", "line": 46, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 46, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "928", "line": 61, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 61, "endColumn": 27}, {"ruleId": "763", "severity": 1, "message": "929", "line": 136, "column": 6, "nodeType": "765", "endLine": 136, "endColumn": 32, "suggestions": "930"}, {"ruleId": "763", "severity": 1, "message": "923", "line": 183, "column": 6, "nodeType": "765", "endLine": 183, "endColumn": 8, "suggestions": "931"}, {"ruleId": "755", "severity": 1, "message": "932", "line": 364, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 364, "endColumn": 32}, {"ruleId": "755", "severity": 1, "message": "933", "line": 412, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 412, "endColumn": 26}, {"ruleId": "763", "severity": 1, "message": "923", "line": 436, "column": 6, "nodeType": "765", "endLine": 436, "endColumn": 8, "suggestions": "934"}, {"ruleId": "763", "severity": 1, "message": "935", "line": 443, "column": 6, "nodeType": "765", "endLine": 443, "endColumn": 19, "suggestions": "936"}, {"ruleId": "755", "severity": 1, "message": "937", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "938", "line": 18, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 18, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "939", "line": 30, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 30, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "940", "line": 160, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 160, "endColumn": 24}, {"ruleId": "941", "severity": 1, "message": "942", "line": 227, "column": 111, "nodeType": "943", "messageId": "944", "endLine": 227, "endColumn": 112, "suggestions": "945"}, {"ruleId": "941", "severity": 1, "message": "942", "line": 249, "column": 89, "nodeType": "943", "messageId": "944", "endLine": 249, "endColumn": 90, "suggestions": "946"}, {"ruleId": "755", "severity": 1, "message": "947", "line": 2, "column": 19, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 26}, {"ruleId": "755", "severity": 1, "message": "948", "line": 2, "column": 28, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 38}, {"ruleId": "755", "severity": 1, "message": "903", "line": 2, "column": 40, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 43}, {"ruleId": "863", "severity": 1, "message": "949", "line": 235, "column": 3, "nodeType": "865", "messageId": "866", "endLine": 235, "endColumn": 17}, {"ruleId": "755", "severity": 1, "message": "805", "line": 7, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 7, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "950", "line": 1, "column": 38, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 46}, {"ruleId": "755", "severity": 1, "message": "821", "line": 11, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 11, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "951", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "952", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "953", "line": 112, "column": 23, "nodeType": "757", "messageId": "758", "endLine": 112, "endColumn": 34}, {"ruleId": "954", "severity": 1, "message": "955", "line": 69, "column": 3, "nodeType": "956", "messageId": "957", "endLine": 90, "endColumn": 5}, {"ruleId": "755", "severity": 1, "message": "958", "line": 198, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 198, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "870", "line": 2, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 20}, {"ruleId": "755", "severity": 1, "message": "869", "line": 2, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 2, "endColumn": 16}, {"ruleId": "755", "severity": 1, "message": "959", "line": 56, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 56, "endColumn": 22}, {"ruleId": "755", "severity": 1, "message": "960", "line": 38, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 21}, {"ruleId": "755", "severity": 1, "message": "961", "line": 38, "column": 23, "nodeType": "757", "messageId": "758", "endLine": 38, "endColumn": 37}, {"ruleId": "755", "severity": 1, "message": "830", "line": 21, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 21, "endColumn": 8}, {"ruleId": "755", "severity": 1, "message": "832", "line": 22, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 22, "endColumn": 11}, {"ruleId": "755", "severity": 1, "message": "895", "line": 23, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 23, "endColumn": 11}, {"ruleId": "763", "severity": 1, "message": "962", "line": 96, "column": 6, "nodeType": "765", "endLine": 96, "endColumn": 15, "suggestions": "963"}, {"ruleId": "755", "severity": 1, "message": "964", "line": 31, "column": 10, "nodeType": "757", "messageId": "758", "endLine": 31, "endColumn": 24}, {"ruleId": "763", "severity": 1, "message": "965", "line": 291, "column": 6, "nodeType": "765", "endLine": 291, "endColumn": 32, "suggestions": "966", "suppressions": "967"}, {"ruleId": "763", "severity": 1, "message": "968", "line": 34, "column": 39, "nodeType": "757", "endLine": 34, "endColumn": 46}, {"ruleId": "755", "severity": 1, "message": "969", "line": 128, "column": 5, "nodeType": "757", "messageId": "758", "endLine": 128, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "938", "line": 9, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 9, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "970", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 10}, {"ruleId": "763", "severity": 1, "message": "971", "line": 94, "column": 6, "nodeType": "765", "endLine": 94, "endColumn": 14, "suggestions": "972"}, {"ruleId": "755", "severity": 1, "message": "800", "line": 7, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 7, "endColumn": 19}, {"ruleId": "755", "severity": 1, "message": "937", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "938", "line": 15, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 15, "endColumn": 13}, {"ruleId": "755", "severity": 1, "message": "973", "line": 1, "column": 17, "nodeType": "757", "messageId": "758", "endLine": 1, "endColumn": 25}, {"ruleId": "755", "severity": 1, "message": "974", "line": 6, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 6, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "975", "line": 10, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 10, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "937", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "976", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 14}, {"ruleId": "755", "severity": 1, "message": "977", "line": 20, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 20, "endColumn": 9}, {"ruleId": "755", "severity": 1, "message": "805", "line": 26, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 26, "endColumn": 15}, {"ruleId": "755", "severity": 1, "message": "789", "line": 27, "column": 11, "nodeType": "757", "messageId": "758", "endLine": 27, "endColumn": 12}, {"ruleId": "763", "severity": 1, "message": "978", "line": 61, "column": 6, "nodeType": "765", "endLine": 61, "endColumn": 14, "suggestions": "979"}, {"ruleId": "980", "severity": 1, "message": "981", "line": 244, "column": 21, "nodeType": "982", "messageId": "983", "endLine": 250, "endColumn": 24}, {"ruleId": "755", "severity": 1, "message": "984", "line": 5, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 5, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "913", "line": 12, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 12, "endColumn": 10}, {"ruleId": "755", "severity": 1, "message": "985", "line": 13, "column": 3, "nodeType": "757", "messageId": "758", "endLine": 13, "endColumn": 12}, {"ruleId": "755", "severity": 1, "message": "986", "line": 62, "column": 9, "nodeType": "757", "messageId": "758", "endLine": 62, "endColumn": 32}, "no-unused-vars", "'Test' is assigned a value but never used.", "Identifier", "unusedVar", "'startTransition' is defined but never used.", "'HideLoading' is defined but never used.", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["987"], "React Hook useEffect has missing dependencies: 'dispatch' and 'subscriptionData?.endDate'. Either include them or remove the dependency array.", ["988"], "React Hook useEffect has missing dependencies: 'dispatch' and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["989"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["990"], "React Hook useCallback has missing dependencies: 'quiz', 'refreshUserData', and 'submitting'. Either include them or remove the dependency array.", ["991"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["992"], ["993"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["994"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["995"], "'TbBrain' is defined but never used.", "'TbHome' is defined but never used.", "'TbBolt' is defined but never used.", "'t' is assigned a value but never used.", "'getClassName' is assigned a value but never used.", "'forceRefreshQuizzes' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'examCache'. Either include it or remove the dependency array.", ["996"], "React Hook useEffect has missing dependencies: 'getUserResults' and 'user'. Either include them or remove the dependency array.", ["997"], "React Hook useMemo has a missing dependency: 'user.level'. Either include it or remove the dependency array.", ["998"], "React Hook useEffect has a missing dependency: 'preloadQuizData'. Either include it or remove the dependency array.", ["999"], "'formatTime' is assigned a value but never used.", "'Input' is defined but never used.", "'Form' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1000"], "'user' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserDetails'. Either include it or remove the dependency array.", ["1001"], "'getVideoComments' is defined but never used.", "'approveVideoComment' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchVideoComments'. Either include it or remove the dependency array.", ["1002"], "'TbVideo' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchVideos'. Either include it or remove the dependency array.", ["1003"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["1004"], "'TbTarget' is defined but never used.", "'TbClock' is defined but never used.", "'TbEye' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["1005"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["1006"], "'FaEye' is defined but never used.", "'FaStar' is defined but never used.", "'FaFilter' is defined but never used.", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checkCurrentSubscription' and 'fetchPlans'. Either include them or remove the dependency array.", ["1007"], "React Hook useEffect has a missing dependency: 'isSubscriptionExpired'. Either include it or remove the dependency array.", ["1008"], "'FaHome' is defined but never used.", "'FaUser' is defined but never used.", "'FaCreditCard' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["1009"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'videoError' is assigned a value but never used.", "'useRef' is defined but never used.", "'motion' is defined but never used.", "'MdVerified' is defined but never used.", "'FaPlayCircle' is assigned a value but never used.", "'FaTimes' is assigned a value but never used.", "'FaExpand' is assigned a value but never used.", "'FaCompress' is assigned a value but never used.", "'TbVideo' is assigned a value but never used.", "'TbInfoCircle' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'inlineStyles'. Either include it or remove the dependency array.", ["1010"], "'showVideoIndices' is assigned a value but never used.", "'replyingTo' is assigned a value but never used.", "'showComments' is assigned a value but never used.", "'setShowComments' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'videoCache'. Either include it or remove the dependency array.", ["1011"], "'handleHideVideo' is assigned a value but never used.", "'toggleVideoExpansion' is assigned a value but never used.", "'handleClearAll' is assigned a value but never used.", "'loadAllVideoComments' is assigned a value but never used.", "'handleAddReply' is assigned a value but never used.", "'handleEditComment' is assigned a value but never used.", "'handleSaveEditComment' is assigned a value but never used.", "'handleCancelEdit' is assigned a value but never used.", "'renderEmptyState' is assigned a value but never used.", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbDownload' is defined but never used.", "'TbCalendar' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbX' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "'TbBooks' is defined but never used.", ["1012"], "React Hook useEffect has a missing dependency: 'subjectsList'. Either include it or remove the dependency array.", ["1013"], ["1014"], "'Option' is assigned a value but never used.", "'TbAward' is defined but never used.", "'TbChartBar' is defined but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["1015"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1016"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["1017"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["1018"], ["1019"], "'userRanking' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "'subscriptionData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["1020"], ["1021"], "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", ["1022"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["1023"], "'TbRobot' is defined but never used.", "'TbSettings' is defined but never used.", "'dispatch' is assigned a value but never used.", "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["1024", "1025"], ["1026", "1027"], "'TbMinus' is defined but never used.", "'TbMaximize' is defined but never used.", "Duplicate key 'studyMaterials'.", "'Fragment' is defined but never used.", "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'getTimerColor' is assigned a value but never used.", "'showPreview' is assigned a value but never used.", "'setShowPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["1028"], "'showCopyButton' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["1029"], ["1030"], "The ref value 'counterRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'counterRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'xpAwarded' is assigned a value but never used.", "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["1031"], "'useState' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbBook' is defined but never used.", "'TbChartLine' is defined but never used.", "'TbStar' is defined but never used.", "React Hook useEffect has a missing dependency: 'setIsOpen'. Either include it or remove the dependency array. If 'setIsOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1032"], "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", {"desc": "1033", "fix": "1034"}, {"desc": "1035", "fix": "1036"}, {"desc": "1037", "fix": "1038"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"kind": "1045", "justification": "1046"}, {"desc": "1047", "fix": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"desc": "1053", "fix": "1054"}, {"desc": "1055", "fix": "1056"}, {"desc": "1057", "fix": "1058"}, {"desc": "1059", "fix": "1060"}, {"desc": "1061", "fix": "1062"}, {"desc": "1063", "fix": "1064"}, {"desc": "1065", "fix": "1066"}, {"desc": "1067", "fix": "1068"}, {"desc": "1069", "fix": "1070"}, {"desc": "1071", "fix": "1072"}, {"desc": "1073", "fix": "1074"}, {"desc": "1075", "fix": "1076"}, {"desc": "1077", "fix": "1078"}, {"desc": "1079", "fix": "1080"}, {"desc": "1081", "fix": "1082"}, {"desc": "1079", "fix": "1083"}, {"desc": "1084", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"desc": "1092", "fix": "1093"}, {"desc": "1094", "fix": "1095"}, {"desc": "1096", "fix": "1097"}, {"desc": "1098", "fix": "1099"}, {"desc": "1094", "fix": "1100"}, {"desc": "1094", "fix": "1101"}, {"desc": "1102", "fix": "1103"}, {"messageId": "1104", "fix": "1105", "desc": "1106"}, {"messageId": "1107", "fix": "1108", "desc": "1109"}, {"messageId": "1104", "fix": "1110", "desc": "1106"}, {"messageId": "1107", "fix": "1111", "desc": "1109"}, {"desc": "1112", "fix": "1113"}, {"desc": "1114", "fix": "1115"}, {"kind": "1045", "justification": "1046"}, {"desc": "1116", "fix": "1117"}, {"desc": "1118", "fix": "1119"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1120", "text": "1121"}, "Update the dependencies array to be: [isPaymentPending, activeRoute, navigate, user, subscriptionData?.endDate, dispatch]", {"range": "1122", "text": "1123"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1126", "text": "1127"}, "Update the dependencies array to be: [submitting, quiz, questions, user, startTime, answers, id, navigate, refreshUserData]", {"range": "1128", "text": "1129"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1130", "text": "1131"}, "directive", "", "Update the dependencies array to be: [getExamData, params.id]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [getExamsData]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [dispatch, examCache, user]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [getUserResults, user]", {"range": "1138", "text": "1139"}, "Update the dependencies array to be: [exams, searchTerm, selectedClass, selectedSubject, user.class, user.level]", {"range": "1140", "text": "1141"}, "Update the dependencies array to be: [filteredExams, preloadQuizData]", {"range": "1142", "text": "1143"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1144", "text": "1145"}, "Update the dependencies array to be: [fetchUserDetails]", {"range": "1146", "text": "1147"}, "Update the dependencies array to be: [fetchVideoComments]", {"range": "1148", "text": "1149"}, "Update the dependencies array to be: [fetchVideos]", {"range": "1150", "text": "1151"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1152", "text": "1153"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1154", "text": "1155"}, "Update the dependencies array to be: [getUsersData]", {"range": "1156", "text": "1157"}, "Update the dependencies array to be: [checkCurrentSubscription, fetchPlans]", {"range": "1158", "text": "1159"}, "Update the dependencies array to be: [isSubscriptionExpired, subscriptionData]", {"range": "1160", "text": "1161"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1162", "text": "1163"}, "Update the dependencies array to be: [inlineStyles]", {"range": "1164", "text": "1165"}, "Update the dependencies array to be: [dispatch, selectedLevel, videoCache]", {"range": "1166", "text": "1167"}, {"range": "1168", "text": "1165"}, "Update the dependencies array to be: [userLevel, userLevelLower, user, subjectsList]", {"range": "1169", "text": "1170"}, "Update the dependencies array to be: [user?.level, selectedSubject, subjectsList]", {"range": "1171", "text": "1172"}, "Update the dependencies array to be: [getData]", {"range": "1173", "text": "1174"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1175", "text": "1176"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1177", "text": "1178"}, "Update the dependencies array to be: [getUserData]", {"range": "1179", "text": "1180"}, "Update the dependencies array to be: [currentPage, fetchQuestions, isAdmin, userData]", {"range": "1181", "text": "1182"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1183", "text": "1184"}, {"range": "1185", "text": "1180"}, {"range": "1186", "text": "1180"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1187", "text": "1188"}, "removeEscape", {"range": "1189", "text": "1046"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1190", "text": "1191"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1192", "text": "1046"}, {"range": "1193", "text": "1191"}, "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1194", "text": "1195"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [isOpen, setIsOpen]", {"range": "1200", "text": "1201"}, [4267, 4269], "[dispatch, getUserData, navigate, user]", [7776, 7823], "[isPaymentPending, activeRoute, navigate, user, subscriptionData?.endDate, dispatch]", [10803, 10836], "[dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", [11198, 11217], "[user, activeRoute, verifyPaymentStatus]", [16653, 16704], "[submitting, quiz, questions, user, startTime, answers, id, navigate, refreshUserData]", [1327, 1329], "[fetchUsers]", [3425, 3427], "[getExamData, params.id]", [5318, 5320], "[getExamsData]", [15840, 15856], "[dispatch, examCache, user]", [17527, 17529], "[getUser<PERSON><PERSON><PERSON><PERSON>, user]", [24376, 24440], "[exams, searchTerm, selectedClass, selectedSubject, user.class, user.level]", [25046, 25061], "[filteredExams, preloadQuizData]", [3188, 3190], "[fetchQuestions]", [897, 899], "[fetchUserDetails]", [2402, 2404], "[fetchVideoComments]", [1432, 1434], "[fetchVideos]", [7040, 7069], "[filters, getData, pagination]", [1196, 1198], "[fetchDashboardData]", [5752, 5754], "[getUsersData]", [4951, 4953], "[checkCurrentSubscription, fetchPlans]", [6611, 6629], "[isSubscriptionExpired, subscriptionData]", [34508, 34510], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [4136, 4138], "[inlineStyles]", [11115, 11140], "[dispatch, selectedLevel, videoCache]", [2194, 2196], [3099, 3132], "[userLevel, userLevelLower, user, subjectsList]", [5178, 5208], "[user?.level, selectedSubject, subjectsList]", [4129, 4131], "[getData]", [4184, 4238], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [29020, 29095], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [2872, 2874], "[getUserData]", [3061, 3093], "[currentPage, fetchQuestions, isAdmin, userData]", [4494, 4520], "[getUserStats, rankingData, userDetails]", [6000, 6002], [13965, 13967], [14117, 14130], "[fetchUserRankingData, userDetails]", [8964, 8965], [8964, 8964], "\\", [10475, 10476], [10475, 10475], [2483, 2492], "[fetchMaterials, filters]", [9719, 9745], "[modalIsOpen, documentUrl, renderPDF]", [2852, 2860], "[fetchNotifications, isOpen, notifications.length]", [1694, 1702], "[isOpen, setIsOpen]"]