{"ast": null, "code": "import { DECADE_UNIT_DIFF } from \"../panels/DecadePanel/constant\";\nexport var WEEK_DAY_COUNT = 7;\nexport function isNullEqual(value1, value2) {\n  if (!value1 && !value2) {\n    return true;\n  }\n  if (!value1 || !value2) {\n    return false;\n  }\n  return undefined;\n}\nexport function isSameDecade(generateConfig, decade1, decade2) {\n  var equal = isNullEqual(decade1, decade2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  var num1 = Math.floor(generateConfig.getYear(decade1) / 10);\n  var num2 = Math.floor(generateConfig.getYear(decade2) / 10);\n  return num1 === num2;\n}\nexport function isSameYear(generateConfig, year1, year2) {\n  var equal = isNullEqual(year1, year2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return generateConfig.getYear(year1) === generateConfig.getYear(year2);\n}\nexport function getQuarter(generateConfig, date) {\n  var quota = Math.floor(generateConfig.getMonth(date) / 3);\n  return quota + 1;\n}\nexport function isSameQuarter(generateConfig, quarter1, quarter2) {\n  var equal = isNullEqual(quarter1, quarter2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return isSameYear(generateConfig, quarter1, quarter2) && getQuarter(generateConfig, quarter1) === getQuarter(generateConfig, quarter2);\n}\nexport function isSameMonth(generateConfig, month1, month2) {\n  var equal = isNullEqual(month1, month2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return isSameYear(generateConfig, month1, month2) && generateConfig.getMonth(month1) === generateConfig.getMonth(month2);\n}\nexport function isSameDate(generateConfig, date1, date2) {\n  var equal = isNullEqual(date1, date2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return generateConfig.getYear(date1) === generateConfig.getYear(date2) && generateConfig.getMonth(date1) === generateConfig.getMonth(date2) && generateConfig.getDate(date1) === generateConfig.getDate(date2);\n}\nexport function isSameTime(generateConfig, time1, time2) {\n  var equal = isNullEqual(time1, time2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return generateConfig.getHour(time1) === generateConfig.getHour(time2) && generateConfig.getMinute(time1) === generateConfig.getMinute(time2) && generateConfig.getSecond(time1) === generateConfig.getSecond(time2);\n}\nexport function isSameWeek(generateConfig, locale, date1, date2) {\n  var equal = isNullEqual(date1, date2);\n  if (typeof equal === 'boolean') {\n    return equal;\n  }\n  return isSameYear(generateConfig, date1, date2) && generateConfig.locale.getWeek(locale, date1) === generateConfig.locale.getWeek(locale, date2);\n}\nexport function isEqual(generateConfig, value1, value2) {\n  return isSameDate(generateConfig, value1, value2) && isSameTime(generateConfig, value1, value2);\n}\n\n/** Between in date but not equal of date */\nexport function isInRange(generateConfig, startDate, endDate, current) {\n  if (!startDate || !endDate || !current) {\n    return false;\n  }\n  return !isSameDate(generateConfig, startDate, current) && !isSameDate(generateConfig, endDate, current) && generateConfig.isAfter(current, startDate) && generateConfig.isAfter(endDate, current);\n}\nexport function getWeekStartDate(locale, generateConfig, value) {\n  var weekFirstDay = generateConfig.locale.getWeekFirstDay(locale);\n  var monthStartDate = generateConfig.setDate(value, 1);\n  var startDateWeekDay = generateConfig.getWeekDay(monthStartDate);\n  var alignStartDate = generateConfig.addDate(monthStartDate, weekFirstDay - startDateWeekDay);\n  if (generateConfig.getMonth(alignStartDate) === generateConfig.getMonth(value) && generateConfig.getDate(alignStartDate) > 1) {\n    alignStartDate = generateConfig.addDate(alignStartDate, -7);\n  }\n  return alignStartDate;\n}\nexport function getClosingViewDate(viewDate, picker, generateConfig) {\n  var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 1;\n  switch (picker) {\n    case 'year':\n      return generateConfig.addYear(viewDate, offset * 10);\n    case 'quarter':\n    case 'month':\n      return generateConfig.addYear(viewDate, offset);\n    default:\n      return generateConfig.addMonth(viewDate, offset);\n  }\n}\nexport function formatValue(value, _ref) {\n  var generateConfig = _ref.generateConfig,\n    locale = _ref.locale,\n    format = _ref.format;\n  return typeof format === 'function' ? format(value) : generateConfig.locale.format(locale.locale, value, format);\n}\nexport function parseValue(value, _ref2) {\n  var generateConfig = _ref2.generateConfig,\n    locale = _ref2.locale,\n    formatList = _ref2.formatList;\n  if (!value || typeof formatList[0] === 'function') {\n    return null;\n  }\n  return generateConfig.locale.parse(locale.locale, value, formatList);\n}\n\n// eslint-disable-next-line consistent-return\nexport function getCellDateDisabled(_ref3) {\n  var cellDate = _ref3.cellDate,\n    mode = _ref3.mode,\n    disabledDate = _ref3.disabledDate,\n    generateConfig = _ref3.generateConfig;\n  if (!disabledDate) return false;\n  // Whether cellDate is disabled in range\n  var getDisabledFromRange = function getDisabledFromRange(currentMode, start, end) {\n    var current = start;\n    while (current <= end) {\n      var _date = void 0;\n      switch (currentMode) {\n        case 'date':\n          {\n            _date = generateConfig.setDate(cellDate, current);\n            if (!disabledDate(_date)) {\n              return false;\n            }\n            break;\n          }\n        case 'month':\n          {\n            _date = generateConfig.setMonth(cellDate, current);\n            if (!getCellDateDisabled({\n              cellDate: _date,\n              mode: 'month',\n              generateConfig: generateConfig,\n              disabledDate: disabledDate\n            })) {\n              return false;\n            }\n            break;\n          }\n        case 'year':\n          {\n            _date = generateConfig.setYear(cellDate, current);\n            if (!getCellDateDisabled({\n              cellDate: _date,\n              mode: 'year',\n              generateConfig: generateConfig,\n              disabledDate: disabledDate\n            })) {\n              return false;\n            }\n            break;\n          }\n      }\n      current += 1;\n    }\n    return true;\n  };\n  switch (mode) {\n    case 'date':\n    case 'week':\n      {\n        return disabledDate(cellDate);\n      }\n    case 'month':\n      {\n        var startDate = 1;\n        var endDate = generateConfig.getDate(generateConfig.getEndDate(cellDate));\n        return getDisabledFromRange('date', startDate, endDate);\n      }\n    case 'quarter':\n      {\n        var startMonth = Math.floor(generateConfig.getMonth(cellDate) / 3) * 3;\n        var endMonth = startMonth + 2;\n        return getDisabledFromRange('month', startMonth, endMonth);\n      }\n    case 'year':\n      {\n        return getDisabledFromRange('month', 0, 11);\n      }\n    case 'decade':\n      {\n        var year = generateConfig.getYear(cellDate);\n        var startYear = Math.floor(year / DECADE_UNIT_DIFF) * DECADE_UNIT_DIFF;\n        var endYear = startYear + DECADE_UNIT_DIFF - 1;\n        return getDisabledFromRange('year', startYear, endYear);\n      }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}