{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nexport default function DefaultPanel(props) {\n  var prefixCls = props.prefixCls,\n    current = props.current,\n    total = props.total,\n    title = props.title,\n    description = props.description,\n    onClose = props.onClose,\n    onPrev = props.onPrev,\n    onNext = props.onNext,\n    onFinish = props.onFinish,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className)\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-inner\")\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onClose,\n    \"aria-label\": \"Close\",\n    className: \"\".concat(prefixCls, \"-close\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-close-x\")\n  }, \"\\xD7\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-header\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-description\")\n  }, description), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-sliders\")\n  }, total > 1 ? _toConsumableArray(Array.from({\n    length: total\n  }).keys()).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      key: item,\n      className: index === current ? 'active' : ''\n    });\n  }) : null), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-buttons\")\n  }, current !== 0 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    onClick: onPrev\n  }, \"Prev\") : null, current === total - 1 ? /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-finish-btn\"),\n    onClick: onFinish\n  }, \"Finish\") : /*#__PURE__*/React.createElement(\"button\", {\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    onClick: onNext\n  }, \"Next\")))));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}