{"ast": null, "code": "export function isNotGrey(color) {\n  // eslint-disable-next-line no-useless-escape\n  const match = (color || '').match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\d.]*)?\\)/);\n  if (match && match[1] && match[2] && match[3]) {\n    return !(match[1] === match[2] && match[2] === match[3]);\n  }\n  return true;\n}\nexport function isValidWaveColor(color) {\n  return color && color !== '#fff' && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && color !== 'rgba(255, 255, 255, 1)' && isNotGrey(color) && !/rgba\\((?:\\d*, ){3}0\\)/.test(color) &&\n  // any transparent rgba color\n  color !== 'transparent';\n}\nexport function getTargetWaveColor(node) {\n  const {\n    borderTopColor,\n    borderColor,\n    backgroundColor\n  } = getComputedStyle(node);\n  if (isValidWaveColor(borderTopColor)) {\n    return borderTopColor;\n  }\n  if (isValidWaveColor(borderColor)) {\n    return borderColor;\n  }\n  if (isValidWaveColor(backgroundColor)) {\n    return backgroundColor;\n  }\n  return null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}