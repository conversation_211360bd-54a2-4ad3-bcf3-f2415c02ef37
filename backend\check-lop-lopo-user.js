const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const axios = require('axios');
require('dotenv').config();

/**
 * CHECK AND FIX lop.lopo USER ISSUE
 * 
 * This script will:
 * 1. Find the lop.lopo user
 * 2. Check their subscription status
 * 3. Verify payment status
 * 4. Revoke unauthorized access
 */

async function checkLopLopoUser() {
  try {
    console.log('🔍 CHECKING lop.lopo USER ISSUE');
    console.log('=' .repeat(50));
    console.log('');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Find lop.lopo user
    const lopUser = await User.findOne({ username: 'lop.lopo' });
    
    if (!lopUser) {
      console.log('❌ User lop.lopo not found');
      
      // Check for similar usernames
      const similarUsers = await User.find({
        username: { $regex: 'lop', $options: 'i' }
      });
      
      if (similarUsers.length > 0) {
        console.log('\n🔍 Found similar usernames:');
        similarUsers.forEach(user => {
          console.log(`   - ${user.username} (${user.firstName} ${user.lastName})`);
        });
      }
      
      return;
    }
    
    console.log('👤 Found lop.lopo user:');
    console.log(`   Name: ${lopUser.firstName} ${lopUser.lastName}`);
    console.log(`   Username: ${lopUser.username}`);
    console.log(`   Email: ${lopUser.email}`);
    console.log(`   Phone: ${lopUser.phoneNumber}`);
    console.log(`   Subscription Status: ${lopUser.subscriptionStatus}`);
    console.log(`   Payment Required: ${lopUser.paymentRequired}`);
    console.log(`   Subscription End Date: ${lopUser.subscriptionEndDate}`);
    console.log(`   Created: ${lopUser.createdAt}`);
    console.log('');
    
    // Find all subscriptions for lop.lopo
    const subscriptions = await Subscription.find({ user: lopUser._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📋 Found ${subscriptions.length} subscription(s) for lop.lopo:\n`);
    
    let hasValidPayment = false;
    let shouldRevokeAccess = false;
    let revokeReason = '';
    
    if (subscriptions.length === 0) {
      console.log('❌ NO SUBSCRIPTIONS FOUND');
      if (lopUser.subscriptionStatus === 'active') {
        shouldRevokeAccess = true;
        revokeReason = 'NO_SUBSCRIPTIONS_BUT_ACTIVE_STATUS';
      }
    } else {
      // Check each subscription
      for (let i = 0; i < subscriptions.length; i++) {
        const sub = subscriptions[i];
        const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
        const minutesAgo = Math.floor(timeSinceCreated / (1000 * 60));
        
        console.log(`🔍 Subscription ${i + 1}:`);
        console.log(`   ID: ${sub._id}`);
        console.log(`   Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`   Payment Status: ${sub.paymentStatus}`);
        console.log(`   Subscription Status: ${sub.status}`);
        console.log(`   Start Date: ${sub.startDate}`);
        console.log(`   End Date: ${sub.endDate}`);
        console.log(`   Created: ${minutesAgo} minutes ago`);
        
        // Check payment history
        if (sub.paymentHistory && sub.paymentHistory.length > 0) {
          console.log(`   Payment History:`);
          sub.paymentHistory.forEach((payment, idx) => {
            console.log(`     Payment ${idx + 1}:`);
            console.log(`       Order ID: ${payment.orderId}`);
            console.log(`       Amount: ${payment.amount}`);
            console.log(`       Payment Status: ${payment.paymentStatus}`);
            console.log(`       Payment Date: ${payment.paymentDate}`);
            console.log(`       Reference ID: ${payment.referenceId}`);
          });
          
          // Verify latest payment with ZenoPay
          const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
          if (latestPayment.orderId && latestPayment.orderId.startsWith('ORDER_')) {
            console.log(`   🔍 Verifying with ZenoPay: ${latestPayment.orderId}`);
            const isValidPayment = await verifyPaymentWithZenoPay(latestPayment.orderId);
            
            if (isValidPayment) {
              console.log(`   ✅ PAYMENT VERIFIED - This is legitimate`);
              hasValidPayment = true;
            } else {
              console.log(`   ❌ PAYMENT NOT VERIFIED - This is unauthorized`);
              if (sub.status === 'active' || sub.paymentStatus === 'paid') {
                shouldRevokeAccess = true;
                revokeReason = 'PAYMENT_NOT_CONFIRMED_WITH_ZENOPAY';
              }
            }
          } else {
            console.log(`   ❌ INVALID ORDER ID - This is unauthorized`);
            if (sub.status === 'active' || sub.paymentStatus === 'paid') {
              shouldRevokeAccess = true;
              revokeReason = 'INVALID_ORDER_ID';
            }
          }
        } else {
          console.log(`   ❌ NO PAYMENT HISTORY - This is unauthorized`);
          if (sub.status === 'active' || sub.paymentStatus === 'paid') {
            shouldRevokeAccess = true;
            revokeReason = 'NO_PAYMENT_HISTORY';
          }
        }
        console.log('');
      }
    }
    
    // Take action based on findings
    console.log('📊 ANALYSIS RESULTS:');
    console.log(`   Valid payments found: ${hasValidPayment ? 'YES' : 'NO'}`);
    console.log(`   Should revoke access: ${shouldRevokeAccess ? 'YES' : 'NO'}`);
    if (shouldRevokeAccess) {
      console.log(`   Revoke reason: ${revokeReason}`);
    }
    console.log('');
    
    if (shouldRevokeAccess && !hasValidPayment) {
      console.log('🚫 REVOKING UNAUTHORIZED ACCESS');
      console.log('=' .repeat(40));
      
      // Revoke all unauthorized subscriptions
      for (const sub of subscriptions) {
        if (sub.status === 'active' || sub.paymentStatus === 'paid') {
          console.log(`🚫 Revoking subscription ${sub._id}`);
          
          sub.status = 'expired';
          sub.paymentStatus = 'failed';
          
          // Add revocation note
          if (sub.paymentHistory && sub.paymentHistory.length > 0) {
            const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
            latestPayment.referenceId = `REVOKED_${Date.now()}_${revokeReason}`;
          }
          
          await sub.save();
          console.log(`   ✅ Subscription ${sub._id} revoked`);
        }
      }
      
      // Update user status
      await User.findByIdAndUpdate(lopUser._id, {
        subscriptionStatus: 'free',
        paymentRequired: true,
        subscriptionEndDate: null,
        subscriptionStartDate: null
      });
      
      console.log('✅ User status updated to FREE');
      console.log('✅ Payment required flag set to TRUE');
      console.log('✅ Subscription dates cleared');
      
    } else if (hasValidPayment) {
      console.log('✅ USER HAS VALID PAYMENT - Access maintained');
    } else {
      console.log('ℹ️ No action needed - user already has correct status');
    }
    
    // Final status check
    console.log('\n📋 FINAL STATUS:');
    const updatedUser = await User.findById(lopUser._id);
    console.log(`   User Status: ${updatedUser.subscriptionStatus}`);
    console.log(`   Payment Required: ${updatedUser.paymentRequired}`);
    console.log(`   End Date: ${updatedUser.subscriptionEndDate || 'None'}`);
    
    const finalSubscriptions = await Subscription.find({ user: lopUser._id });
    const activeCount = finalSubscriptions.filter(sub => sub.status === 'active').length;
    const paidCount = finalSubscriptions.filter(sub => sub.paymentStatus === 'paid').length;
    
    console.log(`   Active Subscriptions: ${activeCount}`);
    console.log(`   Paid Subscriptions: ${paidCount}`);
    
    if (activeCount === 0 && paidCount === 0 && updatedUser.subscriptionStatus === 'free') {
      console.log('\n✅ SECURITY ISSUE RESOLVED');
      console.log('   User lop.lopo no longer has unauthorized access');
    } else if (hasValidPayment) {
      console.log('\n✅ LEGITIMATE USER CONFIRMED');
      console.log('   User lop.lopo has valid payment and access');
    } else {
      console.log('\n⚠️ MANUAL REVIEW REQUIRED');
      console.log('   Please verify the final status manually');
    }
    
  } catch (error) {
    console.error('❌ Error during lop.lopo check:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

async function verifyPaymentWithZenoPay(orderId) {
  try {
    const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
    const response = await axios.get(statusUrl, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY,
      },
      timeout: 10000
    });
    
    if (response.data && response.data.data && response.data.data.length > 0) {
      const paymentData = response.data.data[0];
      console.log(`     📊 ZenoPay Status: ${paymentData.payment_status}`);
      return paymentData.payment_status === 'COMPLETED';
    } else {
      console.log(`     ⚠️ No payment data found in ZenoPay`);
      return false;
    }
    
  } catch (error) {
    console.log(`     ❌ Error checking ZenoPay: ${error.message}`);
    return false;
  }
}

// Run the check
checkLopLopoUser();
