{"ast": null, "code": "import * as React from 'react';\nexport default function getExtraFooter(prefixCls, mode, renderExtraFooter) {\n  if (!renderExtraFooter) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer-extra\")\n  }, renderExtraFooter(mode));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}