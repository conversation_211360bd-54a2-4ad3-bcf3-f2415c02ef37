{"ast": null, "code": "function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n  return _wrapNativeSuper(Class);\n}\n\n/* eslint no-console:0 */\nvar formatRegExp = /%[sdj%]/g;\nvar warning = function warning() {}; // don't print warning message when in production env or node runtime\n\nif (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && typeof document !== 'undefined') {\n  warning = function warning(type, errors) {\n    if (typeof console !== 'undefined' && console.warn && typeof ASYNC_VALIDATOR_NO_WARNING === 'undefined') {\n      if (errors.every(function (e) {\n        return typeof e === 'string';\n      })) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\nfunction convertFieldsError(errors) {\n  if (!errors || !errors.length) return null;\n  var fields = {};\n  errors.forEach(function (error) {\n    var field = error.field;\n    fields[field] = fields[field] || [];\n    fields[field].push(error);\n  });\n  return fields;\n}\nfunction format(template) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  var i = 0;\n  var len = args.length;\n  if (typeof template === 'function') {\n    return template.apply(null, args);\n  }\n  if (typeof template === 'string') {\n    var str = template.replace(formatRegExp, function (x) {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return Number(args[i++]);\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    return str;\n  }\n  return template;\n}\nfunction isNativeStringType(type) {\n  return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'date' || type === 'pattern';\n}\nfunction isEmptyValue(value, type) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\nfunction asyncParallelArray(arr, func, callback) {\n  var results = [];\n  var total = 0;\n  var arrLength = arr.length;\n  function count(errors) {\n    results.push.apply(results, errors || []);\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n  arr.forEach(function (a) {\n    func(a, count);\n  });\n}\nfunction asyncSerialArray(arr, func, callback) {\n  var index = 0;\n  var arrLength = arr.length;\n  function next(errors) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    var original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n  next([]);\n}\nfunction flattenObjArr(objArr) {\n  var ret = [];\n  Object.keys(objArr).forEach(function (k) {\n    ret.push.apply(ret, objArr[k] || []);\n  });\n  return ret;\n}\nvar AsyncValidationError = /*#__PURE__*/function (_Error) {\n  _inheritsLoose(AsyncValidationError, _Error);\n  function AsyncValidationError(errors, fields) {\n    var _this;\n    _this = _Error.call(this, 'Async Validation Error') || this;\n    _this.errors = errors;\n    _this.fields = fields;\n    return _this;\n  }\n  return AsyncValidationError;\n}( /*#__PURE__*/_wrapNativeSuper(Error));\nfunction asyncMap(objArr, option, func, callback, source) {\n  if (option.first) {\n    var _pending = new Promise(function (resolve, reject) {\n      var next = function next(errors) {\n        callback(errors);\n        return errors.length ? reject(new AsyncValidationError(errors, convertFieldsError(errors))) : resolve(source);\n      };\n      var flattenArr = flattenObjArr(objArr);\n      asyncSerialArray(flattenArr, func, next);\n    });\n    _pending[\"catch\"](function (e) {\n      return e;\n    });\n    return _pending;\n  }\n  var firstFields = option.firstFields === true ? Object.keys(objArr) : option.firstFields || [];\n  var objArrKeys = Object.keys(objArr);\n  var objArrLength = objArrKeys.length;\n  var total = 0;\n  var results = [];\n  var pending = new Promise(function (resolve, reject) {\n    var next = function next(errors) {\n      results.push.apply(results, errors);\n      total++;\n      if (total === objArrLength) {\n        callback(results);\n        return results.length ? reject(new AsyncValidationError(results, convertFieldsError(results))) : resolve(source);\n      }\n    };\n    if (!objArrKeys.length) {\n      callback(results);\n      resolve(source);\n    }\n    objArrKeys.forEach(function (key) {\n      var arr = objArr[key];\n      if (firstFields.indexOf(key) !== -1) {\n        asyncSerialArray(arr, func, next);\n      } else {\n        asyncParallelArray(arr, func, next);\n      }\n    });\n  });\n  pending[\"catch\"](function (e) {\n    return e;\n  });\n  return pending;\n}\nfunction isErrorObj(obj) {\n  return !!(obj && obj.message !== undefined);\n}\nfunction getValue(value, path) {\n  var v = value;\n  for (var i = 0; i < path.length; i++) {\n    if (v == undefined) {\n      return v;\n    }\n    v = v[path[i]];\n  }\n  return v;\n}\nfunction complementError(rule, source) {\n  return function (oe) {\n    var fieldValue;\n    if (rule.fullFields) {\n      fieldValue = getValue(source, rule.fullFields);\n    } else {\n      fieldValue = source[oe.field || rule.fullField];\n    }\n    if (isErrorObj(oe)) {\n      oe.field = oe.field || rule.fullField;\n      oe.fieldValue = fieldValue;\n      return oe;\n    }\n    return {\n      message: typeof oe === 'function' ? oe() : oe,\n      fieldValue: fieldValue,\n      field: oe.field || rule.fullField\n    };\n  };\n}\nfunction deepMerge(target, source) {\n  if (source) {\n    for (var s in source) {\n      if (source.hasOwnProperty(s)) {\n        var value = source[s];\n        if (typeof value === 'object' && typeof target[s] === 'object') {\n          target[s] = _extends({}, target[s], value);\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}\nvar required$1 = function required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\n\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\n\nvar whitespace = function whitespace(rule, value, source, errors, options) {\n  if (/^\\s+$/.test(value) || value === '') {\n    errors.push(format(options.messages.whitespace, rule.fullField));\n  }\n};\n\n// https://github.com/kevva/url-regex/blob/master/index.js\nvar urlReg;\nvar getUrlRegex = function () {\n  if (urlReg) {\n    return urlReg;\n  }\n  var word = '[a-fA-F\\\\d:]';\n  var b = function b(options) {\n    return options && options.includeBoundaries ? \"(?:(?<=\\\\s|^)(?=\" + word + \")|(?<=\" + word + \")(?=\\\\s|$))\" : '';\n  };\n  var v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n  var v6seg = '[a-fA-F\\\\d]{1,4}';\n  var v6 = (\"\\n(?:\\n(?:\" + v6seg + \":){7}(?:\" + v6seg + \"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\\n(?:\" + v6seg + \":){6}(?:\" + v4 + \"|:\" + v6seg + \"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\\n(?:\" + v6seg + \":){5}(?::\" + v4 + \"|(?::\" + v6seg + \"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\\n(?:\" + v6seg + \":){4}(?:(?::\" + v6seg + \"){0,1}:\" + v4 + \"|(?::\" + v6seg + \"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\\n(?:\" + v6seg + \":){3}(?:(?::\" + v6seg + \"){0,2}:\" + v4 + \"|(?::\" + v6seg + \"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\\n(?:\" + v6seg + \":){2}(?:(?::\" + v6seg + \"){0,3}:\" + v4 + \"|(?::\" + v6seg + \"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\\n(?:\" + v6seg + \":){1}(?:(?::\" + v6seg + \"){0,4}:\" + v4 + \"|(?::\" + v6seg + \"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\\n(?::(?:(?::\" + v6seg + \"){0,5}:\" + v4 + \"|(?::\" + v6seg + \"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\\n\").replace(/\\s*\\/\\/.*$/gm, '').replace(/\\n/g, '').trim(); // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n\n  var v46Exact = new RegExp(\"(?:^\" + v4 + \"$)|(?:^\" + v6 + \"$)\");\n  var v4exact = new RegExp(\"^\" + v4 + \"$\");\n  var v6exact = new RegExp(\"^\" + v6 + \"$\");\n  var ip = function ip(options) {\n    return options && options.exact ? v46Exact : new RegExp(\"(?:\" + b(options) + v4 + b(options) + \")|(?:\" + b(options) + v6 + b(options) + \")\", 'g');\n  };\n  ip.v4 = function (options) {\n    return options && options.exact ? v4exact : new RegExp(\"\" + b(options) + v4 + b(options), 'g');\n  };\n  ip.v6 = function (options) {\n    return options && options.exact ? v6exact : new RegExp(\"\" + b(options) + v6 + b(options), 'g');\n  };\n  var protocol = \"(?:(?:[a-z]+:)?//)\";\n  var auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  var ipv4 = ip.v4().source;\n  var ipv6 = ip.v6().source;\n  var host = \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\";\n  var domain = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\";\n  var tld = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\";\n  var port = '(?::\\\\d{2,5})?';\n  var path = '(?:[/?#][^\\\\s\"]*)?';\n  var regex = \"(?:\" + protocol + \"|www\\\\.)\" + auth + \"(?:localhost|\" + ipv4 + \"|\" + ipv6 + \"|\" + host + domain + tld + \")\" + port + path;\n  urlReg = new RegExp(\"(?:^\" + regex + \"$)\", 'i');\n  return urlReg;\n};\n\n/* eslint max-len:0 */\n\nvar pattern$2 = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  \"float\": function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function' && !isNaN(value.getTime());\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return typeof value === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && value.length <= 320 && !!value.match(pattern$2.email);\n  },\n  url: function url(value) {\n    return typeof value === 'string' && value.length <= 2048 && !!value.match(getUrlRegex());\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern$2.hex);\n  }\n};\nvar type$1 = function type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required$1(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n    } // straight typeof check\n  } else if (ruleType && typeof value !== rule.type) {\n    errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n};\nvar range = function range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number'; // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  } // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n};\nvar ENUM$1 = 'enum';\nvar enumerable$1 = function enumerable(rule, value, source, errors, options) {\n  rule[ENUM$1] = Array.isArray(rule[ENUM$1]) ? rule[ENUM$1] : [];\n  if (rule[ENUM$1].indexOf(value) === -1) {\n    errors.push(format(options.messages[ENUM$1], rule.fullField, rule[ENUM$1].join(', ')));\n  }\n};\nvar pattern$1 = function pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n};\nvar rules = {\n  required: required$1,\n  whitespace: whitespace,\n  type: type$1,\n  range: range,\n  \"enum\": enumerable$1,\n  pattern: pattern$1\n};\nvar string = function string(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nvar method = function method(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar number = function number(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (value === '') {\n      value = undefined;\n    }\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar _boolean = function _boolean(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar regexp = function regexp(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar integer = function integer(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar floatFn = function floatFn(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar array = function array(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if ((value === undefined || value === null) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'array');\n    if (value !== undefined && value !== null) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar object = function object(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar pattern = function pattern(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'string')) {\n      rules.pattern(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar date = function date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field); // console.log('validate on %s value', value);\n\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      var dateObject;\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nvar required = function required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : typeof value;\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\nvar type = function type(rule, value, callback, source, options) {\n  var ruleType = rule.type;\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nvar any = function any(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\nvar validators = {\n  string: string,\n  method: method,\n  number: number,\n  \"boolean\": _boolean,\n  regexp: regexp,\n  integer: integer,\n  \"float\": floatFn,\n  array: array,\n  object: object,\n  \"enum\": enumerable,\n  pattern: pattern,\n  date: date,\n  url: type,\n  hex: type,\n  email: type,\n  required: required,\n  any: any\n};\nfunction newMessages() {\n  return {\n    \"default\": 'Validation error on field %s',\n    required: '%s is required',\n    \"enum\": '%s must be one of %s',\n    whitespace: '%s cannot be empty',\n    date: {\n      format: '%s date %s is invalid for format %s',\n      parse: '%s date could not be parsed, %s is invalid ',\n      invalid: '%s date %s is invalid'\n    },\n    types: {\n      string: '%s is not a %s',\n      method: '%s is not a %s (function)',\n      array: '%s is not an %s',\n      object: '%s is not an %s',\n      number: '%s is not a %s',\n      date: '%s is not a %s',\n      \"boolean\": '%s is not a %s',\n      integer: '%s is not an %s',\n      \"float\": '%s is not a %s',\n      regexp: '%s is not a valid %s',\n      email: '%s is not a valid %s',\n      url: '%s is not a valid %s',\n      hex: '%s is not a valid %s'\n    },\n    string: {\n      len: '%s must be exactly %s characters',\n      min: '%s must be at least %s characters',\n      max: '%s cannot be longer than %s characters',\n      range: '%s must be between %s and %s characters'\n    },\n    number: {\n      len: '%s must equal %s',\n      min: '%s cannot be less than %s',\n      max: '%s cannot be greater than %s',\n      range: '%s must be between %s and %s'\n    },\n    array: {\n      len: '%s must be exactly %s in length',\n      min: '%s cannot be less than %s in length',\n      max: '%s cannot be greater than %s in length',\n      range: '%s must be between %s and %s in length'\n    },\n    pattern: {\n      mismatch: '%s value %s does not match pattern %s'\n    },\n    clone: function clone() {\n      var cloned = JSON.parse(JSON.stringify(this));\n      cloned.clone = this.clone;\n      return cloned;\n    }\n  };\n}\nvar messages = newMessages();\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\n\nvar Schema = /*#__PURE__*/function () {\n  // ========================= Static =========================\n  // ======================== Instance ========================\n  function Schema(descriptor) {\n    this.rules = null;\n    this._messages = messages;\n    this.define(descriptor);\n  }\n  var _proto = Schema.prototype;\n  _proto.define = function define(rules) {\n    var _this = this;\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if (typeof rules !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n    Object.keys(rules).forEach(function (name) {\n      var item = rules[name];\n      _this.rules[name] = Array.isArray(item) ? item : [item];\n    });\n  };\n  _proto.messages = function messages(_messages) {\n    if (_messages) {\n      this._messages = deepMerge(newMessages(), _messages);\n    }\n    return this._messages;\n  };\n  _proto.validate = function validate(source_, o, oc) {\n    var _this2 = this;\n    if (o === void 0) {\n      o = {};\n    }\n    if (oc === void 0) {\n      oc = function oc() {};\n    }\n    var source = source_;\n    var options = o;\n    var callback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback(null, source);\n      }\n      return Promise.resolve(source);\n    }\n    function complete(results) {\n      var errors = [];\n      var fields = {};\n      function add(e) {\n        if (Array.isArray(e)) {\n          var _errors;\n          errors = (_errors = errors).concat.apply(_errors, e);\n        } else {\n          errors.push(e);\n        }\n      }\n      for (var i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        callback(null, source);\n      } else {\n        fields = convertFieldsError(errors);\n        callback(errors, fields);\n      }\n    }\n    if (options.messages) {\n      var messages$1 = this.messages();\n      if (messages$1 === messages) {\n        messages$1 = newMessages();\n      }\n      deepMerge(messages$1, options.messages);\n      options.messages = messages$1;\n    } else {\n      options.messages = this.messages();\n    }\n    var series = {};\n    var keys = options.keys || Object.keys(this.rules);\n    keys.forEach(function (z) {\n      var arr = _this2.rules[z];\n      var value = source[z];\n      arr.forEach(function (r) {\n        var rule = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = _extends({}, source);\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule\n          };\n        } else {\n          rule = _extends({}, rule);\n        } // Fill validator. Skip if nothing need to validate\n\n        rule.validator = _this2.getValidationMethod(rule);\n        if (!rule.validator) {\n          return;\n        }\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = _this2.getType(rule);\n        series[z] = series[z] || [];\n        series[z].push({\n          rule: rule,\n          value: value,\n          source: source,\n          field: z\n        });\n      });\n    });\n    var errorFields = {};\n    return asyncMap(series, options, function (data, doIt) {\n      var rule = data.rule;\n      var deep = (rule.type === 'object' || rule.type === 'array') && (typeof rule.fields === 'object' || typeof rule.defaultField === 'object');\n      deep = deep && (rule.required || !rule.required && data.value);\n      rule.field = data.field;\n      function addFullField(key, schema) {\n        return _extends({}, schema, {\n          fullField: rule.fullField + \".\" + key,\n          fullFields: rule.fullFields ? [].concat(rule.fullFields, [key]) : [key]\n        });\n      }\n      function cb(e) {\n        if (e === void 0) {\n          e = [];\n        }\n        var errorList = Array.isArray(e) ? e : [e];\n        if (!options.suppressWarning && errorList.length) {\n          Schema.warning('async-validator:', errorList);\n        }\n        if (errorList.length && rule.message !== undefined) {\n          errorList = [].concat(rule.message);\n        } // Fill error info\n\n        var filledErrors = errorList.map(complementError(rule, source));\n        if (options.first && filledErrors.length) {\n          errorFields[rule.field] = 1;\n          return doIt(filledErrors);\n        }\n        if (!deep) {\n          doIt(filledErrors);\n        } else {\n          // if rule is required but the target object\n          // does not exist fail at the rule level and don't\n          // go deeper\n          if (rule.required && !data.value) {\n            if (rule.message !== undefined) {\n              filledErrors = [].concat(rule.message).map(complementError(rule, source));\n            } else if (options.error) {\n              filledErrors = [options.error(rule, format(options.messages.required, rule.field))];\n            }\n            return doIt(filledErrors);\n          }\n          var fieldsSchema = {};\n          if (rule.defaultField) {\n            Object.keys(data.value).map(function (key) {\n              fieldsSchema[key] = rule.defaultField;\n            });\n          }\n          fieldsSchema = _extends({}, fieldsSchema, data.rule.fields);\n          var paredFieldsSchema = {};\n          Object.keys(fieldsSchema).forEach(function (field) {\n            var fieldSchema = fieldsSchema[field];\n            var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [fieldSchema];\n            paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));\n          });\n          var schema = new Schema(paredFieldsSchema);\n          schema.messages(options.messages);\n          if (data.rule.options) {\n            data.rule.options.messages = options.messages;\n            data.rule.options.error = options.error;\n          }\n          schema.validate(data.value, data.rule.options || options, function (errs) {\n            var finalErrors = [];\n            if (filledErrors && filledErrors.length) {\n              finalErrors.push.apply(finalErrors, filledErrors);\n            }\n            if (errs && errs.length) {\n              finalErrors.push.apply(finalErrors, errs);\n            }\n            doIt(finalErrors.length ? finalErrors : null);\n          });\n        }\n      }\n      var res;\n      if (rule.asyncValidator) {\n        res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n      } else if (rule.validator) {\n        try {\n          res = rule.validator(rule, data.value, cb, data.source, options);\n        } catch (error) {\n          console.error == null ? void 0 : console.error(error); // rethrow to report error\n\n          if (!options.suppressValidatorError) {\n            setTimeout(function () {\n              throw error;\n            }, 0);\n          }\n          cb(error.message);\n        }\n        if (res === true) {\n          cb();\n        } else if (res === false) {\n          cb(typeof rule.message === 'function' ? rule.message(rule.fullField || rule.field) : rule.message || (rule.fullField || rule.field) + \" fails\");\n        } else if (res instanceof Array) {\n          cb(res);\n        } else if (res instanceof Error) {\n          cb(res.message);\n        }\n      }\n      if (res && res.then) {\n        res.then(function () {\n          return cb();\n        }, function (e) {\n          return cb(e);\n        });\n      }\n    }, function (results) {\n      complete(results);\n    }, source);\n  };\n  _proto.getType = function getType(rule) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  };\n  _proto.getValidationMethod = function getValidationMethod(rule) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    var keys = Object.keys(rule);\n    var messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || undefined;\n  };\n  return Schema;\n}();\nSchema.register = function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n};\nSchema.warning = warning;\nSchema.messages = messages;\nSchema.validators = validators;\nexport { Schema as default };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}