{"ast": null, "code": "import classNames from 'classnames';\nimport React, { useEffect, useState } from 'react';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = _ref => {\n  let {\n    prefixCls,\n    min = 0,\n    max = 100,\n    value,\n    onChange,\n    className,\n    formatter\n  } = _ref;\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [stepValue, setStepValue] = useState(value);\n  // Update step value\n  useEffect(() => {\n    if (!Number.isNaN(value)) {\n      setStepValue(value);\n    }\n  }, [value]);\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      if (!value) {\n        setStepValue(step || 0);\n      }\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}