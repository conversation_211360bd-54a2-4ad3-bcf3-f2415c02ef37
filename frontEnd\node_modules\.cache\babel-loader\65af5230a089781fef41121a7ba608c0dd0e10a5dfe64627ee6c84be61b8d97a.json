{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { isInRange } from \"../utils/dateUtil\";\nimport { getValue } from \"../utils/miscUtil\";\nexport default function useCellClassName(_ref) {\n  var cellPrefixCls = _ref.cellPrefixCls,\n    generateConfig = _ref.generateConfig,\n    rangedValue = _ref.rangedValue,\n    hoverRangedValue = _ref.hoverRangedValue,\n    isInView = _ref.isInView,\n    isSameCell = _ref.isSameCell,\n    offsetCell = _ref.offsetCell,\n    today = _ref.today,\n    value = _ref.value;\n  function getClassName(currentDate) {\n    var _ref2;\n    var prevDate = offsetCell(currentDate, -1);\n    var nextDate = offsetCell(currentDate, 1);\n    var rangeStart = getValue(rangedValue, 0);\n    var rangeEnd = getValue(rangedValue, 1);\n    var hoverStart = getValue(hoverRangedValue, 0);\n    var hoverEnd = getValue(hoverRangedValue, 1);\n    var isRangeHovered = isInRange(generateConfig, hoverStart, hoverEnd, currentDate);\n    function isRangeStart(date) {\n      return isSameCell(rangeStart, date);\n    }\n    function isRangeEnd(date) {\n      return isSameCell(rangeEnd, date);\n    }\n    var isHoverStart = isSameCell(hoverStart, currentDate);\n    var isHoverEnd = isSameCell(hoverEnd, currentDate);\n    var isHoverEdgeStart = (isRangeHovered || isHoverEnd) && (!isInView(prevDate) || isRangeEnd(prevDate));\n    var isHoverEdgeEnd = (isRangeHovered || isHoverStart) && (!isInView(nextDate) || isRangeStart(nextDate));\n    return _ref2 = {}, _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-in-view\"), isInView(currentDate)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-in-range\"), isInRange(generateConfig, rangeStart, rangeEnd, currentDate)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-start\"), isRangeStart(currentDate)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-end\"), isRangeEnd(currentDate)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-start-single\"), isRangeStart(currentDate) && !rangeEnd), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-end-single\"), isRangeEnd(currentDate) && !rangeStart), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-start-near-hover\"), isRangeStart(currentDate) && (isSameCell(prevDate, hoverStart) || isInRange(generateConfig, hoverStart, hoverEnd, prevDate))), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-end-near-hover\"), isRangeEnd(currentDate) && (isSameCell(nextDate, hoverEnd) || isInRange(generateConfig, hoverStart, hoverEnd, nextDate))), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover\"), isRangeHovered), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-start\"), isHoverStart), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-end\"), isHoverEnd), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-edge-start\"), isHoverEdgeStart), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-edge-end\"), isHoverEdgeEnd), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-edge-start-near-range\"), isHoverEdgeStart && isSameCell(prevDate, rangeEnd)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-range-hover-edge-end-near-range\"), isHoverEdgeEnd && isSameCell(nextDate, rangeStart)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-today\"), isSameCell(today, currentDate)), _defineProperty(_ref2, \"\".concat(cellPrefixCls, \"-selected\"), isSameCell(value, currentDate)), _ref2;\n  }\n  return getClassName;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}