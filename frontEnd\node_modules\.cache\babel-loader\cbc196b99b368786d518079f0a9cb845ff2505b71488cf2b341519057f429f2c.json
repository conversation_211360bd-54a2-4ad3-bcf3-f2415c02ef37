{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport * as React from 'react';\nimport useTimeSelection from \"../../hooks/useTimeSelection\";\nimport { leftPad } from \"../../utils/miscUtil\";\nimport TimeUnitColumn from \"./TimeUnitColumn\";\nfunction shouldUnitsUpdate(prevUnits, nextUnits) {\n  if (prevUnits.length !== nextUnits.length) return true;\n  // if any unit's disabled status is different, the units should be re-evaluted\n  for (var i = 0; i < prevUnits.length; i += 1) {\n    if (prevUnits[i].disabled !== nextUnits[i].disabled) return true;\n  }\n  return false;\n}\nfunction generateUnits(start, end, step, disabledUnits) {\n  var units = [];\n  var integerStep = step >= 1 ? step | 0 : 1;\n  for (var i = start; i <= end; i += integerStep) {\n    units.push({\n      label: leftPad(i, 2),\n      value: i,\n      disabled: (disabledUnits || []).includes(i)\n    });\n  }\n  return units;\n}\nfunction TimeBody(props) {\n  var generateConfig = props.generateConfig,\n    prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    activeColumnIndex = props.activeColumnIndex,\n    value = props.value,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    use12Hours = props.use12Hours,\n    _props$hourStep = props.hourStep,\n    hourStep = _props$hourStep === void 0 ? 1 : _props$hourStep,\n    _props$minuteStep = props.minuteStep,\n    minuteStep = _props$minuteStep === void 0 ? 1 : _props$minuteStep,\n    _props$secondStep = props.secondStep,\n    secondStep = _props$secondStep === void 0 ? 1 : _props$secondStep,\n    disabledHours = props.disabledHours,\n    disabledMinutes = props.disabledMinutes,\n    disabledSeconds = props.disabledSeconds,\n    disabledTime = props.disabledTime,\n    hideDisabledOptions = props.hideDisabledOptions,\n    onSelect = props.onSelect,\n    cellRender = props.cellRender,\n    locale = props.locale;\n\n  // Misc\n  var columns = [];\n  var contentPrefixCls = \"\".concat(prefixCls, \"-content\");\n  var columnPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var isPM;\n  var originHour = value ? generateConfig.getHour(value) : -1;\n  var hour = originHour;\n  var minute = value ? generateConfig.getMinute(value) : -1;\n  var second = value ? generateConfig.getSecond(value) : -1;\n\n  // Disabled Time\n  var now = generateConfig.getNow();\n  var _React$useMemo = React.useMemo(function () {\n      if (disabledTime) {\n        var disabledConfig = disabledTime(now);\n        return [disabledConfig.disabledHours, disabledConfig.disabledMinutes, disabledConfig.disabledSeconds];\n      }\n      return [disabledHours, disabledMinutes, disabledSeconds];\n    }, [disabledHours, disabledMinutes, disabledSeconds, disabledTime, now]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 3),\n    mergedDisabledHours = _React$useMemo2[0],\n    mergedDisabledMinutes = _React$useMemo2[1],\n    mergedDisabledSeconds = _React$useMemo2[2];\n\n  // ========================= Unit =========================\n  var rawHours = generateUnits(0, 23, hourStep, mergedDisabledHours && mergedDisabledHours());\n  var memorizedRawHours = useMemo(function () {\n    return rawHours;\n  }, rawHours, shouldUnitsUpdate);\n\n  // Should additional logic to handle 12 hours\n  if (use12Hours) {\n    isPM = hour >= 12; // -1 means should display AM\n    hour %= 12;\n  }\n  var _React$useMemo3 = React.useMemo(function () {\n      if (!use12Hours) {\n        return [false, false];\n      }\n      var AMPMDisabled = [true, true];\n      memorizedRawHours.forEach(function (_ref) {\n        var disabled = _ref.disabled,\n          hourValue = _ref.value;\n        if (disabled) return;\n        if (hourValue >= 12) {\n          AMPMDisabled[1] = false;\n        } else {\n          AMPMDisabled[0] = false;\n        }\n      });\n      return AMPMDisabled;\n    }, [use12Hours, memorizedRawHours]),\n    _React$useMemo4 = _slicedToArray(_React$useMemo3, 2),\n    AMDisabled = _React$useMemo4[0],\n    PMDisabled = _React$useMemo4[1];\n  var hours = React.useMemo(function () {\n    if (!use12Hours) return memorizedRawHours;\n    return memorizedRawHours.filter(isPM ? function (hourMeta) {\n      return hourMeta.value >= 12;\n    } : function (hourMeta) {\n      return hourMeta.value < 12;\n    }).map(function (hourMeta) {\n      var hourValue = hourMeta.value % 12;\n      var hourLabel = hourValue === 0 ? '12' : leftPad(hourValue, 2);\n      return _objectSpread(_objectSpread({}, hourMeta), {}, {\n        label: hourLabel,\n        value: hourValue\n      });\n    });\n  }, [use12Hours, isPM, memorizedRawHours]);\n  var minutes = generateUnits(0, 59, minuteStep, mergedDisabledMinutes && mergedDisabledMinutes(originHour));\n  var seconds = generateUnits(0, 59, secondStep, mergedDisabledSeconds && mergedDisabledSeconds(originHour, minute));\n\n  // Set Time\n  var setTime = useTimeSelection({\n    value: value,\n    generateConfig: generateConfig,\n    disabledMinutes: mergedDisabledMinutes,\n    disabledSeconds: mergedDisabledSeconds,\n    minutes: minutes,\n    seconds: seconds,\n    use12Hours: use12Hours\n  });\n\n  // ====================== Operations ======================\n  operationRef.current = {\n    onUpDown: function onUpDown(diff) {\n      var column = columns[activeColumnIndex];\n      if (column) {\n        var valueIndex = column.units.findIndex(function (unit) {\n          return unit.value === column.value;\n        });\n        var unitLen = column.units.length;\n        for (var i = 1; i < unitLen; i += 1) {\n          var nextUnit = column.units[(valueIndex + diff * i + unitLen) % unitLen];\n          if (nextUnit.disabled !== true) {\n            column.onSelect(nextUnit.value);\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  // ======================== Render ========================\n  function addColumnNode(condition, node, columnValue, units, onColumnSelect) {\n    if (condition !== false) {\n      columns.push({\n        node: /*#__PURE__*/React.cloneElement(node, {\n          prefixCls: columnPrefixCls,\n          value: columnValue,\n          active: activeColumnIndex === columns.length,\n          onSelect: onColumnSelect,\n          units: units,\n          hideDisabledOptions: hideDisabledOptions\n        }),\n        onSelect: onColumnSelect,\n        value: columnValue,\n        units: units\n      });\n    }\n  }\n\n  // Hour\n  addColumnNode(showHour, /*#__PURE__*/React.createElement(TimeUnitColumn, {\n    key: \"hour\",\n    type: \"hour\",\n    info: {\n      today: now,\n      locale: locale,\n      cellRender: cellRender\n    }\n  }), hour, hours, function (num) {\n    onSelect(setTime(isPM, num, minute, second), 'mouse');\n  });\n\n  // Minute\n  addColumnNode(showMinute, /*#__PURE__*/React.createElement(TimeUnitColumn, {\n    key: \"minute\",\n    type: \"minute\",\n    info: {\n      today: now,\n      locale: locale,\n      cellRender: cellRender\n    }\n  }), minute, minutes, function (num) {\n    onSelect(setTime(isPM, hour, num, second), 'mouse');\n  });\n\n  // Second\n  addColumnNode(showSecond, /*#__PURE__*/React.createElement(TimeUnitColumn, {\n    key: \"second\",\n    type: \"second\",\n    info: {\n      today: now,\n      locale: locale,\n      cellRender: cellRender\n    }\n  }), second, seconds, function (num) {\n    onSelect(setTime(isPM, hour, minute, num), 'mouse');\n  });\n\n  // 12 Hours\n  var PMIndex = -1;\n  if (typeof isPM === 'boolean') {\n    PMIndex = isPM ? 1 : 0;\n  }\n  addColumnNode(use12Hours === true, /*#__PURE__*/React.createElement(TimeUnitColumn, {\n    key: \"meridiem\",\n    type: \"meridiem\",\n    info: {\n      today: now,\n      locale: locale,\n      cellRender: cellRender\n    }\n  }), PMIndex, [{\n    label: 'AM',\n    value: 0,\n    disabled: AMDisabled\n  }, {\n    label: 'PM',\n    value: 1,\n    disabled: PMDisabled\n  }], function (num) {\n    onSelect(setTime(!!num, hour, minute, second), 'mouse');\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: contentPrefixCls\n  }, columns.map(function (_ref2) {\n    var node = _ref2.node;\n    return node;\n  }));\n}\nexport default TimeBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}