{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nexport default (function (rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var checkedKeys = rawLabeledValues.map(function (_ref) {\n      var value = _ref.value;\n      return value;\n    });\n    var halfCheckedKeys = rawHalfCheckedValues.map(function (_ref2) {\n      var value = _ref2.value;\n      return value;\n    });\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    if (treeConduction) {\n      var _conductCheck = conductCheck(checkedKeys, true, keyEntities);\n      checkedKeys = _conductCheck.checkedKeys;\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n    }\n    return [\n    // Checked keys should fill with missing keys which should de-duplicated\n    Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(checkedKeys)))),\n    // Half checked keys\n    halfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}