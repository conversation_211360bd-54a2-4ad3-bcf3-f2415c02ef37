{"ast": null, "code": "const getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    horizontalLineHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    itemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: horizontalLineHeight,\n      border: 0,\n      borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: itemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}