{"ast": null, "code": "import * as React from 'react';\nimport classNames from 'classnames';\nvar TabPane = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    style = _ref.style,\n    id = _ref.id,\n    active = _ref.active,\n    tabKey = _ref.tabKey,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id && \"\".concat(id, \"-panel-\").concat(tabKey),\n    role: \"tabpanel\",\n    tabIndex: active ? 0 : -1,\n    \"aria-labelledby\": id && \"\".concat(id, \"-tab-\").concat(tabKey),\n    \"aria-hidden\": !active,\n    style: style,\n    className: classNames(prefixCls, active && \"\".concat(prefixCls, \"-active\"), className),\n    ref: ref\n  }, children);\n});\nif (process.env.NODE_ENV !== 'production') {\n  TabPane.displayName = 'TabPane';\n}\nexport default TabPane;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}