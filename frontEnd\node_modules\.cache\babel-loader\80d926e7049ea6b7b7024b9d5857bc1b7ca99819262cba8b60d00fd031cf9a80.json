{"ast": null, "code": "import React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  if (isDOM(node)) {\n    return node;\n  }\n  if (node instanceof React.Component) {\n    return ReactDOM.findDOMNode(node);\n  }\n  return null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}