{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React, { cloneElement, isValidElement } from 'react';\nimport KEYCODE from './KeyCode';\nimport LOCALE from './locale/zh_CN';\nimport Options from './Options';\nimport Pager from './Pager';\nfunction noop() {}\nfunction isInteger(v) {\n  var value = Number(v);\n  return (\n    // eslint-disable-next-line no-restricted-globals\n    typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value\n  );\n}\nvar defaultItemRender = function defaultItemRender(page, type, element) {\n  return element;\n};\nfunction calculatePage(p, state, props) {\n  var pageSize = typeof p === 'undefined' ? state.pageSize : p;\n  return Math.floor((props.total - 1) / pageSize) + 1;\n}\nvar Pagination = /*#__PURE__*/function (_React$Component) {\n  _inherits(Pagination, _React$Component);\n  var _super = _createSuper(Pagination);\n  function Pagination(props) {\n    var _this;\n    _classCallCheck(this, Pagination);\n    _this = _super.call(this, props);\n    _this.paginationNode = /*#__PURE__*/React.createRef();\n    _this.getJumpPrevPage = function () {\n      return Math.max(1, _this.state.current - (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getJumpNextPage = function () {\n      return Math.min(calculatePage(undefined, _this.state, _this.props), _this.state.current + (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getItemIcon = function (icon, label) {\n      var prefixCls = _this.props.prefixCls;\n      var iconNode = icon || /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        \"aria-label\": label,\n        className: \"\".concat(prefixCls, \"-item-link\")\n      });\n      if (typeof icon === 'function') {\n        iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, _this.props));\n      }\n      return iconNode;\n    };\n    _this.isValid = function (page) {\n      var total = _this.props.total;\n      return isInteger(page) && page !== _this.state.current && isInteger(total) && total > 0;\n    };\n    _this.shouldDisplayQuickJumper = function () {\n      var _this$props = _this.props,\n        showQuickJumper = _this$props.showQuickJumper,\n        total = _this$props.total;\n      var pageSize = _this.state.pageSize;\n      if (total <= pageSize) {\n        return false;\n      }\n      return showQuickJumper;\n    };\n    _this.handleKeyDown = function (e) {\n      if (e.keyCode === KEYCODE.ARROW_UP || e.keyCode === KEYCODE.ARROW_DOWN) {\n        e.preventDefault();\n      }\n    };\n    _this.handleKeyUp = function (e) {\n      var value = _this.getValidValue(e);\n      var currentInputValue = _this.state.currentInputValue;\n      if (value !== currentInputValue) {\n        _this.setState({\n          currentInputValue: value\n        });\n      }\n      if (e.keyCode === KEYCODE.ENTER) {\n        _this.handleChange(value);\n      } else if (e.keyCode === KEYCODE.ARROW_UP) {\n        _this.handleChange(value - 1);\n      } else if (e.keyCode === KEYCODE.ARROW_DOWN) {\n        _this.handleChange(value + 1);\n      }\n    };\n    _this.handleBlur = function (e) {\n      var value = _this.getValidValue(e);\n      _this.handleChange(value);\n    };\n    _this.changePageSize = function (size) {\n      var current = _this.state.current;\n      var newCurrent = calculatePage(size, _this.state, _this.props);\n      current = current > newCurrent ? newCurrent : current;\n      // fix the issue:\n      // Once 'total' is 0, 'current' in 'onShowSizeChange' is 0, which is not correct.\n      if (newCurrent === 0) {\n        // eslint-disable-next-line prefer-destructuring\n        current = _this.state.current;\n      }\n      if (typeof size === 'number') {\n        if (!('pageSize' in _this.props)) {\n          _this.setState({\n            pageSize: size\n          });\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: current,\n            currentInputValue: current\n          });\n        }\n      }\n      _this.props.onShowSizeChange(current, size);\n      if ('onChange' in _this.props && _this.props.onChange) {\n        _this.props.onChange(current, size);\n      }\n    };\n    _this.handleChange = function (page) {\n      var _this$props2 = _this.props,\n        disabled = _this$props2.disabled,\n        onChange = _this$props2.onChange;\n      var _this$state = _this.state,\n        pageSize = _this$state.pageSize,\n        current = _this$state.current,\n        currentInputValue = _this$state.currentInputValue;\n      if (_this.isValid(page) && !disabled) {\n        var currentPage = calculatePage(undefined, _this.state, _this.props);\n        var newPage = page;\n        if (page > currentPage) {\n          newPage = currentPage;\n        } else if (page < 1) {\n          newPage = 1;\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: newPage\n          });\n        }\n        if (newPage !== currentInputValue) {\n          _this.setState({\n            currentInputValue: newPage\n          });\n        }\n        onChange(newPage, pageSize);\n        return newPage;\n      }\n      return current;\n    };\n    _this.prev = function () {\n      if (_this.hasPrev()) {\n        _this.handleChange(_this.state.current - 1);\n      }\n    };\n    _this.next = function () {\n      if (_this.hasNext()) {\n        _this.handleChange(_this.state.current + 1);\n      }\n    };\n    _this.jumpPrev = function () {\n      _this.handleChange(_this.getJumpPrevPage());\n    };\n    _this.jumpNext = function () {\n      _this.handleChange(_this.getJumpNextPage());\n    };\n    _this.hasPrev = function () {\n      return _this.state.current > 1;\n    };\n    _this.hasNext = function () {\n      return _this.state.current < calculatePage(undefined, _this.state, _this.props);\n    };\n    _this.runIfEnter = function (event, callback) {\n      if (event.key === 'Enter' || event.charCode === 13) {\n        for (var _len = arguments.length, restParams = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n          restParams[_key - 2] = arguments[_key];\n        }\n        callback.apply(void 0, restParams);\n      }\n    };\n    _this.runIfEnterPrev = function (e) {\n      _this.runIfEnter(e, _this.prev);\n    };\n    _this.runIfEnterNext = function (e) {\n      _this.runIfEnter(e, _this.next);\n    };\n    _this.runIfEnterJumpPrev = function (e) {\n      _this.runIfEnter(e, _this.jumpPrev);\n    };\n    _this.runIfEnterJumpNext = function (e) {\n      _this.runIfEnter(e, _this.jumpNext);\n    };\n    _this.handleGoTO = function (e) {\n      if (e.keyCode === KEYCODE.ENTER || e.type === 'click') {\n        _this.handleChange(_this.state.currentInputValue);\n      }\n    };\n    _this.renderPrev = function (prevPage) {\n      var _this$props3 = _this.props,\n        prevIcon = _this$props3.prevIcon,\n        itemRender = _this$props3.itemRender;\n      var prevButton = itemRender(prevPage, 'prev', _this.getItemIcon(prevIcon, 'prev page'));\n      var disabled = !_this.hasPrev();\n      return /*#__PURE__*/isValidElement(prevButton) ? /*#__PURE__*/cloneElement(prevButton, {\n        disabled: disabled\n      }) : prevButton;\n    };\n    _this.renderNext = function (nextPage) {\n      var _this$props4 = _this.props,\n        nextIcon = _this$props4.nextIcon,\n        itemRender = _this$props4.itemRender;\n      var nextButton = itemRender(nextPage, 'next', _this.getItemIcon(nextIcon, 'next page'));\n      var disabled = !_this.hasNext();\n      return /*#__PURE__*/isValidElement(nextButton) ? /*#__PURE__*/cloneElement(nextButton, {\n        disabled: disabled\n      }) : nextButton;\n    };\n    var hasOnChange = props.onChange !== noop;\n    var hasCurrent = ('current' in props);\n    if (hasCurrent && !hasOnChange) {\n      // eslint-disable-next-line no-console\n      console.warn('Warning: You provided a `current` prop to a Pagination component without an `onChange` handler. This will render a read-only component.');\n    }\n    var _current = props.defaultCurrent;\n    if ('current' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _current = props.current;\n    }\n    var _pageSize = props.defaultPageSize;\n    if ('pageSize' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _pageSize = props.pageSize;\n    }\n    _current = Math.min(_current, calculatePage(_pageSize, undefined, props));\n    _this.state = {\n      current: _current,\n      currentInputValue: _current,\n      pageSize: _pageSize\n    };\n    return _this;\n  }\n  _createClass(Pagination, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(_, prevState) {\n      // When current page change, fix focused style of prev item\n      // A hacky solution of https://github.com/ant-design/ant-design/issues/8948\n      var prefixCls = this.props.prefixCls;\n      if (prevState.current !== this.state.current && this.paginationNode.current) {\n        var lastCurrentNode = this.paginationNode.current.querySelector(\".\".concat(prefixCls, \"-item-\").concat(prevState.current));\n        if (lastCurrentNode && document.activeElement === lastCurrentNode) {\n          var _lastCurrentNode$blur;\n          lastCurrentNode === null || lastCurrentNode === void 0 ? void 0 : (_lastCurrentNode$blur = lastCurrentNode.blur) === null || _lastCurrentNode$blur === void 0 ? void 0 : _lastCurrentNode$blur.call(lastCurrentNode);\n        }\n      }\n    }\n  }, {\n    key: \"getValidValue\",\n    value: function getValidValue(e) {\n      var inputValue = e.target.value;\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var currentInputValue = this.state.currentInputValue;\n      var value;\n      if (inputValue === '') {\n        value = inputValue;\n        // eslint-disable-next-line no-restricted-globals\n      } else if (Number.isNaN(Number(inputValue))) {\n        value = currentInputValue;\n      } else if (inputValue >= allPages) {\n        value = allPages;\n      } else {\n        value = Number(inputValue);\n      }\n      return value;\n    }\n  }, {\n    key: \"getShowSizeChanger\",\n    value: function getShowSizeChanger() {\n      var _this$props5 = this.props,\n        showSizeChanger = _this$props5.showSizeChanger,\n        total = _this$props5.total,\n        totalBoundaryShowSizeChanger = _this$props5.totalBoundaryShowSizeChanger;\n      if (typeof showSizeChanger !== 'undefined') {\n        return showSizeChanger;\n      }\n      return total > totalBoundaryShowSizeChanger;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        prefixCls = _this$props6.prefixCls,\n        className = _this$props6.className,\n        style = _this$props6.style,\n        disabled = _this$props6.disabled,\n        hideOnSinglePage = _this$props6.hideOnSinglePage,\n        total = _this$props6.total,\n        locale = _this$props6.locale,\n        showQuickJumper = _this$props6.showQuickJumper,\n        showLessItems = _this$props6.showLessItems,\n        showTitle = _this$props6.showTitle,\n        showTotal = _this$props6.showTotal,\n        simple = _this$props6.simple,\n        itemRender = _this$props6.itemRender,\n        showPrevNextJumpers = _this$props6.showPrevNextJumpers,\n        jumpPrevIcon = _this$props6.jumpPrevIcon,\n        jumpNextIcon = _this$props6.jumpNextIcon,\n        selectComponentClass = _this$props6.selectComponentClass,\n        selectPrefixCls = _this$props6.selectPrefixCls,\n        pageSizeOptions = _this$props6.pageSizeOptions;\n      var _this$state2 = this.state,\n        current = _this$state2.current,\n        pageSize = _this$state2.pageSize,\n        currentInputValue = _this$state2.currentInputValue;\n      // When hideOnSinglePage is true and there is only 1 page, hide the pager\n      if (hideOnSinglePage === true && total <= pageSize) {\n        return null;\n      }\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var pagerList = [];\n      var jumpPrev = null;\n      var jumpNext = null;\n      var firstPager = null;\n      var lastPager = null;\n      var gotoButton = null;\n      var goButton = showQuickJumper && showQuickJumper.goButton;\n      var pageBufferSize = showLessItems ? 1 : 2;\n      var prevPage = current - 1 > 0 ? current - 1 : 0;\n      var nextPage = current + 1 < allPages ? current + 1 : allPages;\n      var dataOrAriaAttributeProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n      var totalText = showTotal && /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-total-text\")\n      }, showTotal(total, [total === 0 ? 0 : (current - 1) * pageSize + 1, current * pageSize > total ? total : current * pageSize]));\n      if (simple) {\n        if (goButton) {\n          if (typeof goButton === 'boolean') {\n            gotoButton = /*#__PURE__*/React.createElement(\"button\", {\n              type: \"button\",\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, locale.jump_to_confirm);\n          } else {\n            gotoButton = /*#__PURE__*/React.createElement(\"span\", {\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, goButton);\n          }\n          gotoButton = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? \"\".concat(locale.jump_to).concat(current, \"/\").concat(allPages) : null,\n            className: \"\".concat(prefixCls, \"-simple-pager\")\n          }, gotoButton);\n        }\n        return /*#__PURE__*/React.createElement(\"ul\", _extends({\n          className: classNames(prefixCls, \"\".concat(prefixCls, \"-simple\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), className),\n          style: style,\n          ref: this.paginationNode\n        }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.prev_page : null,\n          onClick: this.prev,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterPrev,\n          className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasPrev())),\n          \"aria-disabled\": !this.hasPrev()\n        }, this.renderPrev(prevPage)), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? \"\".concat(current, \"/\").concat(allPages) : null,\n          className: \"\".concat(prefixCls, \"-simple-pager\")\n        }, /*#__PURE__*/React.createElement(\"input\", {\n          type: \"text\",\n          value: currentInputValue,\n          disabled: disabled,\n          onKeyDown: this.handleKeyDown,\n          onKeyUp: this.handleKeyUp,\n          onChange: this.handleKeyUp,\n          onBlur: this.handleBlur,\n          size: 3\n        }), /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-slash\")\n        }, \"/\"), allPages), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.next_page : null,\n          onClick: this.next,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterNext,\n          className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasNext())),\n          \"aria-disabled\": !this.hasNext()\n        }, this.renderNext(nextPage)), gotoButton);\n      }\n      if (allPages <= 3 + pageBufferSize * 2) {\n        var pagerProps = {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          showTitle: showTitle,\n          itemRender: itemRender\n        };\n        if (!allPages) {\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: \"noPager\",\n            page: 1,\n            className: \"\".concat(prefixCls, \"-item-disabled\")\n          })));\n        }\n        for (var i = 1; i <= allPages; i += 1) {\n          var active = current === i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: i,\n            page: i,\n            active: active\n          })));\n        }\n      } else {\n        var prevItemTitle = showLessItems ? locale.prev_3 : locale.prev_5;\n        var nextItemTitle = showLessItems ? locale.next_3 : locale.next_5;\n        if (showPrevNextJumpers) {\n          jumpPrev = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? prevItemTitle : null,\n            key: \"prev\",\n            onClick: this.jumpPrev,\n            tabIndex: 0,\n            onKeyPress: this.runIfEnterJumpPrev,\n            className: classNames(\"\".concat(prefixCls, \"-jump-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-prev-custom-icon\"), !!jumpPrevIcon))\n          }, itemRender(this.getJumpPrevPage(), 'jump-prev', this.getItemIcon(jumpPrevIcon, 'prev page')));\n          jumpNext = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? nextItemTitle : null,\n            key: \"next\",\n            tabIndex: 0,\n            onClick: this.jumpNext,\n            onKeyPress: this.runIfEnterJumpNext,\n            className: classNames(\"\".concat(prefixCls, \"-jump-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-next-custom-icon\"), !!jumpNextIcon))\n          }, itemRender(this.getJumpNextPage(), 'jump-next', this.getItemIcon(jumpNextIcon, 'next page')));\n        }\n        lastPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          last: true,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: allPages,\n          page: allPages,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        firstPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: 1,\n          page: 1,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        var left = Math.max(1, current - pageBufferSize);\n        var right = Math.min(current + pageBufferSize, allPages);\n        if (current - 1 <= pageBufferSize) {\n          right = 1 + pageBufferSize * 2;\n        }\n        if (allPages - current <= pageBufferSize) {\n          left = allPages - pageBufferSize * 2;\n        }\n        for (var _i = left; _i <= right; _i += 1) {\n          var _active = current === _i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, {\n            locale: locale,\n            rootPrefixCls: prefixCls,\n            onClick: this.handleChange,\n            onKeyPress: this.runIfEnter,\n            key: _i,\n            page: _i,\n            active: _active,\n            showTitle: showTitle,\n            itemRender: itemRender\n          }));\n        }\n        if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {\n          pagerList[0] = /*#__PURE__*/cloneElement(pagerList[0], {\n            className: \"\".concat(prefixCls, \"-item-after-jump-prev\")\n          });\n          pagerList.unshift(jumpPrev);\n        }\n        if (allPages - current >= pageBufferSize * 2 && current !== allPages - 2) {\n          pagerList[pagerList.length - 1] = /*#__PURE__*/cloneElement(pagerList[pagerList.length - 1], {\n            className: \"\".concat(prefixCls, \"-item-before-jump-next\")\n          });\n          pagerList.push(jumpNext);\n        }\n        if (left !== 1) {\n          pagerList.unshift(firstPager);\n        }\n        if (right !== allPages) {\n          pagerList.push(lastPager);\n        }\n      }\n      var prevDisabled = !this.hasPrev() || !allPages;\n      var nextDisabled = !this.hasNext() || !allPages;\n      return /*#__PURE__*/React.createElement(\"ul\", _extends({\n        className: classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n        style: style,\n        ref: this.paginationNode\n      }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.prev_page : null,\n        onClick: this.prev,\n        tabIndex: prevDisabled ? null : 0,\n        onKeyPress: this.runIfEnterPrev,\n        className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), prevDisabled)),\n        \"aria-disabled\": prevDisabled\n      }, this.renderPrev(prevPage)), pagerList, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.next_page : null,\n        onClick: this.next,\n        tabIndex: nextDisabled ? null : 0,\n        onKeyPress: this.runIfEnterNext,\n        className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), nextDisabled)),\n        \"aria-disabled\": nextDisabled\n      }, this.renderNext(nextPage)), /*#__PURE__*/React.createElement(Options, {\n        disabled: disabled,\n        locale: locale,\n        rootPrefixCls: prefixCls,\n        selectComponentClass: selectComponentClass,\n        selectPrefixCls: selectPrefixCls,\n        changeSize: this.getShowSizeChanger() ? this.changePageSize : null,\n        current: current,\n        pageSize: pageSize,\n        pageSizeOptions: pageSizeOptions,\n        quickGo: this.shouldDisplayQuickJumper() ? this.handleChange : null,\n        goButton: goButton\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var newState = {};\n      if ('current' in props) {\n        newState.current = props.current;\n        if (props.current !== prevState.current) {\n          newState.currentInputValue = newState.current;\n        }\n      }\n      if ('pageSize' in props && props.pageSize !== prevState.pageSize) {\n        var current = prevState.current;\n        var newCurrent = calculatePage(props.pageSize, prevState, props);\n        current = current > newCurrent ? newCurrent : current;\n        if (!('current' in props)) {\n          newState.current = current;\n          newState.currentInputValue = current;\n        }\n        newState.pageSize = props.pageSize;\n      }\n      return newState;\n    }\n  }]);\n  return Pagination;\n}(React.Component);\nPagination.defaultProps = {\n  defaultCurrent: 1,\n  total: 0,\n  defaultPageSize: 10,\n  onChange: noop,\n  className: '',\n  selectPrefixCls: 'rc-select',\n  prefixCls: 'rc-pagination',\n  selectComponentClass: null,\n  hideOnSinglePage: false,\n  showPrevNextJumpers: true,\n  showQuickJumper: false,\n  showLessItems: false,\n  showTitle: true,\n  onShowSizeChange: noop,\n  locale: LOCALE,\n  style: {},\n  itemRender: defaultItemRender,\n  totalBoundaryShowSizeChanger: 50\n};\nexport default Pagination;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}