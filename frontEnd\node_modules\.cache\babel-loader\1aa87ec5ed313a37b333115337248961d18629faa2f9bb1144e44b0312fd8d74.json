{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport { toArray } from \"./valueUtil\";\nfunction warningProps(props) {\n  var searchPlaceholder = props.searchPlaceholder,\n    treeCheckStrictly = props.treeCheckStrictly,\n    treeCheckable = props.treeCheckable,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    multiple = props.multiple;\n  warning(!searchPlaceholder, '`searchPlaceholder` has been removed.');\n  if (treeCheckStrictly && labelInValue === false) {\n    warning(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');\n  }\n  if (labelInValue || treeCheckStrictly) {\n    warning(toArray(value).every(function (val) {\n      return val && _typeof(val) === 'object' && 'value' in val;\n    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');\n  }\n  if (treeCheckStrictly || multiple || treeCheckable) {\n    warning(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');\n  } else {\n    warning(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');\n  }\n}\nexport default warningProps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}