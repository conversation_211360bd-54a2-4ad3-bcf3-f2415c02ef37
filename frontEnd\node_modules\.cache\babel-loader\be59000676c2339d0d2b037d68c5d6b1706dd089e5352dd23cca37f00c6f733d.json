{"ast": null, "code": "import toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  const type = typeof node;\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  let totalLen = 0;\n  nodeList.forEach(node => {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = cuttable(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nconst NONE = 0;\nconst PREPARE = 1;\nconst WALKING = 2;\nconst DONE_WITH_ELLIPSIS = 3;\nconst DONE_WITHOUT_ELLIPSIS = 4;\nconst Ellipsis = _ref => {\n  let {\n    enabledMeasure,\n    children,\n    text,\n    width,\n    fontSize,\n    rows,\n    onEllipsis\n  } = _ref;\n  const [[startLen, midLen, endLen], setCutLength] = React.useState([0, 0, 0]);\n  const [walkingState, setWalkingState] = React.useState(NONE);\n  const [singleRowHeight, setSingleRowHeight] = React.useState(0);\n  const singleRowRef = React.useRef(null);\n  const midRowRef = React.useRef(null);\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const totalLen = React.useMemo(() => getNodesLen(nodeList), [nodeList]);\n  const mergedChildren = React.useMemo(() => {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(() => {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(() => {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        const midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        const maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          const midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          const maxHeight = rows * singleRowHeight;\n          let nextStartLen = startLen;\n          let nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (midHeight <= maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          const nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  const measureStyle = {\n    width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  const renderMeasure = (content, ref, style) => /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: ref,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      zIndex: -9999,\n      visibility: 'hidden',\n      pointerEvents: 'none',\n      fontSize: Math.floor(fontSize / 2) * 2\n    }, style)\n  }, content);\n  const renderMeasureSlice = (len, ref) => {\n    const sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}