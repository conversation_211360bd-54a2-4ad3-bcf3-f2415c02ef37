{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nfunction getPosition(e) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  return {\n    pageX: obj.pageX,\n    pageY: obj.pageY\n  };\n}\nexport default function useDrag(containerRef, direction, rawValues, min, max, formatValue, triggerChange, finishChange, offsetValues) {\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    draggingValue = _React$useState2[0],\n    setDraggingValue = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    draggingIndex = _React$useState4[0],\n    setDraggingIndex = _React$useState4[1];\n  var _React$useState5 = React.useState(rawValues),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    cacheValues = _React$useState6[0],\n    setCacheValues = _React$useState6[1];\n  var _React$useState7 = React.useState(rawValues),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    originValues = _React$useState8[0],\n    setOriginValues = _React$useState8[1];\n  var mouseMoveEventRef = React.useRef(null);\n  var mouseUpEventRef = React.useRef(null);\n  React.useEffect(function () {\n    if (draggingIndex === -1) {\n      setCacheValues(rawValues);\n    }\n  }, [rawValues, draggingIndex]);\n  // Clean up event\n  React.useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveEventRef.current);\n      document.removeEventListener('mouseup', mouseUpEventRef.current);\n      document.removeEventListener('touchmove', mouseMoveEventRef.current);\n      document.removeEventListener('touchend', mouseUpEventRef.current);\n    };\n  }, []);\n  var flushValues = function flushValues(nextValues, nextValue) {\n    // Perf: Only update state when value changed\n    if (cacheValues.some(function (val, i) {\n      return val !== nextValues[i];\n    })) {\n      if (nextValue !== undefined) {\n        setDraggingValue(nextValue);\n      }\n      setCacheValues(nextValues);\n      triggerChange(nextValues);\n    }\n  };\n  var updateCacheValue = function updateCacheValue(valueIndex, offsetPercent) {\n    // Basic point offset\n    if (valueIndex === -1) {\n      // >>>> Dragging on the track\n      var startValue = originValues[0];\n      var endValue = originValues[originValues.length - 1];\n      var maxStartOffset = min - startValue;\n      var maxEndOffset = max - endValue;\n      // Get valid offset\n      var offset = offsetPercent * (max - min);\n      offset = Math.max(offset, maxStartOffset);\n      offset = Math.min(offset, maxEndOffset);\n      // Use first value to revert back of valid offset (like steps marks)\n      var formatStartValue = formatValue(startValue + offset);\n      offset = formatStartValue - startValue;\n      var cloneCacheValues = originValues.map(function (val) {\n        return val + offset;\n      });\n      flushValues(cloneCacheValues);\n    } else {\n      // >>>> Dragging on the handle\n      var offsetDist = (max - min) * offsetPercent;\n      // Always start with the valueIndex origin value\n      var cloneValues = _toConsumableArray(cacheValues);\n      cloneValues[valueIndex] = originValues[valueIndex];\n      var next = offsetValues(cloneValues, offsetDist, valueIndex, 'dist');\n      flushValues(next.values, next.value);\n    }\n  };\n  // Resolve closure\n  var updateCacheValueRef = React.useRef(updateCacheValue);\n  updateCacheValueRef.current = updateCacheValue;\n  var onStartMove = function onStartMove(e, valueIndex) {\n    e.stopPropagation();\n    var originValue = rawValues[valueIndex];\n    setDraggingIndex(valueIndex);\n    setDraggingValue(originValue);\n    setOriginValues(rawValues);\n    var _getPosition = getPosition(e),\n      startX = _getPosition.pageX,\n      startY = _getPosition.pageY;\n    // Moving\n    var onMouseMove = function onMouseMove(event) {\n      event.preventDefault();\n      var _getPosition2 = getPosition(event),\n        moveX = _getPosition2.pageX,\n        moveY = _getPosition2.pageY;\n      var offsetX = moveX - startX;\n      var offsetY = moveY - startY;\n      var _containerRef$current = containerRef.current.getBoundingClientRect(),\n        width = _containerRef$current.width,\n        height = _containerRef$current.height;\n      var offSetPercent;\n      switch (direction) {\n        case 'btt':\n          offSetPercent = -offsetY / height;\n          break;\n        case 'ttb':\n          offSetPercent = offsetY / height;\n          break;\n        case 'rtl':\n          offSetPercent = -offsetX / width;\n          break;\n        default:\n          offSetPercent = offsetX / width;\n      }\n      updateCacheValueRef.current(valueIndex, offSetPercent);\n    };\n    // End\n    var onMouseUp = function onMouseUp(event) {\n      event.preventDefault();\n      document.removeEventListener('mouseup', onMouseUp);\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('touchend', onMouseUp);\n      document.removeEventListener('touchmove', onMouseMove);\n      mouseMoveEventRef.current = null;\n      mouseUpEventRef.current = null;\n      setDraggingIndex(-1);\n      finishChange();\n    };\n    document.addEventListener('mouseup', onMouseUp);\n    document.addEventListener('mousemove', onMouseMove);\n    document.addEventListener('touchend', onMouseUp);\n    document.addEventListener('touchmove', onMouseMove);\n    mouseMoveEventRef.current = onMouseMove;\n    mouseUpEventRef.current = onMouseUp;\n  };\n  // Only return cache value when it mapping with rawValues\n  var returnValues = React.useMemo(function () {\n    var sourceValues = _toConsumableArray(rawValues).sort(function (a, b) {\n      return a - b;\n    });\n    var targetValues = _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n    return sourceValues.every(function (val, index) {\n      return val === targetValues[index];\n    }) ? cacheValues : rawValues;\n  }, [rawValues, cacheValues]);\n  return [draggingIndex, draggingValue, returnValues, onStartMove];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}