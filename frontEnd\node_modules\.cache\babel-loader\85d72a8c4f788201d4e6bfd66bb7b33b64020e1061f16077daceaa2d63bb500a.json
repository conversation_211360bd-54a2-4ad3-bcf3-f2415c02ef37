{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getDirectionStyle } from '../util';\nimport SliderContext from '../context';\nexport default function Dot(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    included = _React$useContext.included,\n    includedStart = _React$useContext.includedStart,\n    includedEnd = _React$useContext.includedEnd;\n  var dotClassName = \"\".concat(prefixCls, \"-dot\");\n  var active = included && includedStart <= value && value <= includedEnd;\n  // ============================ Offset ============================\n  var mergedStyle = _objectSpread(_objectSpread({}, getDirectionStyle(direction, value, min, max)), typeof style === 'function' ? style(value) : style);\n  if (active) {\n    mergedStyle = _objectSpread(_objectSpread({}, mergedStyle), typeof activeStyle === 'function' ? activeStyle(value) : activeStyle);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(dotClassName, _defineProperty({}, \"\".concat(dotClassName, \"-active\"), active)),\n    style: mergedStyle\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}