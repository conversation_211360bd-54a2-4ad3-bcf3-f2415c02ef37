{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from '@rc-component/portal';\nimport Dialog from \"./Dialog\";\n// fix issue #10656\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]);\n\n  // // 渲染在当前 dom 里；\n  // if (getContainer === false) {\n  //   return (\n  //     <Dialog\n  //       {...props}\n  //       getOpenCount={() => 2} // 不对 body 做任何操作。。\n  //     />\n  //   );\n  // }\n\n  // Destroy on close will remove wrapped div\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: visible || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: visible || animatedVisible\n  }, /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n    destroyOnClose: destroyOnClose,\n    afterClose: function afterClose() {\n      _afterClose === null || _afterClose === void 0 ? void 0 : _afterClose();\n      setAnimatedVisible(false);\n    }\n  })));\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}