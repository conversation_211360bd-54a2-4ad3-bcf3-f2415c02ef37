{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { DECADE_DISTANCE_COUNT, DECADE_UNIT_DIFF } from \"./constant\";\nimport PanelBody from \"../PanelBody\";\nexport var DECADE_COL_COUNT = 3;\nvar DECADE_ROW_COUNT = 4;\nfunction DecadeBody(props) {\n  var DECADE_UNIT_DIFF_DES = DECADE_UNIT_DIFF - 1;\n  var prefixCls = props.prefixCls,\n    viewDate = props.viewDate,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender,\n    locale = props.locale;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var decadeYearNumber = Math.floor(yearNumber / DECADE_UNIT_DIFF) * DECADE_UNIT_DIFF;\n  var startDecadeYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;\n  var endDecadeYear = startDecadeYear + DECADE_DISTANCE_COUNT - 1;\n  var baseDecadeYear = generateConfig.setYear(viewDate, startDecadeYear - Math.ceil((DECADE_COL_COUNT * DECADE_ROW_COUNT * DECADE_UNIT_DIFF - DECADE_DISTANCE_COUNT) / 2));\n  var getCellClassName = function getCellClassName(date) {\n    var _ref;\n    var startDecadeNumber = generateConfig.getYear(date);\n    var endDecadeNumber = startDecadeNumber + DECADE_UNIT_DIFF_DES;\n    return _ref = {}, _defineProperty(_ref, \"\".concat(cellPrefixCls, \"-in-view\"), startDecadeYear <= startDecadeNumber && endDecadeNumber <= endDecadeYear), _defineProperty(_ref, \"\".concat(cellPrefixCls, \"-selected\"), startDecadeNumber === decadeYearNumber), _ref;\n  };\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      today: generateConfig.getNow(),\n      type: 'decade',\n      locale: locale\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: DECADE_ROW_COUNT,\n    colNum: DECADE_COL_COUNT,\n    baseDate: baseDecadeYear,\n    getCellNode: getCellNode,\n    getCellText: function getCellText(date) {\n      var startDecadeNumber = generateConfig.getYear(date);\n      return \"\".concat(startDecadeNumber, \"-\").concat(startDecadeNumber + DECADE_UNIT_DIFF_DES);\n    },\n    getCellClassName: getCellClassName,\n    getCellDate: function getCellDate(date, offset) {\n      return generateConfig.addYear(date, offset * DECADE_UNIT_DIFF);\n    }\n  }));\n}\nexport default DecadeBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}