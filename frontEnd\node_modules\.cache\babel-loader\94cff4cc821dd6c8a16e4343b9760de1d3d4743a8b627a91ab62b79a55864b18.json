{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { getOffset } from \"rc-util/es/Dom/css\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport * as React from 'react';\nimport TableContext from \"./context/TableContext\";\nimport { useLayoutState } from \"./hooks/useFrame\";\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: false\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    if (left <= 0) {\n      left = 0;\n    }\n    if (left + scrollBarWidth >= bodyWidth) {\n      left = bodyWidth - scrollBarWidth;\n    }\n    onScroll({\n      scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n    });\n    refState.current.x = event.pageX;\n  };\n  var onContainerScroll = function onContainerScroll() {\n    if (!scrollBodyRef.current) {\n      return;\n    }\n    var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n    var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n    var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n    if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: true\n        });\n      });\n    } else {\n      setScrollState(function (state) {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isHiddenScrollBar: false\n        });\n      });\n    }\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    onContainerScroll();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n  React.useEffect(function () {\n    var onScrollListener = addEventListener(container, 'scroll', onContainerScroll, false);\n    var onResizeListener = addEventListener(window, 'resize', onContainerScroll, false);\n    return function () {\n      onScrollListener.remove();\n      onResizeListener.remove();\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}