{"ast": null, "code": "import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    getRowKey = props.getRowKey,\n    measureColumnWidth = props.measureColumnWidth,\n    expandedKeys = props.expandedKeys,\n    onRow = props.onRow,\n    rowExpandable = props.rowExpandable,\n    emptyNode = props.emptyNode,\n    childrenColumnName = props.childrenColumnName;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index;\n      var key = getRowKey(record, idx);\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: key,\n        rowKey: key,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        expandedKeys: expandedKeys,\n        onRow: onRow,\n        getRowKey: getRowKey,\n        rowExpandable: rowExpandable,\n        childrenColumnName: childrenColumnName,\n        indent: indent\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nBody.displayName = 'Body';\nexport default responseImmutable(Body);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}