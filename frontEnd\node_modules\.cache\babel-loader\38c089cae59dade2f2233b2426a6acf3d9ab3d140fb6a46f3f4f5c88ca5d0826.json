{"ast": null, "code": "const genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor\n  } = token;\n  const tableBorder = `${lineWidth}px ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 -${lineWidth}px 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}