{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport PanelContext from \"../PanelContext\";\nimport { getCellDateDisabled } from \"../utils/dateUtil\";\nimport { getLastDay } from \"../utils/timeUtil\";\nexport default function PanelBody(_ref) {\n  var prefixCls = _ref.prefixCls,\n    disabledDate = _ref.disabledDate,\n    onSelect = _ref.onSelect,\n    picker = _ref.picker,\n    rowNum = _ref.rowNum,\n    colNum = _ref.colNum,\n    prefixColumn = _ref.prefixColumn,\n    rowClassName = _ref.rowClassName,\n    baseDate = _ref.baseDate,\n    getCellClassName = _ref.getCellClassName,\n    getCellText = _ref.getCellText,\n    getCellNode = _ref.getCellNode,\n    getCellDate = _ref.getCellDate,\n    generateConfig = _ref.generateConfig,\n    titleCell = _ref.titleCell,\n    headerCells = _ref.headerCells;\n  var _React$useContext = React.useContext(PanelContext),\n    onDateMouseEnter = _React$useContext.onDateMouseEnter,\n    onDateMouseLeave = _React$useContext.onDateMouseLeave,\n    mode = _React$useContext.mode;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n\n  // =============================== Body ===============================\n  var rows = [];\n  for (var i = 0; i < rowNum; i += 1) {\n    var row = [];\n    var rowStartDate = void 0;\n    var _loop = function _loop() {\n      var _objectSpread2;\n      var offset = i * colNum + j;\n      var currentDate = getCellDate(baseDate, offset);\n      var disabled = getCellDateDisabled({\n        cellDate: currentDate,\n        mode: mode,\n        disabledDate: disabledDate,\n        generateConfig: generateConfig\n      });\n      if (j === 0) {\n        rowStartDate = currentDate;\n        if (prefixColumn) {\n          row.push(prefixColumn(rowStartDate));\n        }\n      }\n      var title = titleCell && titleCell(currentDate);\n      var inner = /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(cellPrefixCls, \"-inner\")\n      }, getCellText(currentDate));\n      row.push( /*#__PURE__*/React.createElement(\"td\", {\n        key: j,\n        title: title,\n        className: classNames(cellPrefixCls, _objectSpread((_objectSpread2 = {}, _defineProperty(_objectSpread2, \"\".concat(cellPrefixCls, \"-disabled\"), disabled), _defineProperty(_objectSpread2, \"\".concat(cellPrefixCls, \"-start\"), getCellText(currentDate) === 1 || picker === 'year' && Number(title) % 10 === 0), _defineProperty(_objectSpread2, \"\".concat(cellPrefixCls, \"-end\"), title === getLastDay(generateConfig, currentDate) || picker === 'year' && Number(title) % 10 === 9), _objectSpread2), getCellClassName(currentDate))),\n        onClick: function onClick() {\n          if (!disabled) {\n            onSelect(currentDate);\n          }\n        },\n        onMouseEnter: function onMouseEnter() {\n          if (!disabled && onDateMouseEnter) {\n            onDateMouseEnter(currentDate);\n          }\n        },\n        onMouseLeave: function onMouseLeave() {\n          if (!disabled && onDateMouseLeave) {\n            onDateMouseLeave(currentDate);\n          }\n        }\n      }, getCellNode ? getCellNode(currentDate, inner) : inner));\n    };\n    for (var j = 0; j < colNum; j += 1) {\n      _loop();\n    }\n    rows.push( /*#__PURE__*/React.createElement(\"tr\", {\n      key: i,\n      className: rowClassName && rowClassName(rowStartDate)\n    }, row));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-body\")\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"\".concat(prefixCls, \"-content\")\n  }, headerCells && /*#__PURE__*/React.createElement(\"thead\", null, /*#__PURE__*/React.createElement(\"tr\", null, headerCells)), /*#__PURE__*/React.createElement(\"tbody\", null, rows)));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}