{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\n// recursion (flat tree structure)\nfunction flatRecord(record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  var arr = [];\n  arr.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      var tempArr = flatRecord(record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n      arr.push.apply(arr, _toConsumableArray(tempArr));\n    }\n  }\n  return arr;\n}\n\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var temp = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var _temp;\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        temp = (_temp = temp).concat.apply(_temp, _toConsumableArray(flatRecord(record, 0, childrenColumnName, expandedKeys, getRowKey, i)));\n      }\n      return temp;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}