{"ast": null, "code": "import { useMemo } from 'react';\n/**\n * Get sticky column offset width\n */\nfunction useStickyOffsets(colWidths, columnCount, direction) {\n  var stickyOffsets = useMemo(function () {\n    var leftOffsets = [];\n    var rightOffsets = [];\n    var left = 0;\n    var right = 0;\n    for (var start = 0; start < columnCount; start += 1) {\n      if (direction === 'rtl') {\n        // Left offset\n        rightOffsets[start] = right;\n        right += colWidths[start] || 0;\n\n        // Right offset\n        var end = columnCount - start - 1;\n        leftOffsets[end] = left;\n        left += colWidths[end] || 0;\n      } else {\n        // Left offset\n        leftOffsets[start] = left;\n        left += colWidths[start] || 0;\n\n        // Right offset\n        var _end = columnCount - start - 1;\n        rightOffsets[_end] = right;\n        right += colWidths[_end] || 0;\n      }\n    }\n    return {\n      left: leftOffsets,\n      right: rightOffsets\n    };\n  }, [colWidths, columnCount, direction]);\n  return stickyOffsets;\n}\nexport default useStickyOffsets;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}