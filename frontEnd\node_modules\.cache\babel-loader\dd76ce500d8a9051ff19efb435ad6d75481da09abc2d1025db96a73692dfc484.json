{"ast": null, "code": "import * as React from 'react';\nimport { renderColumnTitle } from '../util';\nfunction fillTitle(columns, columnTitleProps) {\n  return columns.map(column => {\n    const cloneColumn = Object.assign({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n}\nexport default function useTitleColumns(columnTitleProps) {\n  const filledColumns = React.useCallback(columns => fillTitle(columns, columnTitleProps), [columnTitleProps]);\n  return [filledColumns];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}