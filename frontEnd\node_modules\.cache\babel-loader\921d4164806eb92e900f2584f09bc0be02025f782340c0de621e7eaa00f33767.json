{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open,\n    notFoundContent = _useBaseProps.notFoundContent;\n  var _React$useContext = React.useContext(TreeSelectContext),\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    treeData = _React$useContext.treeData,\n    fieldNames = _React$useContext.fieldNames,\n    onSelect = _React$useContext.onSelect,\n    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n    treeExpandAction = _React$useContext.treeExpandAction;\n  var _React$useContext2 = React.useContext(LegacyContext),\n    checkable = _React$useContext2.checkable,\n    checkedKeys = _React$useContext2.checkedKeys,\n    halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n    treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n    onTreeExpand = _React$useContext2.onTreeExpand,\n    treeIcon = _React$useContext2.treeIcon,\n    showTreeIcon = _React$useContext2.showTreeIcon,\n    switcherIcon = _React$useContext2.switcherIcon,\n    treeLine = _React$useContext2.treeLine,\n    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n    loadData = _React$useContext2.loadData,\n    treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n    treeMotion = _React$useContext2.treeMotion,\n    onTreeLoad = _React$useContext2.onTreeLoad,\n    keyEntities = _React$useContext2.keyEntities;\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  }, [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // ========================== Values ==========================\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]);\n\n  // ========================== Scroll ==========================\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 ? void 0 : _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n  }, [open]);\n\n  // ========================== Search ==========================\n  var lowerSearchValue = String(searchValue).toLowerCase();\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  };\n\n  // =========================== Keys ===========================\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedKeys = _React$useState2[0],\n    setExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    searchExpandedKeys = _React$useState4[0],\n    setSearchExpandedKeys = _React$useState4[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n  }, [searchValue]);\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  };\n\n  // ========================== Events ==========================\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    activeKey = _React$useState6[0],\n    setActiveKey = _React$useState6[1];\n  var activeEntity = keyEntities[activeKey];\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 ? void 0 : _treeRef$current3.onKeyDown(event);\n            break;\n\n          // >>> Select item\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                  selectable = _ref.selectable,\n                  value = _ref.value;\n                if (selectable !== false) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: searchValue ? null : loadData,\n    motion: treeMotion,\n    activeKey: activeKey\n    // We handle keys by out instead tree self\n    ,\n\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction\n  })));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nRefOptionList.displayName = 'OptionList';\nexport default RefOptionList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}