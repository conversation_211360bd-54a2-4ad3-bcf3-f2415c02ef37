const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const User = require("../models/userModel");
const Subscription = require("../models/subscriptionModel");
const axios = require("axios");

/**
 * ADMIN PAYMENT MANAGEMENT ROUTES
 * These routes allow admins to manually verify and activate subscriptions
 * after the security fixes that disabled auto-activation
 */

// Middleware to check if user is admin
const adminMiddleware = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    const user = await User.findById(userId);
    
    if (!user || !user.isAdmin) {
      return res.status(403).json({
        success: false,
        message: "Access denied. Admin privileges required."
      });
    }
    
    next();
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Error checking admin privileges"
    });
  }
};

// Get all pending subscriptions that need manual verification
router.get("/pending-subscriptions", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    console.log('🔍 Admin requesting pending subscriptions...');
    
    // Find all subscriptions with paid payment status but pending subscription status
    const pendingSubscriptions = await Subscription.find({
      paymentStatus: 'paid',
      status: 'pending'
    }).populate('user', 'firstName lastName username email phoneNumber')
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📋 Found ${pendingSubscriptions.length} pending subscriptions`);
    
    // Add payment verification status for each subscription
    const subscriptionsWithStatus = await Promise.all(
      pendingSubscriptions.map(async (sub) => {
        let verificationStatus = 'unknown';
        let zenoPayStatus = 'unknown';
        
        if (sub.paymentHistory && sub.paymentHistory.length > 0) {
          const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
          
          if (latestPayment.orderId && latestPayment.orderId.startsWith('ORDER_')) {
            try {
              // Verify with ZenoPay
              const statusUrl = `https://api.zenoapi.com/api/v1/payments/${latestPayment.orderId}`;
              const response = await axios.get(statusUrl, {
                headers: {
                  "x-api-key": process.env.ZENOPAY_API_KEY,
                },
                timeout: 10000
              });
              
              if (response.data && response.data.data && response.data.data.length > 0) {
                const paymentData = response.data.data[0];
                zenoPayStatus = paymentData.payment_status;
                verificationStatus = paymentData.payment_status === 'COMPLETED' ? 'verified' : 'unverified';
              }
            } catch (error) {
              console.log(`❌ Error verifying ${latestPayment.orderId}:`, error.message);
              verificationStatus = 'error';
            }
          } else {
            verificationStatus = 'invalid_order_id';
          }
        } else {
          verificationStatus = 'no_payment_history';
        }
        
        return {
          ...sub.toObject(),
          verificationStatus,
          zenoPayStatus,
          timeSinceCreated: Math.floor((Date.now() - new Date(sub.createdAt).getTime()) / (1000 * 60 * 60)) // hours
        };
      })
    );
    
    res.json({
      success: true,
      data: subscriptionsWithStatus,
      count: subscriptionsWithStatus.length
    });
    
  } catch (error) {
    console.error('❌ Error fetching pending subscriptions:', error);
    res.status(500).json({
      success: false,
      message: "Error fetching pending subscriptions",
      error: error.message
    });
  }
});

// ENHANCED: Secure manual activation with comprehensive validation
router.post("/activate-subscription", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { subscriptionId, reason } = req.body;
    const adminUserId = req.user.userId;

    console.log(`🔐 SECURE: Admin ${adminUserId} attempting to activate subscription ${subscriptionId}`);
    console.log(`📝 Reason: ${reason || 'manual_admin_approval'}`);

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        message: "Subscription ID is required"
      });
    }

    // Use secure activation system
    const secureActivation = require("../utils/secureActivation");
    const paymentValidator = require("../utils/paymentValidator");

    // Find the subscription with full details
    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', 'firstName lastName username email phoneNumber')
      .populate('activePlan', 'title duration discountedPrice');

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found"
      });
    }

    console.log(`👤 User: ${subscription.user.firstName} ${subscription.user.lastName} (${subscription.user.username})`);
    console.log(`📋 Plan: ${subscription.activePlan?.title}`);

    // Enhanced payment validation using secure validator
    const validationResult = await paymentValidator.validateSubscriptionForActivation(subscription);

    if (!validationResult.canActivate) {
      console.log(`❌ SECURITY: Activation denied - ${validationResult.reason}`);

      // Log the denied activation attempt
      paymentValidator.logValidationAttempt(
        validationResult.orderId || 'unknown',
        validationResult,
        'admin_activation_denied'
      );

      return res.status(400).json({
        success: false,
        message: `Cannot activate subscription: ${validationResult.message}`,
        reason: validationResult.reason,
        paymentStatus: validationResult.paymentStatus,
        orderId: validationResult.orderId
      });
    }

    console.log(`✅ Payment validation passed for order: ${validationResult.orderId}`);
    console.log(`💳 Payment status confirmed: COMPLETED`);

    // Perform secure manual activation
    const activationResult = await secureActivation.manuallyActivateSubscription(
      subscriptionId,
      adminUserId,
      reason || 'manual_admin_approval'
    );

    if (activationResult.success) {
      console.log(`✅ SECURE ACTIVATION SUCCESSFUL`);
      console.log(`📅 Subscription period: ${activationResult.startDate} to ${activationResult.endDate}`);
      console.log(`👤 User: ${subscription.user.firstName} ${subscription.user.lastName}`);
      console.log(`🔐 Activated by admin: ${adminUserId}`);

      // Log successful activation
      paymentValidator.logValidationAttempt(
        validationResult.orderId,
        { valid: true, status: 'ACTIVATED' },
        'admin_activation_success'
      );

      res.json({
        success: true,
        message: "Subscription activated successfully",
        subscription: {
          id: activationResult.subscriptionId,
          userId: activationResult.userId,
          userName: `${subscription.user.firstName} ${subscription.user.lastName}`,
          userEmail: subscription.user.email,
          planTitle: subscription.activePlan?.title,
          startDate: activationResult.startDate,
          endDate: activationResult.endDate,
          activatedBy: activationResult.activatedBy,
          orderId: validationResult.orderId
        }
      });
    } else {
      console.log(`❌ SECURE ACTIVATION FAILED: ${activationResult.message}`);

      // Log failed activation
      paymentValidator.logValidationAttempt(
        validationResult.orderId || 'unknown',
        { valid: false, reason: activationResult.message },
        'admin_activation_failed'
      );

      res.status(400).json({
        success: false,
        message: activationResult.message,
        error: activationResult.error
      });
    }
    
    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date();
    const planDuration = subscription.activePlan?.duration || 6;
    endDate.setMonth(endDate.getMonth() + planDuration);
    
    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];
    
    // Update subscription
    subscription.status = 'active';
    subscription.startDate = formattedStartDate;
    subscription.endDate = formattedEndDate;
    
    // Add activation record to payment history
    if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.referenceId = `MANUAL_ACTIVATION_${adminUserId}_${Date.now()}`;
    }
    
    await subscription.save();
    
    // Update user status
    const user = await User.findById(subscription.user._id);
    if (user) {
      user.subscriptionStatus = 'active';
      user.paymentRequired = false;
      user.subscriptionEndDate = new Date(formattedEndDate);
      await user.save();
    }
    
    console.log(`✅ Subscription activated by admin ${adminUserId}`);
    console.log(`👤 User: ${subscription.user.username}`);
    console.log(`📅 Period: ${formattedStartDate} to ${formattedEndDate}`);
    
    res.json({
      success: true,
      message: "Subscription activated successfully",
      data: {
        subscriptionId: subscription._id,
        user: subscription.user.username,
        plan: subscription.activePlan.title,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        activatedBy: adminUserId,
        reason: reason
      }
    });
    
  } catch (error) {
    console.error('❌ Error activating subscription:', error);
    res.status(500).json({
      success: false,
      message: "Error activating subscription",
      error: error.message
    });
  }
});

// Reject/revoke a subscription
router.post("/reject-subscription", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { subscriptionId, reason } = req.body;
    const adminUserId = req.user.userId;
    
    console.log(`🚫 Admin ${adminUserId} rejecting subscription ${subscriptionId}`);
    console.log(`📝 Reason: ${reason}`);
    
    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', 'firstName lastName username email');
    
    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found"
      });
    }
    
    // Update subscription status
    subscription.status = 'expired';
    subscription.paymentStatus = 'failed';
    
    // Add rejection record
    if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.referenceId = `REJECTED_${adminUserId}_${Date.now()}_${reason}`;
    }
    
    await subscription.save();
    
    // Update user status
    const user = await User.findById(subscription.user._id);
    if (user) {
      user.subscriptionStatus = 'free';
      user.paymentRequired = true;
      user.subscriptionEndDate = null;
      await user.save();
    }
    
    console.log(`❌ Subscription rejected by admin ${adminUserId}`);
    
    res.json({
      success: true,
      message: "Subscription rejected successfully",
      data: {
        subscriptionId: subscription._id,
        user: subscription.user.username,
        rejectedBy: adminUserId,
        reason: reason
      }
    });
    
  } catch (error) {
    console.error('❌ Error rejecting subscription:', error);
    res.status(500).json({
      success: false,
      message: "Error rejecting subscription",
      error: error.message
    });
  }
});

// ENHANCED: Get subscriptions pending manual activation
router.get("/pending-activations", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    console.log(`🔍 Admin ${req.user.userId} requesting pending activations`);

    const secureActivation = require("../utils/secureActivation");
    const pendingActivations = await secureActivation.getPendingActivations();

    console.log(`📋 Found ${pendingActivations.length} subscriptions pending manual activation`);

    res.json({
      success: true,
      message: `Found ${pendingActivations.length} subscriptions pending activation`,
      pendingActivations: pendingActivations,
      count: pendingActivations.length
    });

  } catch (error) {
    console.error(`❌ Error getting pending activations:`, error.message);
    res.status(500).json({
      success: false,
      message: "Error retrieving pending activations",
      error: error.message
    });
  }
});

// ENHANCED: Validate payment status for a subscription
router.get("/validate-payment/:subscriptionId", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    console.log(`🔍 Admin ${req.user.userId} validating payment for subscription ${subscriptionId}`);

    const paymentValidator = require("../utils/paymentValidator");

    const subscription = await Subscription.findById(subscriptionId)
      .populate('user', 'firstName lastName username email')
      .populate('activePlan', 'title duration');

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: "Subscription not found"
      });
    }

    const validationResult = await paymentValidator.validateSubscriptionForActivation(subscription);

    // Log the validation attempt
    paymentValidator.logValidationAttempt(
      validationResult.orderId || 'unknown',
      validationResult,
      'admin_payment_validation'
    );

    res.json({
      success: true,
      subscription: {
        id: subscription._id,
        user: subscription.user,
        plan: subscription.activePlan,
        status: subscription.status,
        paymentStatus: subscription.paymentStatus
      },
      validation: validationResult
    });

  } catch (error) {
    console.error(`❌ Error validating payment:`, error.message);
    res.status(500).json({
      success: false,
      message: "Error validating payment",
      error: error.message
    });
  }
});

module.exports = router;
