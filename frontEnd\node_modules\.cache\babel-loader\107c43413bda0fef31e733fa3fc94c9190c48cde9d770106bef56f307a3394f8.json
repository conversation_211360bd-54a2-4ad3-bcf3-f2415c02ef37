{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    this.maps = void 0;\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      this.maps[key] = value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}