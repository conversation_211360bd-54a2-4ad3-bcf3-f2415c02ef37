{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport { useEffect, useRef, useState } from 'react';\nimport { addGlobalMouseDownEvent, getTargetFromEvent } from \"../utils/uiUtil\";\nexport default function usePickerInput(_ref) {\n  var open = _ref.open,\n    value = _ref.value,\n    isClickOutside = _ref.isClickOutside,\n    triggerOpen = _ref.triggerOpen,\n    forwardKeyDown = _ref.forwardKeyDown,\n    _onKeyDown = _ref.onKeyDown,\n    blurToCancel = _ref.blurToCancel,\n    onSubmit = _ref.onSubmit,\n    onCancel = _ref.onCancel,\n    _onFocus = _ref.onFocus,\n    _onBlur = _ref.onBlur,\n    changeOnBlur = _ref.changeOnBlur;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    typing = _useState2[0],\n    setTyping = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    focused = _useState4[0],\n    setFocused = _useState4[1];\n\n  /**\n   * We will prevent blur to handle open event when user click outside,\n   * since this will repeat trigger `onOpenChange` event.\n   */\n  var preventBlurRef = useRef(false);\n  var valueChangedRef = useRef(false);\n  var preventDefaultRef = useRef(false);\n  var inputProps = {\n    onMouseDown: function onMouseDown() {\n      setTyping(true);\n      triggerOpen(true);\n    },\n    onKeyDown: function onKeyDown(e) {\n      var preventDefault = function preventDefault() {\n        preventDefaultRef.current = true;\n      };\n      _onKeyDown(e, preventDefault);\n      if (preventDefaultRef.current) return;\n      switch (e.which) {\n        case KeyCode.ENTER:\n          {\n            if (!open) {\n              triggerOpen(true);\n            } else if (onSubmit() !== false) {\n              setTyping(true);\n            }\n            e.preventDefault();\n            return;\n          }\n        case KeyCode.TAB:\n          {\n            if (typing && open && !e.shiftKey) {\n              setTyping(false);\n              e.preventDefault();\n            } else if (!typing && open) {\n              if (!forwardKeyDown(e) && e.shiftKey) {\n                setTyping(true);\n                e.preventDefault();\n              }\n            }\n            return;\n          }\n        case KeyCode.ESC:\n          {\n            setTyping(true);\n            onCancel();\n            return;\n          }\n      }\n      if (!open && ![KeyCode.SHIFT].includes(e.which)) {\n        triggerOpen(true);\n      } else if (!typing) {\n        // Let popup panel handle keyboard\n        forwardKeyDown(e);\n      }\n    },\n    onFocus: function onFocus(e) {\n      setTyping(true);\n      setFocused(true);\n      if (_onFocus) {\n        _onFocus(e);\n      }\n    },\n    onBlur: function onBlur(e) {\n      if (preventBlurRef.current || !isClickOutside(document.activeElement)) {\n        preventBlurRef.current = false;\n        return;\n      }\n      if (blurToCancel) {\n        setTimeout(function () {\n          var _document = document,\n            activeElement = _document.activeElement;\n          while (activeElement && activeElement.shadowRoot) {\n            activeElement = activeElement.shadowRoot.activeElement;\n          }\n          if (isClickOutside(activeElement)) {\n            onCancel();\n          }\n        }, 0);\n      } else if (open) {\n        triggerOpen(false);\n        if (valueChangedRef.current) {\n          onSubmit();\n        }\n      }\n      setFocused(false);\n      _onBlur === null || _onBlur === void 0 ? void 0 : _onBlur(e);\n    }\n  };\n\n  // check if value changed\n  useEffect(function () {\n    valueChangedRef.current = false;\n  }, [open]);\n  useEffect(function () {\n    valueChangedRef.current = true;\n  }, [value]);\n\n  // Global click handler\n  useEffect(function () {\n    return addGlobalMouseDownEvent(function (e) {\n      var target = getTargetFromEvent(e);\n      var clickedOutside = isClickOutside(target);\n      if (open) {\n        if (!clickedOutside) {\n          preventBlurRef.current = true;\n\n          // Always set back in case `onBlur` prevented by user\n          raf(function () {\n            preventBlurRef.current = false;\n          });\n        } else if (!changeOnBlur && (!focused || clickedOutside)) {\n          triggerOpen(false);\n        }\n      }\n    });\n  });\n  return [inputProps, {\n    focused: focused,\n    typing: typing\n  }];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}