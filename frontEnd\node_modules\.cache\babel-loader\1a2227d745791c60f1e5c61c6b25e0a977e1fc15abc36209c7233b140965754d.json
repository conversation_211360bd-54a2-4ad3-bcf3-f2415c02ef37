{"ast": null, "code": "import * as React from 'react';\nfunction AddButton(_ref, ref) {\n  var prefixCls = _ref.prefixCls,\n    editable = _ref.editable,\n    locale = _ref.locale,\n    style = _ref.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n}\nexport default /*#__PURE__*/React.forwardRef(AddButton);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}