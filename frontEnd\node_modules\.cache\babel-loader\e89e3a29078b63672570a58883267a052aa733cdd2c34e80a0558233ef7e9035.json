{"ast": null, "code": "'use strict';\n\nvar isCallable = require('../internals/is-callable');\nvar $documentAll = require('../internals/document-all');\nvar documentAll = $documentAll.all;\nmodule.exports = $documentAll.IS_HTMLDDA ? function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it) || it === documentAll;\n} : function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}