{"ast": null, "code": "'use strict';\n\nvar IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.32.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2023 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.32.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}