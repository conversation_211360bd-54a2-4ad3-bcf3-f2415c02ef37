{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n/* eslint react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nvar Pager = function Pager(props) {\n  var _classNames;\n  var rootPrefixCls = props.rootPrefixCls,\n    page = props.page,\n    active = props.active,\n    className = props.className,\n    showTitle = props.showTitle,\n    onClick = props.onClick,\n    onKeyPress = props.onKeyPress,\n    itemRender = props.itemRender;\n  var prefixCls = \"\".concat(rootPrefixCls, \"-item\");\n  var cls = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(page), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-active\"), active), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), !page), _defineProperty(_classNames, props.className, className), _classNames));\n  var handleClick = function handleClick() {\n    onClick(page);\n  };\n  var handleKeyPress = function handleKeyPress(e) {\n    onKeyPress(e, onClick, page);\n  };\n  return /*#__PURE__*/React.createElement(\"li\", {\n    title: showTitle ? page.toString() : null,\n    className: cls,\n    onClick: handleClick,\n    onKeyPress: handleKeyPress,\n    tabIndex: 0\n  }, itemRender(page, 'page', /*#__PURE__*/React.createElement(\"a\", {\n    rel: \"nofollow\"\n  }, page)));\n};\nexport default Pager;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}