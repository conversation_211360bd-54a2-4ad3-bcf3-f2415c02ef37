{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { Color } from \"./color\";\nexport var ColorPickerPrefixCls = 'rc-color-picker';\nexport var generateColor = function generateColor(color) {\n  if (color instanceof Color) {\n    return color;\n  }\n  return new Color(color);\n};\nexport var defaultColor = generateColor('#1677ff');\nexport var calculateColor = function calculateColor(props) {\n  var offset = props.offset,\n    targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    color = props.color,\n    type = props.type;\n  var _containerRef$current = containerRef.current.getBoundingClientRect(),\n    width = _containerRef$current.width,\n    height = _containerRef$current.height;\n  var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n    targetWidth = _targetRef$current$ge.width,\n    targetHeight = _targetRef$current$ge.height;\n  var centerOffsetX = targetWidth / 2;\n  var centerOffsetY = targetHeight / 2;\n  var saturation = (offset.x + centerOffsetX) / width;\n  var bright = 1 - (offset.y + centerOffsetY) / height;\n  var hsb = color.toHsb();\n  var alphaOffset = saturation;\n  var hueOffset = (offset.x + centerOffsetX) / width * 360;\n  if (type) {\n    switch (type) {\n      case 'hue':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          h: hueOffset <= 0 ? 0 : hueOffset\n        }));\n      case 'alpha':\n        return generateColor(_objectSpread(_objectSpread({}, hsb), {}, {\n          a: alphaOffset <= 0 ? 0 : alphaOffset\n        }));\n    }\n  }\n  return generateColor({\n    h: hsb.h,\n    s: saturation <= 0 ? 0 : saturation,\n    b: bright >= 1 ? 1 : bright,\n    a: hsb.a\n  });\n};\nexport var calculateOffset = function calculateOffset(containerRef, targetRef, color, type) {\n  var _containerRef$current2 = containerRef.current.getBoundingClientRect(),\n    width = _containerRef$current2.width,\n    height = _containerRef$current2.height;\n  var _targetRef$current$ge2 = targetRef.current.getBoundingClientRect(),\n    targetWidth = _targetRef$current$ge2.width,\n    targetHeight = _targetRef$current$ge2.height;\n  var centerOffsetX = targetWidth / 2;\n  var centerOffsetY = targetHeight / 2;\n  var hsb = color.toHsb();\n\n  // Exclusion of boundary cases\n  if (targetWidth === 0 && targetHeight === 0 || targetWidth !== targetHeight) {\n    return;\n  }\n  if (type) {\n    switch (type) {\n      case 'hue':\n        return {\n          x: hsb.h / 360 * width - centerOffsetX,\n          y: -centerOffsetY / 3\n        };\n      case 'alpha':\n        return {\n          x: hsb.a / 1 * width - centerOffsetX,\n          y: -centerOffsetY / 3\n        };\n    }\n  }\n  return {\n    x: hsb.s * width - centerOffsetX,\n    y: (1 - hsb.b) * height - centerOffsetY\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}