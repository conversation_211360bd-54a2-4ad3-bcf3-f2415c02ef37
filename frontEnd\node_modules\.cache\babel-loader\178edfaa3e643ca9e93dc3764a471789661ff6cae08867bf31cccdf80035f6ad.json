{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { useBaseProps } from 'rc-select';\n\n/**\n * Control the active open options path.\n */\nexport default (function () {\n  var _useBaseProps = useBaseProps(),\n    multiple = _useBaseProps.multiple,\n    open = _useBaseProps.open;\n  var _React$useContext = React.useContext(CascaderContext),\n    values = _React$useContext.values;\n\n  // Record current dropdown active options\n  // This also control the open status\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValueCells = _React$useState2[0],\n    setActiveValueCells = _React$useState2[1];\n  React.useEffect(function () {\n    if (open && !multiple) {\n      var firstValueCells = values[0];\n      setActiveValueCells(firstValueCells || []);\n    }\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [open]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  return [activeValueCells, setActiveValueCells];\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}