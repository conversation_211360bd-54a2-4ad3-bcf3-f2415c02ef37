{"ast": null, "code": "import KeyCode from \"rc-util/es/KeyCode\";\n\n/** keyCode Judgment function */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(currentKeyCode) {\n  return ![\n  // System function button\n  KeyCode.ESC, KeyCode.SHIFT, KeyCode.BACKSPACE, KeyCode.TAB, KeyCode.WIN_KEY, KeyCode.ALT, KeyCode.META, KeyCode.WIN_KEY_RIGHT, KeyCode.CTRL, KeyCode.SEMICOLON, KeyCode.EQUALS, KeyCode.CAPS_LOCK, KeyCode.CONTEXT_MENU,\n  // F1-F12\n  KeyCode.F1, KeyCode.F2, KeyCode.F3, KeyCode.F4, KeyCode.F5, KeyCode.F6, KeyCode.F7, KeyCode.F8, KeyCode.F9, KeyCode.F10, KeyCode.F11, KeyCode.F12].includes(currentKeyCode);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}