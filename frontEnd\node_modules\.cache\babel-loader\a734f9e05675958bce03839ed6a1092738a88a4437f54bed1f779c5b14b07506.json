{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { PreviewGroupContext } from \"../context\";\nvar uid = 0;\nexport default function useRegisterImage(canPreview, data) {\n  var _React$useState = React.useState(function () {\n      uid += 1;\n      return String(uid);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  var groupContext = React.useContext(PreviewGroupContext);\n  var registerData = {\n    data: data,\n    canPreview: canPreview\n  };\n\n  // Keep order start\n  // Resolve https://github.com/ant-design/ant-design/issues/28881\n  // Only need unRegister when component unMount\n  React.useEffect(function () {\n    if (groupContext) {\n      return groupContext.register(id, registerData);\n    }\n  }, []);\n  React.useEffect(function () {\n    if (groupContext) {\n      groupContext.register(id, registerData);\n    }\n  }, [canPreview, data]);\n  return id;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}