{"ast": null, "code": "export function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  var mergedValue = value || 'value';\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: mergedValue,\n    key: mergedValue,\n    children: children || 'children'\n  };\n}\nexport function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n}\n\n/** Loop fetch all the keys exist in the tree */\nexport function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  }\n  dig(treeData);\n  return keys;\n}\nexport function isNil(val) {\n  return val === null || val === undefined;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}