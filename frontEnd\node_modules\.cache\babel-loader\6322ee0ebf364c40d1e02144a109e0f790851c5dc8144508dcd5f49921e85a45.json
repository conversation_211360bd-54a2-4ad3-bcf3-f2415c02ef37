{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useHover() {\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    startRow = _React$useState2[0],\n    setStartRow = _React$useState2[1];\n  var _React$useState3 = React.useState(-1),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    endRow = _React$useState4[0],\n    setEndRow = _React$useState4[1];\n  var onHover = React.useCallback(function (start, end) {\n    setStartRow(start);\n    setEndRow(end);\n  }, []);\n  return [startRow, endRow, onHover];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}