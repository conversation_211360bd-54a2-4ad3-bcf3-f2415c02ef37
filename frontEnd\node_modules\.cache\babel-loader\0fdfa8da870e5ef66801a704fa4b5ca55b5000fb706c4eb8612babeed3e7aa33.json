{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport MonthHeader from \"./MonthHeader\";\nimport MonthBody, { MONTH_COL_COUNT } from \"./MonthBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nfunction MonthPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-month-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff * MONTH_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange('date', value || viewDate);\n        }\n      });\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(MonthHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(MonthBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      _onSelect(date, 'mouse');\n      onPanelChange('date', date);\n    }\n  })));\n}\nexport default MonthPanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}