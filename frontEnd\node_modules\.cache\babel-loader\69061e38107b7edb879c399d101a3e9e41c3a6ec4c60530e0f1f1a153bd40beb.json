{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nvar MIN_SIZE = 20;\nfunction getPageY(e) {\n  return 'touches' in e ? e.touches[0].pageY : e.pageY;\n}\nvar ScrollBar = /*#__PURE__*/function (_React$Component) {\n  _inherits(ScrollBar, _React$Component);\n  var _super = _createSuper(ScrollBar);\n  function ScrollBar() {\n    var _this;\n    _classCallCheck(this, ScrollBar);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.moveRaf = null;\n    _this.scrollbarRef = /*#__PURE__*/React.createRef();\n    _this.thumbRef = /*#__PURE__*/React.createRef();\n    _this.visibleTimeout = null;\n    _this.state = {\n      dragging: false,\n      pageY: null,\n      startTop: null,\n      visible: false\n    };\n    _this.delayHidden = function () {\n      clearTimeout(_this.visibleTimeout);\n      _this.setState({\n        visible: true\n      });\n      _this.visibleTimeout = setTimeout(function () {\n        _this.setState({\n          visible: false\n        });\n      }, 2000);\n    };\n    _this.onScrollbarTouchStart = function (e) {\n      e.preventDefault();\n    };\n    _this.onContainerMouseDown = function (e) {\n      e.stopPropagation();\n      e.preventDefault();\n    };\n    // ======================= Clean =======================\n    _this.patchEvents = function () {\n      window.addEventListener('mousemove', _this.onMouseMove);\n      window.addEventListener('mouseup', _this.onMouseUp);\n      _this.thumbRef.current.addEventListener('touchmove', _this.onMouseMove);\n      _this.thumbRef.current.addEventListener('touchend', _this.onMouseUp);\n    };\n    _this.removeEvents = function () {\n      window.removeEventListener('mousemove', _this.onMouseMove);\n      window.removeEventListener('mouseup', _this.onMouseUp);\n      if (_this.thumbRef.current) {\n        _this.thumbRef.current.removeEventListener('touchmove', _this.onMouseMove);\n        _this.thumbRef.current.removeEventListener('touchend', _this.onMouseUp);\n      }\n      raf.cancel(_this.moveRaf);\n    };\n    // ======================= Thumb =======================\n    _this.onMouseDown = function (e) {\n      var onStartMove = _this.props.onStartMove;\n      _this.setState({\n        dragging: true,\n        pageY: getPageY(e),\n        startTop: _this.getTop()\n      });\n      onStartMove();\n      _this.patchEvents();\n      e.stopPropagation();\n      e.preventDefault();\n    };\n    _this.onMouseMove = function (e) {\n      var _this$state = _this.state,\n        dragging = _this$state.dragging,\n        pageY = _this$state.pageY,\n        startTop = _this$state.startTop;\n      var onScroll = _this.props.onScroll;\n      raf.cancel(_this.moveRaf);\n      if (dragging) {\n        var offsetY = getPageY(e) - pageY;\n        var newTop = startTop + offsetY;\n        var enableScrollRange = _this.getEnableScrollRange();\n        var enableHeightRange = _this.getEnableHeightRange();\n        var ptg = enableHeightRange ? newTop / enableHeightRange : 0;\n        var newScrollTop = Math.ceil(ptg * enableScrollRange);\n        _this.moveRaf = raf(function () {\n          onScroll(newScrollTop);\n        });\n      }\n    };\n    _this.onMouseUp = function () {\n      var onStopMove = _this.props.onStopMove;\n      _this.setState({\n        dragging: false\n      });\n      onStopMove();\n      _this.removeEvents();\n    };\n    // ===================== Calculate =====================\n    _this.getSpinHeight = function () {\n      var _this$props = _this.props,\n        height = _this$props.height,\n        count = _this$props.count;\n      var baseHeight = height / count * 10;\n      baseHeight = Math.max(baseHeight, MIN_SIZE);\n      baseHeight = Math.min(baseHeight, height / 2);\n      return Math.floor(baseHeight);\n    };\n    _this.getEnableScrollRange = function () {\n      var _this$props2 = _this.props,\n        scrollHeight = _this$props2.scrollHeight,\n        height = _this$props2.height;\n      return scrollHeight - height || 0;\n    };\n    _this.getEnableHeightRange = function () {\n      var height = _this.props.height;\n      var spinHeight = _this.getSpinHeight();\n      return height - spinHeight || 0;\n    };\n    _this.getTop = function () {\n      var scrollTop = _this.props.scrollTop;\n      var enableScrollRange = _this.getEnableScrollRange();\n      var enableHeightRange = _this.getEnableHeightRange();\n      if (scrollTop === 0 || enableScrollRange === 0) {\n        return 0;\n      }\n      var ptg = scrollTop / enableScrollRange;\n      return ptg * enableHeightRange;\n    };\n    // Not show scrollbar when height is large than scrollHeight\n    _this.showScroll = function () {\n      var _this$props3 = _this.props,\n        height = _this$props3.height,\n        scrollHeight = _this$props3.scrollHeight;\n      return scrollHeight > height;\n    };\n    return _this;\n  }\n  _createClass(ScrollBar, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.scrollbarRef.current.addEventListener('touchstart', this.onScrollbarTouchStart);\n      this.thumbRef.current.addEventListener('touchstart', this.onMouseDown);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      if (prevProps.scrollTop !== this.props.scrollTop) {\n        this.delayHidden();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$scrollbarRef$cu, _this$thumbRef$curren;\n      this.removeEvents();\n      (_this$scrollbarRef$cu = this.scrollbarRef.current) === null || _this$scrollbarRef$cu === void 0 ? void 0 : _this$scrollbarRef$cu.removeEventListener('touchstart', this.onScrollbarTouchStart);\n      (_this$thumbRef$curren = this.thumbRef.current) === null || _this$thumbRef$curren === void 0 ? void 0 : _this$thumbRef$curren.removeEventListener('touchstart', this.onMouseDown);\n      clearTimeout(this.visibleTimeout);\n    }\n  }, {\n    key: \"render\",\n    value:\n    // ====================== Render =======================\n    function render() {\n      var _this$state2 = this.state,\n        dragging = _this$state2.dragging,\n        visible = _this$state2.visible;\n      var _this$props4 = this.props,\n        prefixCls = _this$props4.prefixCls,\n        direction = _this$props4.direction;\n      var spinHeight = this.getSpinHeight();\n      var top = this.getTop();\n      var canScroll = this.showScroll();\n      var mergedVisible = canScroll && visible;\n      var scrollBarDirection = direction === 'rtl' ? {\n        left: 0\n      } : {\n        right: 0\n      };\n      return /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.scrollbarRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-show\"), canScroll)),\n        style: _objectSpread(_objectSpread({\n          width: 8,\n          top: 0,\n          bottom: 0\n        }, scrollBarDirection), {}, {\n          position: 'absolute',\n          display: mergedVisible ? null : 'none'\n        }),\n        onMouseDown: this.onContainerMouseDown,\n        onMouseMove: this.delayHidden\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: this.thumbRef,\n        className: classNames(\"\".concat(prefixCls, \"-scrollbar-thumb\"), _defineProperty({}, \"\".concat(prefixCls, \"-scrollbar-thumb-moving\"), dragging)),\n        style: {\n          width: '100%',\n          height: spinHeight,\n          top: top,\n          left: 0,\n          position: 'absolute',\n          background: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 99,\n          cursor: 'pointer',\n          userSelect: 'none'\n        },\n        onMouseDown: this.onMouseDown\n      }));\n    }\n  }]);\n  return ScrollBar;\n}(React.Component);\nexport { ScrollBar as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}