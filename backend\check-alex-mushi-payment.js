const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const axios = require('axios');
require('dotenv').config();

async function checkAlexMushiPayment() {
  try {
    console.log('🔍 Checking alex.mushi payment status...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Find alex.mushi user
    const alexUser = await User.findOne({ username: 'alex.mushi' });
    
    if (!alexUser) {
      console.log('❌ User alex.mushi not found');
      return;
    }
    
    console.log('👤 User Found:');
    console.log(`   Name: ${alexUser.firstName} ${alexUser.lastName}`);
    console.log(`   Username: ${alexUser.username}`);
    console.log(`   Email: ${alexUser.email}`);
    console.log(`   Phone: ${alexUser.phoneNumber}`);
    console.log(`   Subscription Status: ${alexUser.subscriptionStatus}`);
    console.log(`   Payment Required: ${alexUser.paymentRequired}`);
    console.log(`   Subscription End Date: ${alexUser.subscriptionEndDate}`);
    console.log('');
    
    // Find all subscriptions for alex.mushi
    const subscriptions = await Subscription.find({ user: alexUser._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📋 Found ${subscriptions.length} subscription(s):\n`);
    
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      console.log(`Subscription ${i + 1}:`);
      console.log(`   ID: ${sub._id}`);
      console.log(`   Plan: ${sub.activePlan?.title || 'Unknown'}`);
      console.log(`   Payment Status: ${sub.paymentStatus}`);
      console.log(`   Subscription Status: ${sub.status}`);
      console.log(`   Start Date: ${sub.startDate}`);
      console.log(`   End Date: ${sub.endDate}`);
      console.log(`   Created: ${sub.createdAt}`);
      console.log(`   Updated: ${sub.updatedAt}`);
      
      // Check payment history
      if (sub.paymentHistory && sub.paymentHistory.length > 0) {
        console.log(`   Payment History:`);
        sub.paymentHistory.forEach((payment, idx) => {
          console.log(`     Payment ${idx + 1}:`);
          console.log(`       Order ID: ${payment.orderId}`);
          console.log(`       Amount: ${payment.amount}`);
          console.log(`       Payment Status: ${payment.paymentStatus}`);
          console.log(`       Payment Date: ${payment.paymentDate}`);
          console.log(`       Reference ID: ${payment.referenceId}`);
          
          // Check if this payment was actually confirmed with ZenoPay
          if (payment.orderId && payment.orderId.startsWith('ORDER_')) {
            checkZenoPayStatus(payment.orderId);
          }
        });
      } else {
        console.log(`   ⚠️ No payment history found`);
      }
      console.log('');
    }
    
    // Check for any auto-activation issues
    const recentSubs = subscriptions.filter(sub => {
      const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
      const hoursAgo = timeSinceCreated / (1000 * 60 * 60);
      return hoursAgo < 48; // Created within last 48 hours
    });
    
    if (recentSubs.length > 0) {
      console.log('🚨 POTENTIAL ISSUE DETECTED:');
      console.log(`   Found ${recentSubs.length} recent subscription(s) that may have been auto-activated`);
      
      recentSubs.forEach((sub, idx) => {
        const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
        const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));
        
        console.log(`   Recent Sub ${idx + 1}: Created ${hoursAgo}h ago`);
        console.log(`     Status: ${sub.paymentStatus}/${sub.status}`);
        
        if (sub.paymentStatus === 'paid' && sub.status === 'active') {
          console.log(`     ⚠️ This subscription was activated - checking if payment was actually confirmed...`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

async function checkZenoPayStatus(orderId) {
  try {
    console.log(`       🔍 Checking ZenoPay status for ${orderId}...`);
    
    const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
    const response = await axios.get(statusUrl, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY,
      },
      timeout: 15000
    });
    
    if (response.data && response.data.data && response.data.data.length > 0) {
      const paymentData = response.data.data[0];
      console.log(`       📊 ZenoPay Status: ${paymentData.payment_status}`);
      
      if (paymentData.payment_status !== 'COMPLETED') {
        console.log(`       🚨 ISSUE: Payment not completed but subscription was activated!`);
      }
    } else {
      console.log(`       ⚠️ No payment data found in ZenoPay`);
    }
    
  } catch (error) {
    console.log(`       ❌ Error checking ZenoPay: ${error.message}`);
  }
}

// Run the check
checkAlexMushiPayment();
