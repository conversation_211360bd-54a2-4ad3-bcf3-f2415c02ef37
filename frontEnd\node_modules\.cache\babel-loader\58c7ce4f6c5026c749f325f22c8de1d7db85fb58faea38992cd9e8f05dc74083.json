{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport DecadeHeader from \"./DecadeHeader\";\nimport DecadeBody, { DECADE_COL_COUNT } from \"./DecadeBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nimport { DECADE_DISTANCE_COUNT, DECADE_UNIT_DIFF } from \"./constant\";\nexport { DECADE_DISTANCE_COUNT, DECADE_UNIT_DIFF };\nfunction DecadePanel(props) {\n  var prefixCls = props.prefixCls,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    operationRef = props.operationRef,\n    onSelect = props.onSelect,\n    onPanelChange = props.onPanelChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-decade-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          onSelect(generateConfig.addYear(viewDate, diff * DECADE_UNIT_DIFF), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          onSelect(generateConfig.addYear(viewDate, diff * DECADE_DISTANCE_COUNT), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          onSelect(generateConfig.addYear(viewDate, diff * DECADE_UNIT_DIFF * DECADE_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange('year', viewDate);\n        }\n      });\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onDecadesChange = function onDecadesChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff * DECADE_DISTANCE_COUNT);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  var onInternalSelect = function onInternalSelect(date) {\n    onSelect(date, 'mouse');\n    onPanelChange('year', date);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(DecadeHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevDecades: function onPrevDecades() {\n      onDecadesChange(-1);\n    },\n    onNextDecades: function onNextDecades() {\n      onDecadesChange(1);\n    }\n  })), /*#__PURE__*/React.createElement(DecadeBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: onInternalSelect\n  })));\n}\nexport default DecadePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}