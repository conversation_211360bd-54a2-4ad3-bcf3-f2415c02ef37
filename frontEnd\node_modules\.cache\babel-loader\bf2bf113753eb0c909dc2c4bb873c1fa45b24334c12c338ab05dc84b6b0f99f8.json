{"ast": null, "code": "import DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport DownloadOutlined from \"@ant-design/icons/es/icons/DownloadOutlined\";\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport { ConfigContext } from '../../config-provider';\nimport Progress from '../../progress';\nimport Tooltip from '../../tooltip';\nconst ListItem = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n    prefixCls,\n    className,\n    style,\n    locale,\n    listType,\n    file,\n    items,\n    progress: progressProps,\n    iconRender,\n    actionIconRender,\n    itemRender,\n    isImgUrl,\n    showPreviewIcon,\n    showRemoveIcon,\n    showDownloadIcon,\n    previewIcon: customPreviewIcon,\n    removeIcon: customRemoveIcon,\n    downloadIcon: customDownloadIcon,\n    onPreview,\n    onDownload,\n    onClose\n  } = _ref;\n  var _a, _b;\n  // Status: which will ignore `removed` status\n  const {\n    status\n  } = file;\n  const [mergedStatus, setMergedStatus] = React.useState(status);\n  React.useEffect(() => {\n    if (status !== 'removed') {\n      setMergedStatus(status);\n    }\n  }, [status]);\n  // Delay to show the progress bar\n  const [showProgress, setShowProgress] = React.useState(false);\n  React.useEffect(() => {\n    const timer = setTimeout(() => {\n      setShowProgress(true);\n    }, 300);\n    return () => {\n      clearTimeout(timer);\n    };\n  }, []);\n  const iconNode = iconRender(file);\n  let icon = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-icon`\n  }, iconNode);\n  if (listType === 'picture' || listType === 'picture-card' || listType === 'picture-circle') {\n    if (mergedStatus === 'uploading' || !file.thumbUrl && !file.url) {\n      const uploadingClassName = classNames(`${prefixCls}-list-item-thumbnail`, {\n        [`${prefixCls}-list-item-file`]: mergedStatus !== 'uploading'\n      });\n      icon = /*#__PURE__*/React.createElement(\"div\", {\n        className: uploadingClassName\n      }, iconNode);\n    } else {\n      const thumbnail = (isImgUrl === null || isImgUrl === void 0 ? void 0 : isImgUrl(file)) ? /*#__PURE__*/React.createElement(\"img\", {\n        src: file.thumbUrl || file.url,\n        alt: file.name,\n        className: `${prefixCls}-list-item-image`,\n        crossOrigin: file.crossOrigin\n      }) : iconNode;\n      const aClassName = classNames(`${prefixCls}-list-item-thumbnail`, {\n        [`${prefixCls}-list-item-file`]: isImgUrl && !isImgUrl(file)\n      });\n      icon = /*#__PURE__*/React.createElement(\"a\", {\n        className: aClassName,\n        onClick: e => onPreview(file, e),\n        href: file.url || file.thumbUrl,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\"\n      }, thumbnail);\n    }\n  }\n  const listItemClassName = classNames(`${prefixCls}-list-item`, `${prefixCls}-list-item-${mergedStatus}`);\n  const linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n  const removeIcon = showRemoveIcon ? actionIconRender((typeof customRemoveIcon === 'function' ? customRemoveIcon(file) : customRemoveIcon) || /*#__PURE__*/React.createElement(DeleteOutlined, null), () => onClose(file), prefixCls, locale.removeFile) : null;\n  const downloadIcon = showDownloadIcon && mergedStatus === 'done' ? actionIconRender((typeof customDownloadIcon === 'function' ? customDownloadIcon(file) : customDownloadIcon) || /*#__PURE__*/React.createElement(DownloadOutlined, null), () => onDownload(file), prefixCls, locale.downloadFile) : null;\n  const downloadOrDelete = listType !== 'picture-card' && listType !== 'picture-circle' && /*#__PURE__*/React.createElement(\"span\", {\n    key: \"download-delete\",\n    className: classNames(`${prefixCls}-list-item-actions`, {\n      picture: listType === 'picture'\n    })\n  }, downloadIcon, removeIcon);\n  const listItemNameClass = classNames(`${prefixCls}-list-item-name`);\n  const fileName = file.url ? [/*#__PURE__*/React.createElement(\"a\", Object.assign({\n    key: \"view\",\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    className: listItemNameClass,\n    title: file.name\n  }, linkProps, {\n    href: file.url,\n    onClick: e => onPreview(file, e)\n  }), file.name), downloadOrDelete] : [/*#__PURE__*/React.createElement(\"span\", {\n    key: \"view\",\n    className: listItemNameClass,\n    onClick: e => onPreview(file, e),\n    title: file.name\n  }, file.name), downloadOrDelete];\n  const previewStyle = {\n    pointerEvents: 'none',\n    opacity: 0.5\n  };\n  const previewIcon = showPreviewIcon ? /*#__PURE__*/React.createElement(\"a\", {\n    href: file.url || file.thumbUrl,\n    target: \"_blank\",\n    rel: \"noopener noreferrer\",\n    style: file.url || file.thumbUrl ? undefined : previewStyle,\n    onClick: e => onPreview(file, e),\n    title: locale.previewFile\n  }, typeof customPreviewIcon === 'function' ? customPreviewIcon(file) : customPreviewIcon || /*#__PURE__*/React.createElement(EyeOutlined, null)) : null;\n  const pictureCardActions = (listType === 'picture-card' || listType === 'picture-circle') && mergedStatus !== 'uploading' && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-list-item-actions`\n  }, previewIcon, mergedStatus === 'done' && downloadIcon, removeIcon);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const rootPrefixCls = getPrefixCls();\n  const dom = /*#__PURE__*/React.createElement(\"div\", {\n    className: listItemClassName\n  }, icon, fileName, pictureCardActions, showProgress && /*#__PURE__*/React.createElement(CSSMotion, {\n    motionName: `${rootPrefixCls}-fade`,\n    visible: mergedStatus === 'uploading',\n    motionDeadline: 2000\n  }, _ref2 => {\n    let {\n      className: motionClassName\n    } = _ref2;\n    // show loading icon if upload progress listener is disabled\n    const loadingProgress = 'percent' in file ? /*#__PURE__*/React.createElement(Progress, Object.assign({}, progressProps, {\n      type: \"line\",\n      percent: file.percent,\n      \"aria-label\": file['aria-label'],\n      \"aria-labelledby\": file['aria-labelledby']\n    })) : null;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: classNames(`${prefixCls}-list-item-progress`, motionClassName)\n    }, loadingProgress);\n  }));\n  const message = file.response && typeof file.response === 'string' ? file.response : ((_a = file.error) === null || _a === void 0 ? void 0 : _a.statusText) || ((_b = file.error) === null || _b === void 0 ? void 0 : _b.message) || locale.uploadError;\n  const item = mergedStatus === 'error' ? /*#__PURE__*/React.createElement(Tooltip, {\n    title: message,\n    getPopupContainer: node => node.parentNode\n  }, dom) : dom;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-list-item-container`, className),\n    style: style,\n    ref: ref\n  }, itemRender ? itemRender(item, file, items, {\n    download: onDownload.bind(null, file),\n    preview: onPreview.bind(null, file),\n    remove: onClose.bind(null, file)\n  }) : item);\n});\nexport default ListItem;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}