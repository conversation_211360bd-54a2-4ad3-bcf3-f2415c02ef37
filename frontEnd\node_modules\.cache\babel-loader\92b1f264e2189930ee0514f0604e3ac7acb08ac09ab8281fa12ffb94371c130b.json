{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useMemo } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { isInViewPort } from \"../util\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nexport default function useTarget(target, open, gap, scrollIntoViewOptions) {\n  // ========================= Target =========================\n  // We trade `undefined` as not get target by function yet.\n  // `null` as empty target.\n  var _useState = useState(undefined),\n    _useState2 = _slicedToArray(_useState, 2),\n    targetElement = _useState2[0],\n    setTargetElement = _useState2[1];\n  useLayoutEffect(function () {\n    var nextElement = typeof target === 'function' ? target() : target;\n    setTargetElement(nextElement || null);\n  });\n\n  // ========================= Align ==========================\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    posInfo = _useState4[0],\n    setPosInfo = _useState4[1];\n  var updatePos = useEvent(function () {\n    if (targetElement) {\n      // Exist target element. We should scroll and get target position\n      if (!isInViewPort(targetElement) && open) {\n        targetElement.scrollIntoView(scrollIntoViewOptions);\n      }\n      var _targetElement$getBou = targetElement.getBoundingClientRect(),\n        left = _targetElement$getBou.left,\n        top = _targetElement$getBou.top,\n        width = _targetElement$getBou.width,\n        height = _targetElement$getBou.height;\n      var nextPosInfo = {\n        left: left,\n        top: top,\n        width: width,\n        height: height,\n        radius: 0\n      };\n      setPosInfo(function (origin) {\n        if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {\n          return nextPosInfo;\n        }\n        return origin;\n      });\n    } else {\n      // Not exist target which means we just show in center\n      setPosInfo(null);\n    }\n  });\n  useLayoutEffect(function () {\n    updatePos();\n    // update when window resize\n    window.addEventListener('resize', updatePos);\n    return function () {\n      window.removeEventListener('resize', updatePos);\n    };\n  }, [targetElement, open, updatePos]);\n\n  // ======================== PosInfo =========================\n  var mergedPosInfo = useMemo(function () {\n    if (!posInfo) {\n      return posInfo;\n    }\n    var gapOffset = (gap === null || gap === void 0 ? void 0 : gap.offset) || 6;\n    var gapRadius = (gap === null || gap === void 0 ? void 0 : gap.radius) || 2;\n    return {\n      left: posInfo.left - gapOffset,\n      top: posInfo.top - gapOffset,\n      width: posInfo.width + gapOffset * 2,\n      height: posInfo.height + gapOffset * 2,\n      radius: gapRadius\n    };\n  }, [posInfo, gap]);\n  return [mergedPosInfo, targetElement];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}