{"ast": null, "code": "import * as React from 'react';\n/** Similar with `useEffect` but only trigger after mounted */\nconst useUpdatedEffect = (callback, conditions) => {\n  const mountRef = React.useRef(false);\n  React.useEffect(() => {\n    if (mountRef.current) {\n      callback();\n    } else {\n      mountRef.current = true;\n    }\n  }, conditions);\n};\nexport default useUpdatedEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}