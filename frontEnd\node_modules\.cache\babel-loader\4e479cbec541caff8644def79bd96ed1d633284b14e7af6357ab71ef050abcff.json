{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport { STATUS_APPEAR, STATUS_ENTER, STATUS_LEAVE, STATUS_NONE, STEP_ACTIVE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useDomMotionEvents from \"./useDomMotionEvents\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useStepQueue, { DoStep, isActive, SkipStep } from \"./useStepQueue\";\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useState3 = useState(STATUS_NONE),\n    _useState4 = _slicedToArray(_useState3, 2),\n    status = _useState4[0],\n    setStatus = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    style = _useState6[0],\n    setStyle = _useState6[1];\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = useRef(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(STATUS_NONE, true);\n    setStyle(null, true);\n  }\n  function onInternalMotionEnd(event) {\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (status !== STATUS_NONE && currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  }\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    var _ref2, _ref3, _ref4;\n    switch (targetStatus) {\n      case STATUS_APPEAR:\n        return _ref2 = {}, _defineProperty(_ref2, STEP_PREPARE, onAppearPrepare), _defineProperty(_ref2, STEP_START, onAppearStart), _defineProperty(_ref2, STEP_ACTIVE, onAppearActive), _ref2;\n      case STATUS_ENTER:\n        return _ref3 = {}, _defineProperty(_ref3, STEP_PREPARE, onEnterPrepare), _defineProperty(_ref3, STEP_START, onEnterStart), _defineProperty(_ref3, STEP_ACTIVE, onEnterActive), _ref3;\n      case STATUS_LEAVE:\n        return _ref4 = {}, _defineProperty(_ref4, STEP_PREPARE, onLeavePrepare), _defineProperty(_ref4, STEP_START, onLeaveStart), _defineProperty(_ref4, STEP_ACTIVE, onLeaveActive), _ref4;\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = React.useMemo(function () {\n    return getEventHandlers(status);\n  }, [status]);\n  var _useStepQueue = useStepQueue(status, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  // Update with new status\n  useIsomorphicLayoutEffect(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(STATUS_NONE);\n    }\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  useEffect(function () {\n    if (\n    // Cancel appear\n    status === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    status === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    status === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = React.useRef(false);\n  useEffect(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && status === STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 ? void 0 : onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, status]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [status, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}