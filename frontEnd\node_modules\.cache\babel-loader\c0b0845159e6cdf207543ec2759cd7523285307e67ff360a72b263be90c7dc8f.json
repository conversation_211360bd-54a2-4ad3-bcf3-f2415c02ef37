{"ast": null, "code": "const genBorderedStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const tableBorder = `${token.lineWidth}px ${token.lineType} ${token.tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `-${paddingVertical}px -${paddingHorizontal + token.lineWidth}px`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `-${token.tablePaddingVertical}px -${token.tablePaddingHorizontal + token.lineWidth}px`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: token.lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                [`> th, > td`]: {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${token.lineWidth}px 0 ${token.lineWidth}px ${token.tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}