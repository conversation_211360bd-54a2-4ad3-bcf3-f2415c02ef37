{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { formatValue, isSameQuarter } from \"../../utils/dateUtil\";\nimport RangeContext from \"../../RangeContext\";\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport PanelBody from \"../PanelBody\";\nexport var QUARTER_COL_COUNT = 4;\nvar QUARTER_ROW_COUNT = 1;\nfunction QuarterBody(props) {\n  var prefixCls = props.prefixCls,\n    locale = props.locale,\n    value = props.value,\n    viewDate = props.viewDate,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: cellPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameQuarter(generateConfig, current, target);\n    },\n    isInView: function isInView() {\n      return true;\n    },\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addMonth(date, offset * 3);\n    }\n  });\n  var baseQuarter = generateConfig.setDate(generateConfig.setMonth(viewDate, 0), 1);\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      locale: locale,\n      today: generateConfig.getNow(),\n      type: 'quarter'\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: QUARTER_ROW_COUNT,\n    colNum: QUARTER_COL_COUNT,\n    baseDate: baseQuarter,\n    getCellNode: getCellNode,\n    getCellText: function getCellText(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: locale.quarterFormat || '[Q]Q',\n        generateConfig: generateConfig\n      });\n    },\n    getCellClassName: getCellClassName,\n    getCellDate: function getCellDate(date, offset) {\n      return generateConfig.addMonth(date, offset * 3);\n    },\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY-[Q]Q',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default QuarterBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}