{"ast": null, "code": "/* eslint-disable max-classes-per-file */\n\nimport BigIntDecimal from \"./BigIntDecimal\";\nimport NumberDecimal from \"./NumberDecimal\";\nimport { trimNumber } from \"./numberUtil\";\nimport { supportBigInt } from \"./supportUtil\";\n\n// Still support origin export\nexport { NumberDecimal, BigIntDecimal };\nexport default function getMiniDecimal(value) {\n  // We use BigInt here.\n  // Will fallback to Number if not support.\n  if (supportBigInt()) {\n    return new BigIntDecimal(value);\n  }\n  return new NumberDecimal(value);\n}\n\n/**\n * Align the logic of toFixed to around like 1.5 => 2.\n * If set `cutOnly`, will just remove the over decimal part.\n */\nexport function toFixed(numStr, separatorStr, precision) {\n  var cutOnly = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (numStr === '') {\n    return '';\n  }\n  var _trimNumber = trimNumber(numStr),\n    negativeStr = _trimNumber.negativeStr,\n    integerStr = _trimNumber.integerStr,\n    decimalStr = _trimNumber.decimalStr;\n  var precisionDecimalStr = \"\".concat(separatorStr).concat(decimalStr);\n  var numberWithoutDecimal = \"\".concat(negativeStr).concat(integerStr);\n  if (precision >= 0) {\n    // We will get last + 1 number to check if need advanced number\n    var advancedNum = Number(decimalStr[precision]);\n    if (advancedNum >= 5 && !cutOnly) {\n      var advancedDecimal = getMiniDecimal(numStr).add(\"\".concat(negativeStr, \"0.\").concat('0'.repeat(precision)).concat(10 - advancedNum));\n      return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);\n    }\n    if (precision === 0) {\n      return numberWithoutDecimal;\n    }\n    return \"\".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));\n  }\n  if (precisionDecimalStr === '.0') {\n    return numberWithoutDecimal;\n  }\n  return \"\".concat(numberWithoutDecimal).concat(precisionDecimalStr);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}