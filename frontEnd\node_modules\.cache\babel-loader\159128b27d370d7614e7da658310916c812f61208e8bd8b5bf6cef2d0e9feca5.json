{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport defaultLocale from '../locale/en_US';\nimport { useLocale } from '../locale';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nconst FormItemLabel = _ref => {\n  let {\n    prefixCls,\n    label,\n    htmlFor,\n    labelCol,\n    labelAlign,\n    colon,\n    required,\n    requiredMark,\n    tooltip\n  } = _ref;\n  var _a;\n  const [formLocale] = useLocale('Form');\n  const {\n    vertical,\n    labelAlign: contextLabelAlign,\n    labelCol: contextLabelCol,\n    labelWrap,\n    colon: contextColon\n  } = React.useContext(FormContext);\n  if (!label) {\n    return null;\n  }\n  const mergedLabelCol = labelCol || contextLabelCol || {};\n  const mergedLabelAlign = labelAlign || contextLabelAlign;\n  const labelClsBasic = `${prefixCls}-item-label`;\n  const labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && `${labelClsBasic}-left`, mergedLabelCol.className, {\n    [`${labelClsBasic}-wrap`]: !!labelWrap\n  });\n  let labelChildren = label;\n  // Keep label is original where there should have no colon\n  const computedColon = colon === true || contextColon !== false && colon !== false;\n  const haveColon = computedColon && !vertical;\n  // Remove duplicated user input colon\n  if (haveColon && typeof label === 'string' && label.trim() !== '') {\n    labelChildren = label.replace(/[:|：]\\s*$/, '');\n  }\n  // Tooltip\n  const tooltipProps = toTooltipProps(tooltip);\n  if (tooltipProps) {\n    const {\n        icon = /*#__PURE__*/React.createElement(QuestionCircleOutlined, null)\n      } = tooltipProps,\n      restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n    const tooltipNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n      className: `${prefixCls}-item-tooltip`,\n      title: ''\n    }));\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n  }\n  if (requiredMark === 'optional' && !required) {\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-item-optional`,\n      title: \"\"\n    }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n  }\n  const labelClassName = classNames({\n    [`${prefixCls}-item-required`]: required,\n    [`${prefixCls}-item-required-mark-optional`]: requiredMark === 'optional',\n    [`${prefixCls}-item-no-colon`]: !computedColon\n  });\n  return /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedLabelCol, {\n    className: labelColClassName\n  }), /*#__PURE__*/React.createElement(\"label\", {\n    htmlFor: htmlFor,\n    className: labelClassName,\n    title: typeof label === 'string' ? label : ''\n  }, labelChildren));\n};\nexport default FormItemLabel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}