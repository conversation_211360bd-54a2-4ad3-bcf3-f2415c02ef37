{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\n/* eslint-disable no-param-reassign */\nimport * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default function useScrollTo(containerRef, data, heights, itemHeight, getKey, collectHeight, syncScrollTop, triggerFlash) {\n  var scrollRef = React.useRef();\n  return function (arg) {\n    // When not argument provided, we think dev may want to show the scrollbar\n    if (arg === null || arg === undefined) {\n      triggerFlash();\n      return;\n    }\n    // Normal scroll logic\n    raf.cancel(scrollRef.current);\n    if (typeof arg === 'number') {\n      syncScrollTop(arg);\n    } else if (arg && _typeof(arg) === 'object') {\n      var index;\n      var align = arg.align;\n      if ('index' in arg) {\n        index = arg.index;\n      } else {\n        index = data.findIndex(function (item) {\n          return getKey(item) === arg.key;\n        });\n      }\n      var _arg$offset = arg.offset,\n        offset = _arg$offset === void 0 ? 0 : _arg$offset;\n      // We will retry 3 times in case dynamic height shaking\n      var syncScroll = function syncScroll(times, targetAlign) {\n        if (times < 0 || !containerRef.current) return;\n        var height = containerRef.current.clientHeight;\n        var needCollectHeight = false;\n        var newTargetAlign = targetAlign;\n        // Go to next frame if height not exist\n        if (height) {\n          var mergedAlign = targetAlign || align;\n          // Get top & bottom\n          var stackTop = 0;\n          var itemTop = 0;\n          var itemBottom = 0;\n          var maxLen = Math.min(data.length, index);\n          for (var i = 0; i <= maxLen; i += 1) {\n            var key = getKey(data[i]);\n            itemTop = stackTop;\n            var cacheHeight = heights.get(key);\n            itemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n            stackTop = itemBottom;\n            if (i === index && cacheHeight === undefined) {\n              needCollectHeight = true;\n            }\n          }\n          // Scroll to\n          var targetTop = null;\n          switch (mergedAlign) {\n            case 'top':\n              targetTop = itemTop - offset;\n              break;\n            case 'bottom':\n              targetTop = itemBottom - height + offset;\n              break;\n            default:\n              {\n                var scrollTop = containerRef.current.scrollTop;\n                var scrollBottom = scrollTop + height;\n                if (itemTop < scrollTop) {\n                  newTargetAlign = 'top';\n                } else if (itemBottom > scrollBottom) {\n                  newTargetAlign = 'bottom';\n                }\n              }\n          }\n          if (targetTop !== null && targetTop !== containerRef.current.scrollTop) {\n            syncScrollTop(targetTop);\n          }\n        }\n        // We will retry since element may not sync height as it described\n        scrollRef.current = raf(function () {\n          if (needCollectHeight) {\n            collectHeight();\n          }\n          syncScroll(times - 1, newTargetAlign);\n        }, 2); // Delay 2 to wait for List collect heights\n      };\n\n      syncScroll(3);\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}