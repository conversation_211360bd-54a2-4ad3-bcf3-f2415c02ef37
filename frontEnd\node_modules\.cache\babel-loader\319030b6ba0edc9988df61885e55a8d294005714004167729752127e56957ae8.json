{"ast": null, "code": "import * as React from 'react';\nexport default function useLazyKVMap(data, childrenColumnName, getRowKey) {\n  const mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      const kvMap = new Map();\n      /* eslint-disable no-inner-declarations */\n      function dig(records) {\n        records.forEach((record, index) => {\n          const rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && typeof record === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      /* eslint-enable */\n      dig(data);\n      mapCacheRef.current = {\n        data,\n        childrenColumnName,\n        kvMap,\n        getRowKey\n      };\n    }\n    return mapCacheRef.current.kvMap.get(key);\n  }\n  return [getRecordByKey];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}