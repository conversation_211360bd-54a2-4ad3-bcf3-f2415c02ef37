{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [rgbValue, setRgbValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setRgbValue(value);\n    }\n  }, [value]);\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    if (!value) {\n      setRgbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}