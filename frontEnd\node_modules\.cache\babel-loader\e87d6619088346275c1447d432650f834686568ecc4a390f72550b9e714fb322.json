{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from \"../TreeNode\";\nexport function convertChildrenToData(nodes) {\n  return toArray(nodes).map(function (node) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref = node,\n      key = _ref.key,\n      _ref$props = _ref.props,\n      children = _ref$props.children,\n      value = _ref$props.value,\n      restProps = _objectWithoutProperties(_ref$props, _excluded);\n    var data = _objectSpread({\n      key: key,\n      value: value\n    }, restProps);\n    var childData = convertChildrenToData(children);\n    if (childData.length) {\n      data.children = childData;\n    }\n    return data;\n  }).filter(function (data) {\n    return data;\n  });\n}\nexport function fillLegacyProps(dataNode) {\n  if (!dataNode) {\n    return dataNode;\n  }\n  var cloneNode = _objectSpread({}, dataNode);\n  if (!('props' in cloneNode)) {\n    Object.defineProperty(cloneNode, 'props', {\n      get: function get() {\n        warning(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');\n        return cloneNode;\n      }\n    });\n  }\n  return cloneNode;\n}\nexport function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {\n  var triggerNode = null;\n  var nodeList = null;\n  function generateMap() {\n    function dig(list) {\n      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';\n      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      return list.map(function (option, index) {\n        var pos = \"\".concat(level, \"-\").concat(index);\n        var value = option[fieldNames.value];\n        var included = checkedValues.includes(value);\n        var children = dig(option[fieldNames.children] || [], pos, included);\n        var node = /*#__PURE__*/React.createElement(TreeNode, option, children.map(function (child) {\n          return child.node;\n        }));\n\n        // Link with trigger node\n        if (triggerValue === value) {\n          triggerNode = node;\n        }\n        if (included) {\n          var checkedNode = {\n            pos: pos,\n            node: node,\n            children: children\n          };\n          if (!parentIncluded) {\n            nodeList.push(checkedNode);\n          }\n          return checkedNode;\n        }\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n    if (!nodeList) {\n      nodeList = [];\n      dig(treeData);\n\n      // Sort to keep the checked node length\n      nodeList.sort(function (_ref2, _ref3) {\n        var val1 = _ref2.node.props.value;\n        var val2 = _ref3.node.props.value;\n        var index1 = checkedValues.indexOf(val1);\n        var index2 = checkedValues.indexOf(val2);\n        return index1 - index2;\n      });\n    }\n  }\n  Object.defineProperty(extra, 'triggerNode', {\n    get: function get() {\n      warning(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      return triggerNode;\n    }\n  });\n  Object.defineProperty(extra, 'allCheckedNodes', {\n    get: function get() {\n      warning(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      if (showPosition) {\n        return nodeList;\n      }\n      return nodeList.map(function (_ref4) {\n        var node = _ref4.node;\n        return node;\n      });\n    }\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}