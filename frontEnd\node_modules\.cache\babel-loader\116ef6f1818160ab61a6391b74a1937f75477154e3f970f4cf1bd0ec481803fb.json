{"ast": null, "code": "import React, { forwardRef } from 'react';\nvar Transform = /*#__PURE__*/forwardRef(function (props, ref) {\n  var children = props.children,\n    offset = props.offset;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: ref,\n    style: {\n      position: 'absolute',\n      left: offset.x,\n      top: offset.y,\n      zIndex: 1\n    }\n  }, children);\n});\nexport default Transform;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}