{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { WEEK_DAY_COUNT } from \"../../utils/dateUtil\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nimport DateBody from \"./DateBody\";\nimport DateHeader from \"./DateHeader\";\nvar DATE_ROW_COUNT = 6;\nfunction DatePanel(props) {\n  var prefixCls = props.prefixCls,\n    _props$panelName = props.panelName,\n    panelName = _props$panelName === void 0 ? 'date' : _props$panelName,\n    keyboardConfig = props.keyboardConfig,\n    active = props.active,\n    operationRef = props.operationRef,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    onViewDateChange = props.onViewDateChange,\n    onPanelChange = props.onPanelChange,\n    _onSelect = props.onSelect;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-\").concat(panelName, \"-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, _objectSpread({\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addDate(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addDate(value || viewDate, diff * WEEK_DAY_COUNT), 'key');\n        },\n        onPageUpDown: function onPageUpDown(diff) {\n          _onSelect(generateConfig.addMonth(value || viewDate, diff), 'key');\n        }\n      }, keyboardConfig));\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onYearChange = function onYearChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  var onMonthChange = function onMonthChange(diff) {\n    var newDate = generateConfig.addMonth(viewDate, diff);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls, _defineProperty({}, \"\".concat(panelPrefixCls, \"-active\"), active))\n  }, /*#__PURE__*/React.createElement(DateHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    value: value,\n    viewDate: viewDate\n    // View Operation\n    ,\n\n    onPrevYear: function onPrevYear() {\n      onYearChange(-1);\n    },\n    onNextYear: function onNextYear() {\n      onYearChange(1);\n    },\n    onPrevMonth: function onPrevMonth() {\n      onMonthChange(-1);\n    },\n    onNextMonth: function onNextMonth() {\n      onMonthChange(1);\n    },\n    onMonthClick: function onMonthClick() {\n      onPanelChange('month', viewDate);\n    },\n    onYearClick: function onYearClick() {\n      onPanelChange('year', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(DateBody, _extends({}, props, {\n    onSelect: function onSelect(date) {\n      return _onSelect(date, 'mouse');\n    },\n    prefixCls: prefixCls,\n    value: value,\n    viewDate: viewDate,\n    rowCount: DATE_ROW_COUNT\n  })));\n}\nexport default DatePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}