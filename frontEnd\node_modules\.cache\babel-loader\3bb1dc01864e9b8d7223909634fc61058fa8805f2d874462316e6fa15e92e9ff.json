{"ast": null, "code": "import { blue } from '@ant-design/colors';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { clearFix, textEllipsis } from '../../style';\nconst genPictureStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    uploadThumbnailSize,\n    uploadProgressOffset\n  } = token;\n  const listCls = `${componentCls}-list`;\n  const itemCls = `${listCls}-item`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ${listCls} 增加优先级\n      [`\n        ${listCls}${listCls}-picture,\n        ${listCls}${listCls}-picture-card,\n        ${listCls}${listCls}-picture-circle\n      `]: {\n        [itemCls]: {\n          position: 'relative',\n          height: uploadThumbnailSize + token.lineWidth * 2 + token.paddingXS * 2,\n          padding: token.paddingXS,\n          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,\n          borderRadius: token.borderRadiusLG,\n          '&:hover': {\n            background: 'transparent'\n          },\n          [`${itemCls}-thumbnail`]: Object.assign(Object.assign({}, textEllipsis), {\n            width: uploadThumbnailSize,\n            height: uploadThumbnailSize,\n            lineHeight: `${uploadThumbnailSize + token.paddingSM}px`,\n            textAlign: 'center',\n            flex: 'none',\n            [iconCls]: {\n              fontSize: token.fontSizeHeading2,\n              color: token.colorPrimary\n            },\n            img: {\n              display: 'block',\n              width: '100%',\n              height: '100%',\n              overflow: 'hidden'\n            }\n          }),\n          [`${itemCls}-progress`]: {\n            bottom: uploadProgressOffset,\n            width: `calc(100% - ${token.paddingSM * 2}px)`,\n            marginTop: 0,\n            paddingInlineStart: uploadThumbnailSize + token.paddingXS\n          }\n        },\n        [`${itemCls}-error`]: {\n          borderColor: token.colorError,\n          // Adjust the color of the error icon : https://github.com/ant-design/ant-design/pull/24160\n          [`${itemCls}-thumbnail ${iconCls}`]: {\n            [`svg path[fill='${blue[0]}']`]: {\n              fill: token.colorErrorBg\n            },\n            [`svg path[fill='${blue.primary}']`]: {\n              fill: token.colorError\n            }\n          }\n        },\n        [`${itemCls}-uploading`]: {\n          borderStyle: 'dashed',\n          [`${itemCls}-name`]: {\n            marginBottom: uploadProgressOffset\n          }\n        }\n      },\n      [`${listCls}${listCls}-picture-circle ${itemCls}`]: {\n        [`&, &::before, ${itemCls}-thumbnail`]: {\n          borderRadius: '50%'\n        }\n      }\n    }\n  };\n};\nconst genPictureCardStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontSizeLG,\n    colorTextLightSolid\n  } = token;\n  const listCls = `${componentCls}-list`;\n  const itemCls = `${listCls}-item`;\n  const uploadPictureCardSize = token.uploadPicCardSize;\n  return {\n    [`\n      ${componentCls}-wrapper${componentCls}-picture-card-wrapper,\n      ${componentCls}-wrapper${componentCls}-picture-circle-wrapper\n    `]: Object.assign(Object.assign({}, clearFix()), {\n      display: 'inline-block',\n      width: '100%',\n      [`${componentCls}${componentCls}-select`]: {\n        width: uploadPictureCardSize,\n        height: uploadPictureCardSize,\n        marginInlineEnd: token.marginXS,\n        marginBottom: token.marginXS,\n        textAlign: 'center',\n        verticalAlign: 'top',\n        backgroundColor: token.colorFillAlter,\n        border: `${token.lineWidth}px dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [`> ${componentCls}`]: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        [`&:not(${componentCls}-disabled):hover`]: {\n          borderColor: token.colorPrimary\n        }\n      },\n      // list\n      [`${listCls}${listCls}-picture-card, ${listCls}${listCls}-picture-circle`]: {\n        [`${listCls}-item-container`]: {\n          display: 'inline-block',\n          width: uploadPictureCardSize,\n          height: uploadPictureCardSize,\n          marginBlock: `0 ${token.marginXS}px`,\n          marginInline: `0 ${token.marginXS}px`,\n          verticalAlign: 'top'\n        },\n        '&::after': {\n          display: 'none'\n        },\n        [itemCls]: {\n          height: '100%',\n          margin: 0,\n          '&::before': {\n            position: 'absolute',\n            zIndex: 1,\n            width: `calc(100% - ${token.paddingXS * 2}px)`,\n            height: `calc(100% - ${token.paddingXS * 2}px)`,\n            backgroundColor: token.colorBgMask,\n            opacity: 0,\n            transition: `all ${token.motionDurationSlow}`,\n            content: '\" \"'\n          }\n        },\n        [`${itemCls}:hover`]: {\n          [`&::before, ${itemCls}-actions`]: {\n            opacity: 1\n          }\n        },\n        [`${itemCls}-actions`]: {\n          position: 'absolute',\n          insetInlineStart: 0,\n          zIndex: 10,\n          width: '100%',\n          whiteSpace: 'nowrap',\n          textAlign: 'center',\n          opacity: 0,\n          transition: `all ${token.motionDurationSlow}`,\n          [`${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {\n            zIndex: 10,\n            width: fontSizeLG,\n            margin: `0 ${token.marginXXS}px`,\n            fontSize: fontSizeLG,\n            cursor: 'pointer',\n            transition: `all ${token.motionDurationSlow}`,\n            svg: {\n              verticalAlign: 'baseline'\n            }\n          }\n        },\n        [`${itemCls}-actions, ${itemCls}-actions:hover`]: {\n          [`${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {\n            color: new TinyColor(colorTextLightSolid).setAlpha(0.65).toRgbString(),\n            '&:hover': {\n              color: colorTextLightSolid\n            }\n          }\n        },\n        [`${itemCls}-thumbnail, ${itemCls}-thumbnail img`]: {\n          position: 'static',\n          display: 'block',\n          width: '100%',\n          height: '100%',\n          objectFit: 'contain'\n        },\n        [`${itemCls}-name`]: {\n          display: 'none',\n          textAlign: 'center'\n        },\n        [`${itemCls}-file + ${itemCls}-name`]: {\n          position: 'absolute',\n          bottom: token.margin,\n          display: 'block',\n          width: `calc(100% - ${token.paddingXS * 2}px)`\n        },\n        [`${itemCls}-uploading`]: {\n          [`&${itemCls}`]: {\n            backgroundColor: token.colorFillAlter\n          },\n          [`&::before, ${iconCls}-eye, ${iconCls}-download, ${iconCls}-delete`]: {\n            display: 'none'\n          }\n        },\n        [`${itemCls}-progress`]: {\n          bottom: token.marginXL,\n          width: `calc(100% - ${token.paddingXS * 2}px)`,\n          paddingInlineStart: 0\n        }\n      }\n    }),\n    [`${componentCls}-wrapper${componentCls}-picture-circle-wrapper`]: {\n      [`${componentCls}${componentCls}-select`]: {\n        borderRadius: '50%'\n      }\n    }\n  };\n};\nexport { genPictureStyle, genPictureCardStyle };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}