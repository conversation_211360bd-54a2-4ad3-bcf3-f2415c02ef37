{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport CaretDownOutlined from \"@ant-design/icons/es/icons/CaretDownOutlined\";\nimport CaretUpOutlined from \"@ant-design/icons/es/icons/CaretUpOutlined\";\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport Tooltip from '../../tooltip';\nimport { getColumnKey, getColumnPos, renderColumnTitle, safeColumnTitle } from '../util';\nconst ASCEND = 'ascend';\nconst DESCEND = 'descend';\nfunction getMultiplePriority(column) {\n  if (typeof column.sorter === 'object' && typeof column.sorter.multiple === 'number') {\n    return column.sorter.multiple;\n  }\n  return false;\n}\nfunction getSortFunction(sorter) {\n  if (typeof sorter === 'function') {\n    return sorter;\n  }\n  if (sorter && typeof sorter === 'object' && sorter.compare) {\n    return sorter.compare;\n  }\n  return false;\n}\nfunction nextSortDirection(sortDirections, current) {\n  if (!current) {\n    return sortDirections[0];\n  }\n  return sortDirections[sortDirections.indexOf(current) + 1];\n}\nfunction collectSortStates(columns, init, pos) {\n  let sortStates = [];\n  function pushState(column, columnPos) {\n    sortStates.push({\n      column,\n      key: getColumnKey(column, columnPos),\n      multiplePriority: getMultiplePriority(column),\n      sortOrder: column.sortOrder\n    });\n  }\n  (columns || []).forEach((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    if (column.children) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      }\n      sortStates = [].concat(_toConsumableArray(sortStates), _toConsumableArray(collectSortStates(column.children, init, columnPos)));\n    } else if (column.sorter) {\n      if ('sortOrder' in column) {\n        // Controlled\n        pushState(column, columnPos);\n      } else if (init && column.defaultSortOrder) {\n        // Default sorter\n        sortStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          multiplePriority: getMultiplePriority(column),\n          sortOrder: column.defaultSortOrder\n        });\n      }\n    }\n  });\n  return sortStates;\n}\nfunction injectSorter(prefixCls, columns, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, pos) {\n  return (columns || []).map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    let newColumn = column;\n    if (newColumn.sorter) {\n      const sortDirections = newColumn.sortDirections || defaultSortDirections;\n      const showSorterTooltip = newColumn.showSorterTooltip === undefined ? tableShowSorterTooltip : newColumn.showSorterTooltip;\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const sorterState = sorterStates.find(_ref => {\n        let {\n          key\n        } = _ref;\n        return key === columnKey;\n      });\n      const sortOrder = sorterState ? sorterState.sortOrder : null;\n      const nextSortOrder = nextSortDirection(sortDirections, sortOrder);\n      let sorter;\n      if (column.sortIcon) {\n        sorter = column.sortIcon({\n          sortOrder\n        });\n      } else {\n        const upNode = sortDirections.includes(ASCEND) && /*#__PURE__*/React.createElement(CaretUpOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-up`, {\n            active: sortOrder === ASCEND\n          })\n        });\n        const downNode = sortDirections.includes(DESCEND) && /*#__PURE__*/React.createElement(CaretDownOutlined, {\n          className: classNames(`${prefixCls}-column-sorter-down`, {\n            active: sortOrder === DESCEND\n          })\n        });\n        sorter = /*#__PURE__*/React.createElement(\"span\", {\n          className: classNames(`${prefixCls}-column-sorter`, {\n            [`${prefixCls}-column-sorter-full`]: !!(upNode && downNode)\n          })\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-column-sorter-inner`,\n          \"aria-hidden\": \"true\"\n        }, upNode, downNode));\n      }\n      const {\n        cancelSort,\n        triggerAsc,\n        triggerDesc\n      } = tableLocale || {};\n      let sortTip = cancelSort;\n      if (nextSortOrder === DESCEND) {\n        sortTip = triggerDesc;\n      } else if (nextSortOrder === ASCEND) {\n        sortTip = triggerAsc;\n      }\n      const tooltipProps = typeof showSorterTooltip === 'object' ? showSorterTooltip : {\n        title: sortTip\n      };\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        className: classNames(newColumn.className, {\n          [`${prefixCls}-column-sort`]: sortOrder\n        }),\n        title: renderProps => {\n          const renderSortTitle = /*#__PURE__*/React.createElement(\"div\", {\n            className: `${prefixCls}-column-sorters`\n          }, /*#__PURE__*/React.createElement(\"span\", {\n            className: `${prefixCls}-column-title`\n          }, renderColumnTitle(column.title, renderProps)), sorter);\n          return showSorterTooltip ? /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps), renderSortTitle) : renderSortTitle;\n        },\n        onHeaderCell: col => {\n          const cell = column.onHeaderCell && column.onHeaderCell(col) || {};\n          const originOnClick = cell.onClick;\n          const originOKeyDown = cell.onKeyDown;\n          cell.onClick = event => {\n            triggerSorter({\n              column,\n              key: columnKey,\n              sortOrder: nextSortOrder,\n              multiplePriority: getMultiplePriority(column)\n            });\n            originOnClick === null || originOnClick === void 0 ? void 0 : originOnClick(event);\n          };\n          cell.onKeyDown = event => {\n            if (event.keyCode === KeyCode.ENTER) {\n              triggerSorter({\n                column,\n                key: columnKey,\n                sortOrder: nextSortOrder,\n                multiplePriority: getMultiplePriority(column)\n              });\n              originOKeyDown === null || originOKeyDown === void 0 ? void 0 : originOKeyDown(event);\n            }\n          };\n          const renderTitle = safeColumnTitle(column.title, {});\n          const displayTitle = renderTitle === null || renderTitle === void 0 ? void 0 : renderTitle.toString();\n          // Inform the screen-reader so it can tell the visually impaired user which column is sorted\n          if (sortOrder) {\n            cell['aria-sort'] = sortOrder === 'ascend' ? 'ascending' : 'descending';\n          } else {\n            cell['aria-label'] = displayTitle || '';\n          }\n          cell.className = classNames(cell.className, `${prefixCls}-column-has-sorters`);\n          cell.tabIndex = 0;\n          if (column.ellipsis) {\n            cell.title = (renderTitle !== null && renderTitle !== void 0 ? renderTitle : '').toString();\n          }\n          return cell;\n        }\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectSorter(prefixCls, newColumn.children, sorterStates, triggerSorter, defaultSortDirections, tableLocale, tableShowSorterTooltip, columnPos)\n      });\n    }\n    return newColumn;\n  });\n}\nfunction stateToInfo(sorterStates) {\n  const {\n    column,\n    sortOrder\n  } = sorterStates;\n  return {\n    column,\n    order: sortOrder,\n    field: column.dataIndex,\n    columnKey: column.key\n  };\n}\nfunction generateSorterInfo(sorterStates) {\n  const list = sorterStates.filter(_ref2 => {\n    let {\n      sortOrder\n    } = _ref2;\n    return sortOrder;\n  }).map(stateToInfo);\n  // =========== Legacy compatible support ===========\n  // https://github.com/ant-design/ant-design/pull/19226\n  if (list.length === 0 && sorterStates.length) {\n    return Object.assign(Object.assign({}, stateToInfo(sorterStates[sorterStates.length - 1])), {\n      column: undefined\n    });\n  }\n  if (list.length <= 1) {\n    return list[0] || {};\n  }\n  return list;\n}\nexport function getSortData(data, sortStates, childrenColumnName) {\n  const innerSorterStates = sortStates.slice().sort((a, b) => b.multiplePriority - a.multiplePriority);\n  const cloneData = data.slice();\n  const runningSorters = innerSorterStates.filter(_ref3 => {\n    let {\n      column: {\n        sorter\n      },\n      sortOrder\n    } = _ref3;\n    return getSortFunction(sorter) && sortOrder;\n  });\n  // Skip if no sorter needed\n  if (!runningSorters.length) {\n    return cloneData;\n  }\n  return cloneData.sort((record1, record2) => {\n    for (let i = 0; i < runningSorters.length; i += 1) {\n      const sorterState = runningSorters[i];\n      const {\n        column: {\n          sorter\n        },\n        sortOrder\n      } = sorterState;\n      const compareFn = getSortFunction(sorter);\n      if (compareFn && sortOrder) {\n        const compareResult = compareFn(record1, record2, sortOrder);\n        if (compareResult !== 0) {\n          return sortOrder === ASCEND ? compareResult : -compareResult;\n        }\n      }\n    }\n    return 0;\n  }).map(record => {\n    const subRecords = record[childrenColumnName];\n    if (subRecords) {\n      return Object.assign(Object.assign({}, record), {\n        [childrenColumnName]: getSortData(subRecords, sortStates, childrenColumnName)\n      });\n    }\n    return record;\n  });\n}\nexport default function useFilterSorter(_ref4) {\n  let {\n    prefixCls,\n    mergedColumns,\n    onSorterChange,\n    sortDirections,\n    tableLocale,\n    showSorterTooltip\n  } = _ref4;\n  const [sortStates, setSortStates] = React.useState(collectSortStates(mergedColumns, true));\n  const mergedSorterStates = React.useMemo(() => {\n    let validate = true;\n    const collectedStates = collectSortStates(mergedColumns, false);\n    // Return if not controlled\n    if (!collectedStates.length) {\n      return sortStates;\n    }\n    const validateStates = [];\n    function patchStates(state) {\n      if (validate) {\n        validateStates.push(state);\n      } else {\n        validateStates.push(Object.assign(Object.assign({}, state), {\n          sortOrder: null\n        }));\n      }\n    }\n    let multipleMode = null;\n    collectedStates.forEach(state => {\n      if (multipleMode === null) {\n        patchStates(state);\n        if (state.sortOrder) {\n          if (state.multiplePriority === false) {\n            validate = false;\n          } else {\n            multipleMode = true;\n          }\n        }\n      } else if (multipleMode && state.multiplePriority !== false) {\n        patchStates(state);\n      } else {\n        validate = false;\n        patchStates(state);\n      }\n    });\n    return validateStates;\n  }, [mergedColumns, sortStates]);\n  // Get render columns title required props\n  const columnTitleSorterProps = React.useMemo(() => {\n    const sortColumns = mergedSorterStates.map(_ref5 => {\n      let {\n        column,\n        sortOrder\n      } = _ref5;\n      return {\n        column,\n        order: sortOrder\n      };\n    });\n    return {\n      sortColumns,\n      // Legacy\n      sortColumn: sortColumns[0] && sortColumns[0].column,\n      sortOrder: sortColumns[0] && sortColumns[0].order\n    };\n  }, [mergedSorterStates]);\n  function triggerSorter(sortState) {\n    let newSorterStates;\n    if (sortState.multiplePriority === false || !mergedSorterStates.length || mergedSorterStates[0].multiplePriority === false) {\n      newSorterStates = [sortState];\n    } else {\n      newSorterStates = [].concat(_toConsumableArray(mergedSorterStates.filter(_ref6 => {\n        let {\n          key\n        } = _ref6;\n        return key !== sortState.key;\n      })), [sortState]);\n    }\n    setSortStates(newSorterStates);\n    onSorterChange(generateSorterInfo(newSorterStates), newSorterStates);\n  }\n  const transformColumns = innerColumns => injectSorter(prefixCls, innerColumns, mergedSorterStates, triggerSorter, sortDirections, tableLocale, showSorterTooltip);\n  const getSorters = () => generateSorterInfo(mergedSorterStates);\n  return [transformColumns, mergedSorterStates, columnTitleSorterProps, getSorters];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}