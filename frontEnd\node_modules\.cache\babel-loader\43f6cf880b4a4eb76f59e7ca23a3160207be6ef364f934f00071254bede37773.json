{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { EXPAND_COLUMN } from \"../constant\";\nimport { INTERNAL_COL_DEFINE } from \"../utils/legacyUtil\";\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object';\n  }).reduce(function (list, column) {\n    var fixed = column.fixed;\n    // Convert `fixed='true'` to `fixed='left'` instead\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({}, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction warningFixed(flattenColumns) {\n  var allFixLeft = true;\n  for (var i = 0; i < flattenColumns.length; i += 1) {\n    var col = flattenColumns[i];\n    if (allFixLeft && col.fixed !== 'left') {\n      allFixLeft = false;\n    } else if (!allFixLeft && col.fixed === 'left') {\n      warning(false, \"Index \".concat(i - 1, \" of `columns` missing `fixed='left'` prop.\"));\n      break;\n    }\n  }\n  var allFixRight = true;\n  for (var _i = flattenColumns.length - 1; _i >= 0; _i -= 1) {\n    var _col = flattenColumns[_i];\n    if (allFixRight && _col.fixed !== 'right') {\n      allFixRight = false;\n    } else if (!allFixRight && _col.fixed === 'right') {\n      warning(false, \"Index \".concat(_i + 1, \" of `columns` missing `fixed='right'` prop.\"));\n      break;\n    }\n  }\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2);\n\n    // Convert `fixed='left'` to `fixed='right'` instead\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n\n/**\n * Parse `columns` & `children` into `columns`.\n */\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    columnTitle = _ref2.columnTitle,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed;\n  var baseColumns = React.useMemo(function () {\n    return columns || convertChildrenToColumns(children);\n  }, [columns, children]);\n\n  // ========================== Expand ==========================\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var _expandColumn;\n      var cloneColumns = baseColumns.slice();\n\n      // >>> Warning if use `expandIconColumnIndex`\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      }\n\n      // >>> Insert expand column if not exist\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n      }\n\n      // >>> Deduplicate additional expand column\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      });\n\n      // >>> Check if expand column need to fixed\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if ((fixed === 'left' || fixed) && !expandIconColumnIndex) {\n        fixedColumn = 'left';\n      } else if ((fixed === 'right' || fixed) && expandIconColumnIndex === baseColumns.length) {\n        fixedColumn = 'right';\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      }\n\n      // >>> Create expandable column\n      var expandColumn = (_expandColumn = {}, _defineProperty(_expandColumn, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), _defineProperty(_expandColumn, \"title\", columnTitle), _defineProperty(_expandColumn, \"fixed\", fixedColumn), _defineProperty(_expandColumn, \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), _defineProperty(_expandColumn, \"width\", columnWidth), _defineProperty(_expandColumn, \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      }), _expandColumn);\n      return cloneColumns.map(function (col) {\n        return col === EXPAND_COLUMN ? expandColumn : col;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction]);\n\n  // ========================= Transform ========================\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    }\n\n    // Always provides at least one column for table display\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n  }, [transformColumns, withExpandColumns, direction]);\n\n  // ========================== Flatten =========================\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n  }, [mergedColumns, direction]);\n  // Only check out of production since it's waste for each render\n  if (process.env.NODE_ENV !== 'production') {\n    warningFixed(direction === 'rtl' ? flattenColumns.slice().reverse() : flattenColumns);\n  }\n  return [mergedColumns, flattenColumns];\n}\nexport default useColumns;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}