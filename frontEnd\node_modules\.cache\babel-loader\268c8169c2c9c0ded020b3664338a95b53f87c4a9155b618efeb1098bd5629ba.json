{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nexport default function TabPanelList(_ref) {\n  var id = _ref.id,\n    activeKey = _ref.activeKey,\n    animated = _ref.animated,\n    tabPosition = _ref.tabPosition,\n    destroyInactiveTabPane = _ref.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (_ref2) {\n    var key = _ref2.key,\n      forceRender = _ref2.forceRender,\n      paneStyle = _ref2.style,\n      paneClassName = _ref2.className,\n      restTabProps = _objectWithoutProperties(_ref2, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!destroyInactiveTabPane,\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref3, ref) {\n      var motionStyle = _ref3.style,\n        motionClassName = _ref3.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}