{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport React from \"react\";\nexport function getClearIcon(prefixCls, allowClear, clearIcon) {\n  var mergedClearIcon = _typeof(allowClear) === \"object\" ? allowClear.clearIcon : clearIcon;\n  return mergedClearIcon || /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-clear-btn\")\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}