{"ast": null, "code": "import { operationUnit } from '../../style';\nconst genExpandStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    controlInteractiveSize: checkboxSize,\n    motionDurationSlow,\n    lineWidth,\n    paddingXS,\n    lineType,\n    tableBorderColor,\n    tableExpandIconBg,\n    tableExpandColumnWidth,\n    borderRadius,\n    fontSize,\n    fontSizeSM,\n    lineHeight,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    tableExpandedRowBg,\n    paddingXXS\n  } = token;\n  const halfInnerSize = checkboxSize / 2 - lineWidth;\n  // must be odd number, unless it cannot align center\n  const expandIconSize = halfInnerSize * 2 + lineWidth * 3;\n  const tableBorder = `${lineWidth}px ${lineType} ${tableBorderColor}`;\n  const expandIconLineOffset = paddingXXS - lineWidth;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-expand-icon-col`]: {\n        width: tableExpandColumnWidth\n      },\n      [`${componentCls}-row-expand-icon-cell`]: {\n        textAlign: 'center',\n        [`${componentCls}-row-expand-icon`]: {\n          display: 'inline-flex',\n          float: 'none',\n          verticalAlign: 'sub'\n        }\n      },\n      [`${componentCls}-row-indent`]: {\n        height: 1,\n        float: 'left'\n      },\n      [`${componentCls}-row-expand-icon`]: Object.assign(Object.assign({}, operationUnit(token)), {\n        position: 'relative',\n        float: 'left',\n        boxSizing: 'border-box',\n        width: expandIconSize,\n        height: expandIconSize,\n        padding: 0,\n        color: 'inherit',\n        lineHeight: `${expandIconSize}px`,\n        background: tableExpandIconBg,\n        border: tableBorder,\n        borderRadius,\n        transform: `scale(${checkboxSize / expandIconSize})`,\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:focus, &:hover, &:active`]: {\n          borderColor: 'currentcolor'\n        },\n        [`&::before, &::after`]: {\n          position: 'absolute',\n          background: 'currentcolor',\n          transition: `transform ${motionDurationSlow} ease-out`,\n          content: '\"\"'\n        },\n        '&::before': {\n          top: halfInnerSize,\n          insetInlineEnd: expandIconLineOffset,\n          insetInlineStart: expandIconLineOffset,\n          height: lineWidth\n        },\n        '&::after': {\n          top: expandIconLineOffset,\n          bottom: expandIconLineOffset,\n          insetInlineStart: halfInnerSize,\n          width: lineWidth,\n          transform: 'rotate(90deg)'\n        },\n        // Motion effect\n        '&-collapsed::before': {\n          transform: 'rotate(-180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        },\n        '&-spaced': {\n          '&::before, &::after': {\n            display: 'none',\n            content: 'none'\n          },\n          background: 'transparent',\n          border: 0,\n          visibility: 'hidden'\n        }\n      }),\n      [`${componentCls}-row-indent + ${componentCls}-row-expand-icon`]: {\n        marginTop: (fontSize * lineHeight - lineWidth * 3) / 2 - Math.ceil((fontSizeSM * 1.4 - lineWidth * 3) / 2),\n        marginInlineEnd: paddingXS\n      },\n      [`tr${componentCls}-expanded-row`]: {\n        '&, &:hover': {\n          [`> th, > td`]: {\n            background: tableExpandedRowBg\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/25573\n        [`${antCls}-descriptions-view`]: {\n          display: 'flex',\n          table: {\n            flex: 'auto',\n            width: 'auto'\n          }\n        }\n      },\n      // With fixed\n      [`${componentCls}-expanded-row-fixed`]: {\n        position: 'relative',\n        margin: `-${tablePaddingVertical}px -${tablePaddingHorizontal}px`,\n        padding: `${tablePaddingVertical}px ${tablePaddingHorizontal}px`\n      }\n    }\n  };\n};\nexport default genExpandStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}