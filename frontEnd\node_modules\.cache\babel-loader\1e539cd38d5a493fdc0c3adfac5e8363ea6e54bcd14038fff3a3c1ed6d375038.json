{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport YearHeader from \"./YearHeader\";\nimport YearBody, { YEAR_COL_COUNT } from \"./YearBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nimport { YEAR_DECADE_COUNT } from \"./constant\";\nexport { YEAR_DECADE_COUNT };\nfunction YearPanel(props) {\n  var prefixCls = props.prefixCls,\n    operationRef = props.operationRef,\n    onViewDateChange = props.onViewDateChange,\n    generateConfig = props.generateConfig,\n    value = props.value,\n    viewDate = props.viewDate,\n    sourceMode = props.sourceMode,\n    _onSelect = props.onSelect,\n    onPanelChange = props.onPanelChange;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-year-panel\");\n\n  // ======================= Keyboard =======================\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff), 'key');\n        },\n        onCtrlLeftRight: function onCtrlLeftRight(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_DECADE_COUNT), 'key');\n        },\n        onUpDown: function onUpDown(diff) {\n          _onSelect(generateConfig.addYear(value || viewDate, diff * YEAR_COL_COUNT), 'key');\n        },\n        onEnter: function onEnter() {\n          onPanelChange(sourceMode === 'date' ? 'date' : 'month', value || viewDate);\n        }\n      });\n    }\n  };\n\n  // ==================== View Operation ====================\n  var onDecadeChange = function onDecadeChange(diff) {\n    var newDate = generateConfig.addYear(viewDate, diff * 10);\n    onViewDateChange(newDate);\n    onPanelChange(null, newDate);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: panelPrefixCls\n  }, /*#__PURE__*/React.createElement(YearHeader, _extends({}, props, {\n    prefixCls: prefixCls,\n    onPrevDecade: function onPrevDecade() {\n      onDecadeChange(-1);\n    },\n    onNextDecade: function onNextDecade() {\n      onDecadeChange(1);\n    },\n    onDecadeClick: function onDecadeClick() {\n      onPanelChange('decade', viewDate);\n    }\n  })), /*#__PURE__*/React.createElement(YearBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    onSelect: function onSelect(date) {\n      onPanelChange(sourceMode === 'date' ? 'date' : 'month', date);\n      _onSelect(date, 'mouse');\n    }\n  })));\n}\nexport default YearPanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}