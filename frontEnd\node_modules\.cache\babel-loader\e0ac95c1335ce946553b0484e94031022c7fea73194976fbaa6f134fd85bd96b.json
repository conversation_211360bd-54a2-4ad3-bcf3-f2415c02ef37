{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useRef, useEffect } from 'react';\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport raf from \"rc-util/es/raf\";\nimport CacheMap from '../utils/CacheMap';\nexport default function useHeights(getKey, onItemAdd, onItemRemove) {\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    updatedMark = _React$useState2[0],\n    setUpdatedMark = _React$useState2[1];\n  var instanceRef = useRef(new Map());\n  var heightsRef = useRef(new CacheMap());\n  var collectRafRef = useRef();\n  function cancelRaf() {\n    raf.cancel(collectRafRef.current);\n  }\n  function collectHeight() {\n    cancelRaf();\n    collectRafRef.current = raf(function () {\n      instanceRef.current.forEach(function (element, key) {\n        if (element && element.offsetParent) {\n          var htmlElement = findDOMNode(element);\n          var offsetHeight = htmlElement.offsetHeight;\n          if (heightsRef.current.get(key) !== offsetHeight) {\n            heightsRef.current.set(key, htmlElement.offsetHeight);\n          }\n        }\n      });\n      // Always trigger update mark to tell parent that should re-calculate heights when resized\n      setUpdatedMark(function (c) {\n        return c + 1;\n      });\n    });\n  }\n  function setInstanceRef(item, instance) {\n    var key = getKey(item);\n    var origin = instanceRef.current.get(key);\n    if (instance) {\n      instanceRef.current.set(key, instance);\n      collectHeight();\n    } else {\n      instanceRef.current.delete(key);\n    }\n    // Instance changed\n    if (!origin !== !instance) {\n      if (instance) {\n        onItemAdd === null || onItemAdd === void 0 ? void 0 : onItemAdd(item);\n      } else {\n        onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove(item);\n      }\n    }\n  }\n  useEffect(function () {\n    return cancelRaf;\n  }, []);\n  return [setInstanceRef, collectHeight, heightsRef.current, updatedMark];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}