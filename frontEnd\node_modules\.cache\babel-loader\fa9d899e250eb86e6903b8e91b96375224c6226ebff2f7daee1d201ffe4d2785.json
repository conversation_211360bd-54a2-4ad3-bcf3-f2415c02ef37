{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport Input from '../../input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(`#${hex}`);\nconst ColorHexInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;\n  const [hexValue, setHexValue] = useState(value === null || value === void 0 ? void 0 : value.toHex());\n  // Update step value\n  useEffect(() => {\n    const hex = value === null || value === void 0 ? void 0 : value.toHex();\n    if (isHexString(hex) && value) {\n      setHexValue(toHexFormat(hex));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue === null || hexValue === void 0 ? void 0 : hexValue.toUpperCase(),\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}