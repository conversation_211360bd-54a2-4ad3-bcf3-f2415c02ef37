{"ast": null, "code": "import { genTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nexport default genTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}