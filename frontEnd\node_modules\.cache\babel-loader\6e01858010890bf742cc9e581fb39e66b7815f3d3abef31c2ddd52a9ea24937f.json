{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { useState } from 'react';\nimport extendsObject from '../../_util/extendsObject';\nexport const DEFAULT_PAGE_SIZE = 10;\nexport function getPaginationParam(mergedPagination, pagination) {\n  const param = {\n    current: mergedPagination.current,\n    pageSize: mergedPagination.pageSize\n  };\n  const paginationObj = pagination && typeof pagination === 'object' ? pagination : {};\n  Object.keys(paginationObj).forEach(pageProp => {\n    const value = mergedPagination[pageProp];\n    if (typeof value !== 'function') {\n      param[pageProp] = value;\n    }\n  });\n  return param;\n}\nfunction usePagination(total, onChange, pagination) {\n  const _a = pagination && typeof pagination === 'object' ? pagination : {},\n    {\n      total: paginationTotal = 0\n    } = _a,\n    paginationObj = __rest(_a, [\"total\"]);\n  const [innerPagination, setInnerPagination] = useState(() => ({\n    current: 'defaultCurrent' in paginationObj ? paginationObj.defaultCurrent : 1,\n    pageSize: 'defaultPageSize' in paginationObj ? paginationObj.defaultPageSize : DEFAULT_PAGE_SIZE\n  }));\n  // ============ Basic Pagination Config ============\n  const mergedPagination = extendsObject(innerPagination, paginationObj, {\n    total: paginationTotal > 0 ? paginationTotal : total\n  });\n  // Reset `current` if data length or pageSize changed\n  const maxPage = Math.ceil((paginationTotal || total) / mergedPagination.pageSize);\n  if (mergedPagination.current > maxPage) {\n    // Prevent a maximum page count of 0\n    mergedPagination.current = maxPage || 1;\n  }\n  const refreshPagination = (current, pageSize) => {\n    setInnerPagination({\n      current: current !== null && current !== void 0 ? current : 1,\n      pageSize: pageSize || mergedPagination.pageSize\n    });\n  };\n  const onInternalChange = (current, pageSize) => {\n    var _a;\n    if (pagination) {\n      (_a = pagination.onChange) === null || _a === void 0 ? void 0 : _a.call(pagination, current, pageSize);\n    }\n    refreshPagination(current, pageSize);\n    onChange(current, pageSize || (mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize));\n  };\n  if (pagination === false) {\n    return [{}, () => {}];\n  }\n  return [Object.assign(Object.assign({}, mergedPagination), {\n    onChange: onInternalChange\n  }), refreshPagination];\n}\nexport default usePagination;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}