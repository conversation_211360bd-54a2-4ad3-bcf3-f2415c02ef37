{"ast": null, "code": "const genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    tableHeaderIconColor,\n    tableHeaderIconColorHover,\n    tableSelectionColumnWidth\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth + paddingXS * 2,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: tableSelectionColumnWidth + fontSizeIcon + padding / 4 + paddingXS * 2\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: token.zIndexTableFixed + 1\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: `${token.tablePaddingHorizontal / 4}px`,\n        [iconCls]: {\n          color: tableHeaderIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: tableHeaderIconColorHover\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}