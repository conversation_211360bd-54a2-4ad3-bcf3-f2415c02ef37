{"ast": null, "code": "import ThemeCache from \"./ThemeCache\";\nimport Theme from \"./Theme\";\nvar cacheThemes = new ThemeCache();\n\n/**\n * Same as new Theme, but will always return same one if `derivative` not changed.\n */\nexport default function createTheme(derivatives) {\n  var derivativeArr = Array.isArray(derivatives) ? derivatives : [derivatives];\n  // Create new theme if not exist\n  if (!cacheThemes.has(derivativeArr)) {\n    cacheThemes.set(derivativeArr, new Theme(derivativeArr));\n  }\n\n  // Get theme from cache and return\n  return cacheThemes.get(derivativeArr);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}