{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport { generateColor } from \"../util\";\nfunction hasValue(value) {\n  return value !== undefined;\n}\nvar useColorState = function useColorState(defaultStateValue, option) {\n  var defaultValue = option.defaultValue,\n    value = option.value;\n  var _useState = useState(function () {\n      var mergeState;\n      if (hasValue(value)) {\n        mergeState = value;\n      } else if (hasValue(defaultValue)) {\n        mergeState = defaultValue;\n      } else {\n        mergeState = defaultStateValue;\n      }\n      return generateColor(mergeState);\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    colorValue = _useState2[0],\n    setColorValue = _useState2[1];\n  useEffect(function () {\n    if (value) {\n      setColorValue(generateColor(value));\n    }\n  }, [value]);\n  return [colorValue, setColorValue];\n};\nexport default useColorState;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}