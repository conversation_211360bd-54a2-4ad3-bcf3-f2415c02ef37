{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { getClientSize } from \"rc-util/es/Dom/css\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport raf from \"rc-util/es/raf\";\nimport { useRef, useState } from 'react';\nvar initialTransform = {\n  x: 0,\n  y: 0,\n  rotate: 0,\n  scale: 1,\n  flipX: false,\n  flipY: false\n};\nexport default function useImageTransform(imgRef, minScale, maxScale, onTransform) {\n  var frame = useRef(null);\n  var queue = useRef([]);\n  var _useState = useState(initialTransform),\n    _useState2 = _slicedToArray(_useState, 2),\n    transform = _useState2[0],\n    setTransform = _useState2[1];\n  var resetTransform = function resetTransform(action) {\n    setTransform(initialTransform);\n    if (onTransform && !isEqual(initialTransform, transform)) {\n      onTransform({\n        transform: initialTransform,\n        action: action\n      });\n    }\n  };\n\n  /** Direct update transform */\n  var updateTransform = function updateTransform(newTransform, action) {\n    if (frame.current === null) {\n      queue.current = [];\n      frame.current = raf(function () {\n        setTransform(function (preState) {\n          var memoState = preState;\n          queue.current.forEach(function (queueState) {\n            memoState = _objectSpread(_objectSpread({}, memoState), queueState);\n          });\n          frame.current = null;\n          onTransform === null || onTransform === void 0 ? void 0 : onTransform({\n            transform: memoState,\n            action: action\n          });\n          return memoState;\n        });\n      });\n    }\n    queue.current.push(_objectSpread(_objectSpread({}, transform), newTransform));\n  };\n\n  /** Scale according to the position of clientX and clientY */\n  var dispatchZoomChange = function dispatchZoomChange(ratio, action, clientX, clientY) {\n    var _imgRef$current = imgRef.current,\n      width = _imgRef$current.width,\n      height = _imgRef$current.height,\n      offsetWidth = _imgRef$current.offsetWidth,\n      offsetHeight = _imgRef$current.offsetHeight,\n      offsetLeft = _imgRef$current.offsetLeft,\n      offsetTop = _imgRef$current.offsetTop;\n    var newRatio = ratio;\n    var newScale = transform.scale * ratio;\n    if (newScale > maxScale) {\n      newRatio = maxScale / transform.scale;\n      newScale = maxScale;\n    } else if (newScale < minScale) {\n      newRatio = minScale / transform.scale;\n      newScale = minScale;\n    }\n\n    /** Default center point scaling */\n    var mergedClientX = clientX !== null && clientX !== void 0 ? clientX : innerWidth / 2;\n    var mergedClientY = clientY !== null && clientY !== void 0 ? clientY : innerHeight / 2;\n    var diffRatio = newRatio - 1;\n    /** Deviation calculated from image size */\n    var diffImgX = diffRatio * width * 0.5;\n    var diffImgY = diffRatio * height * 0.5;\n    /** The difference between the click position and the edge of the document */\n    var diffOffsetLeft = diffRatio * (mergedClientX - transform.x - offsetLeft);\n    var diffOffsetTop = diffRatio * (mergedClientY - transform.y - offsetTop);\n    /** Final positioning */\n    var newX = transform.x - (diffOffsetLeft - diffImgX);\n    var newY = transform.y - (diffOffsetTop - diffImgY);\n\n    /**\n     * When zooming the image\n     * When the image size is smaller than the width and height of the window, the position is initialized\n     */\n    if (ratio < 1 && newScale === 1) {\n      var mergedWidth = offsetWidth * newScale;\n      var mergedHeight = offsetHeight * newScale;\n      var _getClientSize = getClientSize(),\n        clientWidth = _getClientSize.width,\n        clientHeight = _getClientSize.height;\n      if (mergedWidth <= clientWidth && mergedHeight <= clientHeight) {\n        newX = 0;\n        newY = 0;\n      }\n    }\n    updateTransform({\n      x: newX,\n      y: newY,\n      scale: newScale\n    }, action);\n  };\n  return {\n    transform: transform,\n    resetTransform: resetTransform,\n    updateTransform: updateTransform,\n    dispatchZoomChange: dispatchZoomChange\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}