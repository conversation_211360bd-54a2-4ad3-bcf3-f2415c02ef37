{"ast": null, "code": "// This icon file is generated automatically.\nvar TrophyFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.6 630.2 359 721.8 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.8 758.4 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM248 439.6c-37.1-11.9-64-46.7-64-87.6V232h64v207.6zM840 352c0 41-26.9 75.8-64 87.6V232h64v120z\"\n      }\n    }]\n  },\n  \"name\": \"trophy\",\n  \"theme\": \"filled\"\n};\nexport default TrophyFilled;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}