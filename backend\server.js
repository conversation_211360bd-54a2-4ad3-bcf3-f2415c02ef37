const express = require("express");
const app = express();
const morgan = require("morgan");
const chalk = require("chalk");
require("dotenv").config();
const cors = require("cors");
const path = require("path");
const port = process.env.PORT || 5000;

const { connectDB } = require("./config/dbConfig");

// Security middleware
const {
  generalLimiter,
  authLimiter,
  apiLimiter,
  paymentLimiter,
  helmet,
  compression,
  securityHeaders,
  securityLogger
} = require("./middlewares/securityMiddleware");

// Routes
const usersRoute = require("./routes/usersRoute");
const plansRoute = require("./routes/planRoute");
const examsRoute = require("./routes/examsRoute");
const resportsRoute = require("./routes/reportsRoute");
const studyRoute = require("./routes/studyRoute");
const reviewsRoute = require("./routes/reviewsRoute");
const forumQuestionRoute = require("./routes/forumQuestionRoute");
const chatgptRoute = require("./routes/chatRoute");
const awsBucketRoute = require("./uploads/awsBucket");
const paymentRoute = require("./routes/paymentRoute");
const adminPaymentRoute = require("./routes/adminPaymentRoute");
const skillsRoute = require("./routes/skillsRoute");
const aiResponseRoute = require("./routes/aiResponseRoute");
const videoCommentRoute = require("./routes/videoCommentRoute");
const qaRoute = require("./routes/qaRouteSimple");
const authRoute = require("./routes/authRoute");
const enhancedQuizRoute = require("./routes/enhancedQuizRoute");
const xpDashboardRoute = require("./routes/xpDashboardRoute");
const notificationRoute = require("./routes/notificationRoute");

// Payment verification
const paymentVerificationService = require("./services/paymentVerificationService");

// Security for production
if (process.env.NODE_ENV === "production") {
  app.use(helmet);
  app.use(compression);
  app.use(securityHeaders);
  app.use(securityLogger);
  app.use(generalLimiter);
}

// CORS
app.use(cors({
  origin: [
    "http://localhost:3000",
    "https://www.brainwave.zone",
    "https://brainwave.zone",
    "https://server-fmff.onrender.com"
  ],
  credentials: true
}));

// Logging
morgan.format("short", (tokens, req, res) => {
  const method = tokens.method(req, res);
  const url = tokens.url(req, res);
  const status = tokens.status(req, res);
  const responseTime = Math.round(tokens["response-time"](req, res));
  let colorStatus = status >= 400 ? chalk.red(status) : chalk.green(status);
  return `${chalk.blue(method)} ${chalk.yellow(url)} ${colorStatus} ${responseTime}ms`;
});
app.use(morgan("short"));

// Static + Body Parsers
app.use("/uploads", express.static(path.join(__dirname, "Photos")));
app.use(express.json({ limit: "500mb" }));
app.use(express.urlencoded({ extended: true, limit: "500mb" }));

// Routes
app.get("/", (req, res) => res.send("Server is Up!"));
app.use("/api/users", usersRoute);
app.use("/api/plans", plansRoute);
app.use("/api/exams", examsRoute);
app.use("/api/reports", resportsRoute);
app.use("/api/study", studyRoute);
app.use("/api/reviews", reviewsRoute);
app.use("/api/forum", forumQuestionRoute);
app.use("/api/chatgpt", chatgptRoute);
app.use("/api/image", awsBucketRoute);
app.use("/api/payment", paymentRoute);
app.use("/api/admin/payment", adminPaymentRoute);
app.use("/api/skills", skillsRoute);
app.use("/api/ai-response", aiResponseRoute);
app.use("/api/video-comments", videoCommentRoute);
app.use("/api/qa", qaRoute);
app.use("/api/auth", authRoute);
app.use("/api/quiz", enhancedQuizRoute);
app.use("/api/xp-dashboard", xpDashboardRoute);
app.use("/api/notifications", notificationRoute);

// Health Check
app.get("/api/health", (req, res) => {
  res.json({ status: "success", message: "Server is running", timestamp: new Date().toISOString() });
});

// ✅ Start Server Only After DB Connect
connectDB()
  .then(() => {
    app.listen(port, () => {
      console.log(chalk.green(`✅ Server listening on port ${port}`));
      console.log(chalk.blue(`🔗 Health check: http://localhost:${port}/api/health`));

      setTimeout(() => {
        console.log(chalk.yellow("🚀 Starting payment verification service..."));
        paymentVerificationService.start();
      }, 5000);
    });
  })
  .catch((err) => {
    console.error(chalk.red(`❌ Could not start server: ${err.message}`));
    process.exit(1);
  });
