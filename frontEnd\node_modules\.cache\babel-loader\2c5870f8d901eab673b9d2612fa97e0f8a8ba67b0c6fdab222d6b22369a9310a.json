{"ast": null, "code": "const genSorterStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    fontSizeIcon,\n    tableHeaderIconColor,\n    tableHeaderIconColorHover\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-thead th${componentCls}-column-has-sorters`]: {\n        outline: 'none',\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:hover': {\n          background: token.tableHeaderSortHoverBg,\n          '&::before': {\n            backgroundColor: 'transparent !important'\n          }\n        },\n        '&:focus-visible': {\n          color: token.colorPrimary\n        },\n        // https://github.com/ant-design/ant-design/issues/30969\n        [`\n          &${componentCls}-cell-fix-left:hover,\n          &${componentCls}-cell-fix-right:hover\n        `]: {\n          background: token.tableFixedHeaderSortActiveBg\n        }\n      },\n      [`${componentCls}-thead th${componentCls}-column-sort`]: {\n        background: token.tableHeaderSortBg,\n        '&::before': {\n          backgroundColor: 'transparent !important'\n        }\n      },\n      [`td${componentCls}-column-sort`]: {\n        background: token.tableBodySortBg\n      },\n      [`${componentCls}-column-title`]: {\n        position: 'relative',\n        zIndex: 1,\n        flex: 1\n      },\n      [`${componentCls}-column-sorters`]: {\n        display: 'flex',\n        flex: 'auto',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          height: '100%',\n          content: '\"\"'\n        }\n      },\n      [`${componentCls}-column-sorter`]: {\n        marginInlineStart: marginXXS,\n        color: tableHeaderIconColor,\n        fontSize: 0,\n        transition: `color ${token.motionDurationSlow}`,\n        '&-inner': {\n          display: 'inline-flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        '&-up, &-down': {\n          fontSize: fontSizeIcon,\n          '&.active': {\n            color: token.colorPrimary\n          }\n        },\n        [`${componentCls}-column-sorter-up + ${componentCls}-column-sorter-down`]: {\n          marginTop: '-0.3em'\n        }\n      },\n      [`${componentCls}-column-sorters:hover ${componentCls}-column-sorter`]: {\n        color: tableHeaderIconColorHover\n      }\n    }\n  };\n};\nexport default genSorterStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}