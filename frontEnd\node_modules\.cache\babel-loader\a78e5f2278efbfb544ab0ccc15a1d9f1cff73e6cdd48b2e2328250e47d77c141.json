{"ast": null, "code": "export function setTime(generateConfig, date, hour, minute, second) {\n  var nextTime = generateConfig.setHour(date, hour);\n  nextTime = generateConfig.setMinute(nextTime, minute);\n  nextTime = generateConfig.setSecond(nextTime, second);\n  return nextTime;\n}\nexport function setDateTime(generateConfig, date, defaultDate) {\n  if (!defaultDate) {\n    return date;\n  }\n  var newDate = date;\n  newDate = generateConfig.setHour(newDate, generateConfig.getHour(defaultDate));\n  newDate = generateConfig.setMinute(newDate, generateConfig.getMinute(defaultDate));\n  newDate = generateConfig.setSecond(newDate, generateConfig.getSecond(defaultDate));\n  return newDate;\n}\nexport function getLowerBoundTime(hour, minute, second, hourStep, minuteStep, secondStep) {\n  var lowerBoundHour = Math.floor(hour / hourStep) * hourStep;\n  if (lowerBoundHour < hour) {\n    return [lowerBoundHour, 60 - minuteStep, 60 - secondStep];\n  }\n  var lowerBoundMinute = Math.floor(minute / minuteStep) * minuteStep;\n  if (lowerBoundMinute < minute) {\n    return [lowerBoundHour, lowerBoundMinute, 60 - secondStep];\n  }\n  var lowerBoundSecond = Math.floor(second / secondStep) * secondStep;\n  return [lowerBoundHour, lowerBoundMinute, lowerBoundSecond];\n}\nexport function getLastDay(generateConfig, date) {\n  var year = generateConfig.getYear(date);\n  var month = generateConfig.getMonth(date) + 1;\n  var endDate = generateConfig.getEndDate(generateConfig.getFixedDate(\"\".concat(year, \"-\").concat(month, \"-01\")));\n  var lastDay = generateConfig.getDate(endDate);\n  var monthShow = month < 10 ? \"0\".concat(month) : \"\".concat(month);\n  return \"\".concat(year, \"-\").concat(monthShow, \"-\").concat(lastDay);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}