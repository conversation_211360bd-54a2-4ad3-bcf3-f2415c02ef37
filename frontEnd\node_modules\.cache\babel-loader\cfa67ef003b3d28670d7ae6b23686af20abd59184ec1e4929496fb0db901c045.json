{"ast": null, "code": "import * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\n\n/**\n * Parse `children` to `options` if `options` is not provided.\n * Then flatten the `options`.\n */\nexport default function useOptions(options, children, fieldNames, optionFilterProp, optionLabelProp) {\n  return React.useMemo(function () {\n    var mergedOptions = options;\n    var childrenAsData = !options;\n    if (childrenAsData) {\n      mergedOptions = convertChildrenToData(children);\n    }\n    var valueOptions = new Map();\n    var labelOptions = new Map();\n    var setLabelOptions = function setLabelOptions(labelOptionsMap, option, key) {\n      if (key && typeof key === 'string') {\n        labelOptionsMap.set(option[key], option);\n      }\n    };\n    function dig(optionList) {\n      var isChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      // for loop to speed up collection speed\n      for (var i = 0; i < optionList.length; i += 1) {\n        var option = optionList[i];\n        if (!option[fieldNames.options] || isChildren) {\n          valueOptions.set(option[fieldNames.value], option);\n          setLabelOptions(labelOptions, option, fieldNames.label);\n          // https://github.com/ant-design/ant-design/issues/35304\n          setLabelOptions(labelOptions, option, optionFilterProp);\n          setLabelOptions(labelOptions, option, optionLabelProp);\n        } else {\n          dig(option[fieldNames.options], true);\n        }\n      }\n    }\n    dig(mergedOptions);\n    return {\n      options: mergedOptions,\n      valueOptions: valueOptions,\n      labelOptions: labelOptions\n    };\n  }, [options, children, fieldNames, optionFilterProp, optionLabelProp]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}