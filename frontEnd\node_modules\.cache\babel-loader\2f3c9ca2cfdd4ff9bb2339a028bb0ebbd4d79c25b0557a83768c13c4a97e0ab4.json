{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nexport function legacyPropsWarning(props) {\n  var picker = props.picker,\n    disabledHours = props.disabledHours,\n    disabledMinutes = props.disabledMinutes,\n    disabledSeconds = props.disabledSeconds;\n  if (picker === 'time' && (disabledHours || disabledMinutes || disabledSeconds)) {\n    warning(false, \"'disabledHours', 'disabledMinutes', 'disabledSeconds' will be removed in the next major version, please use 'disabledTime' instead.\");\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}