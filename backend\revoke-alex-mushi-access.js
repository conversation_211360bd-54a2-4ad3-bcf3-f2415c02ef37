const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const axios = require('axios');
require('dotenv').config();

/**
 * EMERGENCY FIX: Revoke alex.mushi's unauthorized access
 * 
 * This script specifically addresses the alex.mushi payment issue
 * where the system allowed access without confirmed payment
 */

async function revokeAlexMushiAccess() {
  try {
    console.log('🚨 EMERGENCY FIX: Revoking alex.mushi unauthorized access');
    console.log('=' .repeat(60));
    console.log('');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Find alex.mushi user
    const alexUser = await User.findOne({ username: 'alex.mushi' });
    
    if (!alexUser) {
      console.log('❌ User alex.mushi not found');
      return;
    }
    
    console.log('👤 Found alex.mushi:');
    console.log(`   Name: ${alexUser.firstName} ${alexUser.lastName}`);
    console.log(`   Email: ${alexUser.email}`);
    console.log(`   Phone: ${alexUser.phoneNumber}`);
    console.log(`   Current Status: ${alexUser.subscriptionStatus}`);
    console.log(`   Payment Required: ${alexUser.paymentRequired}`);
    console.log('');
    
    // Find all subscriptions for alex.mushi
    const subscriptions = await Subscription.find({ user: alexUser._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📋 Found ${subscriptions.length} subscription(s)\n`);
    
    let hasValidPayment = false;
    let revokedCount = 0;
    
    // Check each subscription
    for (let i = 0; i < subscriptions.length; i++) {
      const sub = subscriptions[i];
      console.log(`🔍 Checking Subscription ${i + 1}:`);
      console.log(`   ID: ${sub._id}`);
      console.log(`   Plan: ${sub.activePlan?.title || 'Unknown'}`);
      console.log(`   Payment Status: ${sub.paymentStatus}`);
      console.log(`   Subscription Status: ${sub.status}`);
      console.log(`   Created: ${sub.createdAt}`);
      
      // Check if this subscription has valid payment
      if (sub.paymentHistory && sub.paymentHistory.length > 0) {
        const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
        console.log(`   Order ID: ${latestPayment.orderId}`);
        
        if (latestPayment.orderId && latestPayment.orderId.startsWith('ORDER_')) {
          // Verify with ZenoPay
          const isValidPayment = await verifyPaymentWithZenoPay(latestPayment.orderId);
          
          if (isValidPayment) {
            console.log(`   ✅ VALID PAYMENT CONFIRMED`);
            hasValidPayment = true;
          } else {
            console.log(`   ❌ PAYMENT NOT CONFIRMED - REVOKING`);
            await revokeSubscription(sub);
            revokedCount++;
          }
        } else {
          console.log(`   ❌ INVALID ORDER ID - REVOKING`);
          await revokeSubscription(sub);
          revokedCount++;
        }
      } else {
        console.log(`   ❌ NO PAYMENT HISTORY - REVOKING`);
        await revokeSubscription(sub);
        revokedCount++;
      }
      console.log('');
    }
    
    // Update user status based on findings
    if (!hasValidPayment) {
      console.log('🚫 NO VALID PAYMENTS FOUND - REVOKING USER ACCESS');
      
      await User.findByIdAndUpdate(alexUser._id, {
        subscriptionStatus: 'free',
        paymentRequired: true,
        subscriptionEndDate: null,
        subscriptionStartDate: null
      });
      
      console.log('✅ User access revoked');
    } else {
      console.log('✅ Valid payment found - user access maintained');
    }
    
    // Generate report
    console.log('\n📊 EMERGENCY FIX REPORT:');
    console.log('=' .repeat(40));
    console.log(`User: alex.mushi`);
    console.log(`Total subscriptions checked: ${subscriptions.length}`);
    console.log(`Subscriptions revoked: ${revokedCount}`);
    console.log(`Valid payments found: ${hasValidPayment ? 'YES' : 'NO'}`);
    console.log(`Final user status: ${hasValidPayment ? 'ACTIVE' : 'FREE'}`);
    console.log('');
    
    if (revokedCount > 0) {
      console.log('🔒 SECURITY MEASURES APPLIED:');
      console.log('- Unauthorized subscriptions revoked');
      console.log('- User access restricted to free tier');
      console.log('- Payment verification required for future access');
    }
    
  } catch (error) {
    console.error('❌ Error during emergency fix:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

async function verifyPaymentWithZenoPay(orderId) {
  try {
    console.log(`     🔍 Verifying with ZenoPay: ${orderId}`);
    
    const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
    const response = await axios.get(statusUrl, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY,
      },
      timeout: 15000
    });
    
    if (response.data && response.data.data && response.data.data.length > 0) {
      const paymentData = response.data.data[0];
      console.log(`     📊 ZenoPay Status: ${paymentData.payment_status}`);
      
      if (paymentData.payment_status === 'COMPLETED') {
        return true;
      }
    } else {
      console.log(`     ⚠️ No payment data found in ZenoPay`);
    }
    
    return false;
    
  } catch (error) {
    console.log(`     ❌ Error checking ZenoPay: ${error.message}`);
    return false;
  }
}

async function revokeSubscription(subscription) {
  try {
    console.log(`     🚫 Revoking subscription ${subscription._id}`);
    
    // Update subscription status
    subscription.status = 'expired';
    subscription.paymentStatus = 'failed';
    
    // Add revocation note to payment history
    if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.referenceId = `REVOKED_${Date.now()}_UNAUTHORIZED`;
    }
    
    await subscription.save();
    console.log(`     ✅ Subscription revoked`);
    
  } catch (error) {
    console.error(`     ❌ Error revoking subscription:`, error.message);
  }
}

// Run the emergency fix
revokeAlexMushiAccess();
