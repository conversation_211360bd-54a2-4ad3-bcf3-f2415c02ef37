{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"autoComplete\", \"onChange\", \"onFocus\", \"onBlur\", \"onPressEnter\", \"onKeyDown\", \"prefixCls\", \"disabled\", \"htmlSize\", \"className\", \"maxLength\", \"suffix\", \"showCount\", \"type\", \"classes\", \"classNames\", \"styles\"];\nimport clsx from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';\nimport BaseInput from \"./BaseInput\";\nimport { fixControlledValue, resolveOnChange, triggerFocus } from \"./utils/commonUtils\";\nvar Input = /*#__PURE__*/forwardRef(function (props, ref) {\n  var autoComplete = props.autoComplete,\n    onChange = props.onChange,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onPressEnter = props.onPressEnter,\n    onKeyDown = props.onKeyDown,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-input' : _props$prefixCls,\n    disabled = props.disabled,\n    htmlSize = props.htmlSize,\n    className = props.className,\n    maxLength = props.maxLength,\n    suffix = props.suffix,\n    showCount = props.showCount,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'text' : _props$type,\n    classes = props.classes,\n    classNames = props.classNames,\n    styles = props.styles,\n    rest = _objectWithoutProperties(props, _excluded);\n  var _useMergedState = useMergedState(props.defaultValue, {\n      value: props.value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    focused = _useState2[0],\n    setFocused = _useState2[1];\n  var inputRef = useRef(null);\n  var focus = function focus(option) {\n    if (inputRef.current) {\n      triggerFocus(inputRef.current, option);\n    }\n  };\n  useImperativeHandle(ref, function () {\n    return {\n      focus: focus,\n      blur: function blur() {\n        var _inputRef$current;\n        (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.blur();\n      },\n      setSelectionRange: function setSelectionRange(start, end, direction) {\n        var _inputRef$current2;\n        (_inputRef$current2 = inputRef.current) === null || _inputRef$current2 === void 0 ? void 0 : _inputRef$current2.setSelectionRange(start, end, direction);\n      },\n      select: function select() {\n        var _inputRef$current3;\n        (_inputRef$current3 = inputRef.current) === null || _inputRef$current3 === void 0 ? void 0 : _inputRef$current3.select();\n      },\n      input: inputRef.current\n    };\n  });\n  useEffect(function () {\n    setFocused(function (prev) {\n      return prev && disabled ? false : prev;\n    });\n  }, [disabled]);\n  var handleChange = function handleChange(e) {\n    if (props.value === undefined) {\n      setValue(e.target.value);\n    }\n    if (inputRef.current) {\n      resolveOnChange(inputRef.current, e, onChange);\n    }\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (onPressEnter && e.key === 'Enter') {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n  };\n  var handleReset = function handleReset(e) {\n    setValue('');\n    focus();\n    if (inputRef.current) {\n      resolveOnChange(inputRef.current, e, onChange);\n    }\n  };\n  var getInputElement = function getInputElement() {\n    // Fix https://fb.me/react-unknown-prop\n    var otherProps = omit(props, ['prefixCls', 'onPressEnter', 'addonBefore', 'addonAfter', 'prefix', 'suffix', 'allowClear',\n    // Input elements must be either controlled or uncontrolled,\n    // specify either the value prop, or the defaultValue prop, but not both.\n    'defaultValue', 'showCount', 'classes', 'htmlSize', 'styles', 'classNames']);\n    return /*#__PURE__*/React.createElement(\"input\", _extends({\n      autoComplete: autoComplete\n    }, otherProps, {\n      onChange: handleChange,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onKeyDown: handleKeyDown,\n      className: clsx(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), classNames === null || classNames === void 0 ? void 0 : classNames.input),\n      style: styles === null || styles === void 0 ? void 0 : styles.input,\n      ref: inputRef,\n      size: htmlSize,\n      type: type\n    }));\n  };\n  var getSuffix = function getSuffix() {\n    // Max length value\n    var hasMaxLength = Number(maxLength) > 0;\n    if (suffix || showCount) {\n      var val = fixControlledValue(value);\n      var valueLength = _toConsumableArray(val).length;\n      var dataCount = _typeof(showCount) === 'object' ? showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      }) : \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n      return /*#__PURE__*/React.createElement(React.Fragment, null, !!showCount && /*#__PURE__*/React.createElement(\"span\", {\n        className: clsx(\"\".concat(prefixCls, \"-show-count-suffix\"), _defineProperty({}, \"\".concat(prefixCls, \"-show-count-has-suffix\"), !!suffix), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n        style: _objectSpread({}, styles === null || styles === void 0 ? void 0 : styles.count)\n      }, dataCount), suffix);\n    }\n    return null;\n  };\n  return /*#__PURE__*/React.createElement(BaseInput, _extends({}, rest, {\n    prefixCls: prefixCls,\n    className: className,\n    inputElement: getInputElement(),\n    handleReset: handleReset,\n    value: fixControlledValue(value),\n    focused: focused,\n    triggerFocus: focus,\n    suffix: getSuffix(),\n    disabled: disabled,\n    classes: classes,\n    classNames: classNames,\n    styles: styles\n  }));\n});\nexport default Input;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}