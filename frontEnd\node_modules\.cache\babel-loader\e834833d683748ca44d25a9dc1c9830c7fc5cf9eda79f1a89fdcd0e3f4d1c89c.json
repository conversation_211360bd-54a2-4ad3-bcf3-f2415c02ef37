{"ast": null, "code": "import { TinyColor } from '@ctrl/tinycolor';\nexport const getAlphaColor = (baseColor, alpha) => new TinyColor(baseColor).setAlpha(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new TinyColor(baseColor);\n  return instance.darken(brightness).toHexString();\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}