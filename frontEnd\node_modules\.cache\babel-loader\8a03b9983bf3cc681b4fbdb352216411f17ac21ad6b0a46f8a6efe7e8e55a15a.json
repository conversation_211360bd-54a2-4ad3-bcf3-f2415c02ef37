{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport TimeHeader from \"./TimeHeader\";\nimport TimeBody from \"./TimeBody\";\nimport { createKeyDownHandler } from \"../../utils/uiUtil\";\nvar countBoolean = function countBoolean(boolList) {\n  return boolList.filter(function (bool) {\n    return bool !== false;\n  }).length;\n};\nfunction TimePanel(props) {\n  var generateConfig = props.generateConfig,\n    _props$format = props.format,\n    format = _props$format === void 0 ? 'HH:mm:ss' : _props$format,\n    prefixCls = props.prefixCls,\n    active = props.active,\n    operationRef = props.operationRef,\n    showHour = props.showHour,\n    showMinute = props.showMinute,\n    showSecond = props.showSecond,\n    _props$use12Hours = props.use12Hours,\n    use12Hours = _props$use12Hours === void 0 ? false : _props$use12Hours,\n    onSelect = props.onSelect,\n    value = props.value;\n  var panelPrefixCls = \"\".concat(prefixCls, \"-time-panel\");\n  var bodyOperationRef = React.useRef();\n\n  // ======================= Keyboard =======================\n  var _React$useState = React.useState(-1),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeColumnIndex = _React$useState2[0],\n    setActiveColumnIndex = _React$useState2[1];\n  var columnsCount = countBoolean([showHour, showMinute, showSecond, use12Hours]);\n  operationRef.current = {\n    onKeyDown: function onKeyDown(event) {\n      return createKeyDownHandler(event, {\n        onLeftRight: function onLeftRight(diff) {\n          setActiveColumnIndex((activeColumnIndex + diff + columnsCount) % columnsCount);\n        },\n        onUpDown: function onUpDown(diff) {\n          if (activeColumnIndex === -1) {\n            setActiveColumnIndex(0);\n          } else if (bodyOperationRef.current) {\n            bodyOperationRef.current.onUpDown(diff);\n          }\n        },\n        onEnter: function onEnter() {\n          onSelect(value || generateConfig.getNow(), 'key');\n          setActiveColumnIndex(-1);\n        }\n      });\n    },\n    onBlur: function onBlur() {\n      setActiveColumnIndex(-1);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(panelPrefixCls, _defineProperty({}, \"\".concat(panelPrefixCls, \"-active\"), active))\n  }, /*#__PURE__*/React.createElement(TimeHeader, _extends({}, props, {\n    format: format,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(TimeBody, _extends({}, props, {\n    prefixCls: prefixCls,\n    activeColumnIndex: activeColumnIndex,\n    operationRef: bodyOperationRef\n  })));\n}\nexport default TimePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}