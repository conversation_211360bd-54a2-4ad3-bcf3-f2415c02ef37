{"ast": null, "code": "export function getColumnKey(column, defaultKey) {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n}\nexport function getColumnPos(index, pos) {\n  return pos ? `${pos}-${index}` : `${index}`;\n}\nexport function renderColumnTitle(title, props) {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n}\n/**\n * Safe get column title\n *\n * Should filter [object Object]\n *\n * @param title\n * @returns\n */\nexport function safeColumnTitle(title, props) {\n  const res = renderColumnTitle(title, props);\n  if (Object.prototype.toString.call(res) === '[object Object]') return '';\n  return res;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}