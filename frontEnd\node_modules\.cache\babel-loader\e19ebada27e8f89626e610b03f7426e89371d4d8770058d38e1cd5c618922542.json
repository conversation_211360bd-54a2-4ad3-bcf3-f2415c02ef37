{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"classes\", \"showCount\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\"];\nimport clsx from 'classnames';\nimport { BaseInput } from 'rc-input';\nimport { fixControlledValue, resolveOnChange } from \"rc-input/es/utils/commonUtils\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useEffect, useImperativeHandle, useRef } from 'react';\nimport ResizableTextArea from \"./ResizableTextArea\";\nfunction fixEmojiLength(value, maxLength) {\n  return _toConsumableArray(value || '').slice(0, maxLength).join('');\n}\nfunction setTriggerValue(isCursorInEnd, preValue, triggerValue, maxLength) {\n  var newTriggerValue = triggerValue;\n  if (isCursorInEnd) {\n    // 光标在尾部，直接截断\n    newTriggerValue = fixEmojiLength(triggerValue, maxLength);\n  } else if (_toConsumableArray(preValue || '').length < triggerValue.length && _toConsumableArray(triggerValue || '').length > maxLength) {\n    // 光标在中间，如果最后的值超过最大值，则采用原先的值\n    newTriggerValue = preValue;\n  }\n  return newTriggerValue;\n}\nvar TextArea = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var _clsx;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    classes = _ref.classes,\n    showCount = _ref.showCount,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useMergedState = useMergedState(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var resizableTextAreaRef = useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    compositing = _React$useState4[0],\n    setCompositing = _React$useState4[1];\n  var oldCompositionValueRef = React.useRef();\n  var oldSelectionStartRef = React.useRef(0);\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    textareaResized = _React$useState6[0],\n    setTextareaResized = _React$useState6[1];\n  var focus = function focus() {\n    var _resizableTextAreaRef;\n    (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea.focus();\n  };\n  useImperativeHandle(ref, function () {\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        var _resizableTextAreaRef2;\n        (_resizableTextAreaRef2 = resizableTextAreaRef.current) === null || _resizableTextAreaRef2 === void 0 ? void 0 : _resizableTextAreaRef2.textArea.blur();\n      }\n    };\n  });\n  useEffect(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Value Update ===========================\n  // Max length value\n  var hasMaxLength = Number(maxLength) > 0;\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    setCompositing(true);\n    // 拼音输入前保存一份旧值\n    oldCompositionValueRef.current = value;\n    // 保存旧的光标位置\n    oldSelectionStartRef.current = e.currentTarget.selectionStart;\n    onCompositionStart === null || onCompositionStart === void 0 ? void 0 : onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    setCompositing(false);\n    var triggerValue = e.currentTarget.value;\n    if (hasMaxLength) {\n      var _oldCompositionValueR;\n      var isCursorInEnd = oldSelectionStartRef.current >= maxLength + 1 || oldSelectionStartRef.current === ((_oldCompositionValueR = oldCompositionValueRef.current) === null || _oldCompositionValueR === void 0 ? void 0 : _oldCompositionValueR.length);\n      triggerValue = setTriggerValue(isCursorInEnd, oldCompositionValueRef.current, triggerValue, maxLength);\n    }\n    // Patch composition onChange when value changed\n    if (triggerValue !== value) {\n      setValue(triggerValue);\n      resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n    }\n    onCompositionEnd === null || onCompositionEnd === void 0 ? void 0 : onCompositionEnd(e);\n  };\n  var handleChange = function handleChange(e) {\n    var triggerValue = e.target.value;\n    if (!compositing && hasMaxLength) {\n      // 1. 复制粘贴超过maxlength的情况 2.未超过maxlength的情况\n      var isCursorInEnd = e.target.selectionStart >= maxLength + 1 || e.target.selectionStart === triggerValue.length || !e.target.selectionStart;\n      triggerValue = setTriggerValue(isCursorInEnd, value, triggerValue, maxLength);\n    }\n    setValue(triggerValue);\n    resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var onPressEnter = rest.onPressEnter,\n      onKeyDown = rest.onKeyDown;\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    var _resizableTextAreaRef3;\n    setValue('');\n    focus();\n    resolveOnChange((_resizableTextAreaRef3 = resizableTextAreaRef.current) === null || _resizableTextAreaRef3 === void 0 ? void 0 : _resizableTextAreaRef3.textArea, e, onChange);\n  };\n  var val = fixControlledValue(value);\n  if (!compositing && hasMaxLength && (customValue === null || customValue === undefined)) {\n    // fix #27612 将value转为数组进行截取，解决 '😂'.length === 2 等emoji表情导致的截取乱码的问题\n    val = fixEmojiLength(val, maxLength);\n  }\n  var suffixNode = suffix;\n  var dataCount;\n  if (showCount) {\n    var valueLength = _toConsumableArray(val).length;\n    if (_typeof(showCount) === 'object') {\n      dataCount = showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n    }\n    suffixNode = /*#__PURE__*/React.createElement(React.Fragment, null, suffixNode, /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _resizableTextAreaRef4;\n    onResize === null || onResize === void 0 ? void 0 : onResize(size);\n    if ((_resizableTextAreaRef4 = resizableTextAreaRef.current) !== null && _resizableTextAreaRef4 !== void 0 && _resizableTextAreaRef4.textArea.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !rest.autoSize && !showCount && !allowClear;\n  var textarea = /*#__PURE__*/React.createElement(BaseInput, {\n    value: val,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classes: {\n      affixWrapper: clsx(classes === null || classes === void 0 ? void 0 : classes.affixWrapper, (_clsx = {}, _defineProperty(_clsx, \"\".concat(prefixCls, \"-show-count\"), showCount), _defineProperty(_clsx, \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear), _clsx))\n    },\n    disabled: disabled,\n    focused: focused,\n    className: className,\n    style: _objectSpread(_objectSpread({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    inputElement: /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, rest, {\n      onKeyDown: handleKeyDown,\n      onChange: handleChange,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onCompositionStart: onInternalCompositionStart,\n      onCompositionEnd: onInternalCompositionEnd,\n      className: classNames === null || classNames === void 0 ? void 0 : classNames.textarea,\n      style: _objectSpread(_objectSpread({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n        resize: style === null || style === void 0 ? void 0 : style.resize\n      }),\n      disabled: disabled,\n      prefixCls: prefixCls,\n      onResize: handleResize,\n      ref: resizableTextAreaRef\n    }))\n  });\n  return textarea;\n});\nexport default TextArea;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}