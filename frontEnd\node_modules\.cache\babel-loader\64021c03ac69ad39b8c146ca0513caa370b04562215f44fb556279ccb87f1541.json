{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from \"../Header\";\nimport { YEAR_DECADE_COUNT } from \"./constant\";\nimport PanelContext from \"../../PanelContext\";\nfunction YearHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    onPrevDecade = props.onPrevDecade,\n    onNextDecade = props.onNextDecade,\n    onDecadeClick = props.onDecadeClick;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecade,\n    onSuperNext: onNextDecade\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onDecadeClick,\n    className: \"\".concat(prefixCls, \"-decade-btn\")\n  }, startYear, \"-\", endYear));\n}\nexport default YearHeader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}