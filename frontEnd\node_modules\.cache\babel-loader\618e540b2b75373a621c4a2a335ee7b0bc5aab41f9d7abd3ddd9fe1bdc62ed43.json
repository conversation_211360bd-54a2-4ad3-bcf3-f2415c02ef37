{"ast": null, "code": "import * as React from 'react';\nimport PanelContext from \"../PanelContext\";\nvar HIDDEN_STYLE = {\n  visibility: 'hidden'\n};\nfunction Header(_ref) {\n  var prefixCls = _ref.prefixCls,\n    _ref$prevIcon = _ref.prevIcon,\n    prevIcon = _ref$prevIcon === void 0 ? \"\\u2039\" : _ref$prevIcon,\n    _ref$nextIcon = _ref.nextIcon,\n    nextIcon = _ref$nextIcon === void 0 ? \"\\u203A\" : _ref$nextIcon,\n    _ref$superPrevIcon = _ref.superPrevIcon,\n    superPrevIcon = _ref$superPrevIcon === void 0 ? \"\\xAB\" : _ref$superPrevIcon,\n    _ref$superNextIcon = _ref.superNextIcon,\n    superNextIcon = _ref$superNextIcon === void 0 ? \"\\xBB\" : _ref$superNextIcon,\n    onSuperPrev = _ref.onSuperPrev,\n    onSuperNext = _ref.onSuperNext,\n    onPrev = _ref.onPrev,\n    onNext = _ref.onNext,\n    children = _ref.children;\n  var _React$useContext = React.useContext(PanelContext),\n    hideNextBtn = _React$useContext.hideNextBtn,\n    hidePrevBtn = _React$useContext.hidePrevBtn;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: prefixCls\n  }, onSuperPrev && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onSuperPrev,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-super-prev-btn\"),\n    style: hidePrevBtn ? HIDDEN_STYLE : {}\n  }, superPrevIcon), onPrev && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onPrev,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-prev-btn\"),\n    style: hidePrevBtn ? HIDDEN_STYLE : {}\n  }, prevIcon), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-view\")\n  }, children), onNext && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onNext,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-next-btn\"),\n    style: hideNextBtn ? HIDDEN_STYLE : {}\n  }, nextIcon), onSuperNext && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    onClick: onSuperNext,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-super-next-btn\"),\n    style: hideNextBtn ? HIDDEN_STYLE : {}\n  }, superNextIcon));\n}\nexport default Header;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}