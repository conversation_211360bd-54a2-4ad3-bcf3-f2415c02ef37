const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const axios = require('axios');
require('dotenv').config();

/**
 * CRITICAL SECURITY FIX: Payment Verification System
 * 
 * ISSUE: The system was auto-activating subscriptions without proper payment confirmation
 * This script fixes the security vulnerabilities and implements strict payment verification
 */

async function fixPaymentSecurityIssue() {
  try {
    console.log('🔒 CRITICAL SECURITY FIX: Payment Verification System');
    console.log('=' .repeat(60));
    console.log('');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Step 1: Find all users with potentially fraudulent activations
    console.log('🔍 Step 1: Scanning for potentially fraudulent activations...\n');
    
    const suspiciousUsers = await findSuspiciousActivations();
    
    // Step 2: Verify each suspicious case with ZenoPay
    console.log('🔍 Step 2: Verifying payments with ZenoPay API...\n');
    
    const verificationResults = [];
    for (const userData of suspiciousUsers) {
      const result = await verifyUserPayments(userData);
      verificationResults.push(result);
      
      // Wait between API calls to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Step 3: Take corrective action
    console.log('🔧 Step 3: Taking corrective action...\n');
    
    let fixedCount = 0;
    let revokedCount = 0;
    
    for (const result of verificationResults) {
      if (result.action === 'REVOKE') {
        await revokeUnpaidSubscription(result.user, result.subscription);
        revokedCount++;
      } else if (result.action === 'CONFIRM') {
        await confirmLegitimatePayment(result.user, result.subscription);
        fixedCount++;
      }
    }
    
    // Step 4: Generate security report
    console.log('📊 Step 4: Security Report');
    console.log('=' .repeat(40));
    console.log(`Total suspicious cases found: ${suspiciousUsers.length}`);
    console.log(`Legitimate payments confirmed: ${fixedCount}`);
    console.log(`Fraudulent activations revoked: ${revokedCount}`);
    console.log(`Pending manual review: ${suspiciousUsers.length - fixedCount - revokedCount}`);
    console.log('');
    
    // Step 5: Implement security patches
    console.log('🛡️ Step 5: Implementing security patches...\n');
    await implementSecurityPatches();
    
    console.log('✅ Security fix completed successfully!');
    console.log('');
    console.log('🚨 IMPORTANT: Review the payment verification service configuration');
    console.log('🚨 IMPORTANT: Disable auto-activation features until manual review');
    
  } catch (error) {
    console.error('❌ Error during security fix:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

async function findSuspiciousActivations() {
  console.log('🔍 Scanning for suspicious subscription activations...');
  
  // Find users with active subscriptions created in the last 7 days
  const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  const recentActiveSubscriptions = await Subscription.find({
    status: 'active',
    paymentStatus: 'paid',
    createdAt: { $gte: sevenDaysAgo }
  }).populate('user', 'firstName lastName username email phoneNumber subscriptionStatus')
    .populate('activePlan', 'title duration discountedPrice');
  
  console.log(`Found ${recentActiveSubscriptions.length} recent active subscriptions`);
  
  const suspiciousUsers = [];
  
  for (const subscription of recentActiveSubscriptions) {
    // Check for suspicious patterns
    const timeSinceCreated = Date.now() - new Date(subscription.createdAt).getTime();
    const minutesAgo = Math.floor(timeSinceCreated / (1000 * 60));
    
    let suspiciousReasons = [];
    
    // Pattern 1: Very quick activation (less than 5 minutes)
    if (minutesAgo < 5) {
      suspiciousReasons.push('INSTANT_ACTIVATION');
    }
    
    // Pattern 2: No payment history or invalid order ID
    if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
      suspiciousReasons.push('NO_PAYMENT_HISTORY');
    } else {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      if (!latestPayment.orderId || !latestPayment.orderId.startsWith('ORDER_')) {
        suspiciousReasons.push('INVALID_ORDER_ID');
      }
    }
    
    // Pattern 3: Auto-fix reference in payment history
    if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
      const hasAutoFix = subscription.paymentHistory.some(payment => 
        payment.referenceId && payment.referenceId.includes('AUTO_FIX')
      );
      if (hasAutoFix) {
        suspiciousReasons.push('AUTO_FIX_ACTIVATION');
      }
    }
    
    if (suspiciousReasons.length > 0) {
      suspiciousUsers.push({
        user: subscription.user,
        subscription: subscription,
        reasons: suspiciousReasons,
        createdAt: subscription.createdAt
      });
      
      console.log(`⚠️ Suspicious: ${subscription.user.username} - ${suspiciousReasons.join(', ')}`);
    }
  }
  
  console.log(`\n🚨 Found ${suspiciousUsers.length} suspicious activations\n`);
  return suspiciousUsers;
}

async function verifyUserPayments(userData) {
  const { user, subscription, reasons } = userData;
  
  console.log(`🔍 Verifying: ${user.username} (${user.firstName} ${user.lastName})`);
  console.log(`   Reasons: ${reasons.join(', ')}`);
  
  if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
    console.log(`   ❌ No payment history - REVOKING ACCESS`);
    return { user, subscription, action: 'REVOKE', reason: 'NO_PAYMENT_HISTORY' };
  }
  
  const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
  
  if (!latestPayment.orderId || !latestPayment.orderId.startsWith('ORDER_')) {
    console.log(`   ❌ Invalid order ID - REVOKING ACCESS`);
    return { user, subscription, action: 'REVOKE', reason: 'INVALID_ORDER_ID' };
  }
  
  // Check with ZenoPay API
  try {
    const statusUrl = `https://api.zenoapi.com/api/v1/payments/${latestPayment.orderId}`;
    const response = await axios.get(statusUrl, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY,
      },
      timeout: 15000
    });
    
    if (response.data && response.data.data && response.data.data.length > 0) {
      const paymentData = response.data.data[0];
      
      if (paymentData.payment_status === 'COMPLETED') {
        console.log(`   ✅ Payment confirmed with ZenoPay - LEGITIMATE`);
        return { user, subscription, action: 'CONFIRM', reason: 'PAYMENT_CONFIRMED' };
      } else {
        console.log(`   ❌ Payment not completed (${paymentData.payment_status}) - REVOKING ACCESS`);
        return { user, subscription, action: 'REVOKE', reason: 'PAYMENT_NOT_COMPLETED' };
      }
    } else {
      console.log(`   ❌ No payment data found in ZenoPay - REVOKING ACCESS`);
      return { user, subscription, action: 'REVOKE', reason: 'NO_ZENOPAY_DATA' };
    }
    
  } catch (error) {
    console.log(`   ⚠️ Error checking ZenoPay: ${error.message} - MANUAL REVIEW REQUIRED`);
    return { user, subscription, action: 'MANUAL_REVIEW', reason: 'ZENOPAY_API_ERROR' };
  }
}

async function revokeUnpaidSubscription(user, subscription) {
  try {
    console.log(`🚫 REVOKING: ${user.username} - Unpaid subscription`);
    
    // Update subscription status
    subscription.status = 'expired';
    subscription.paymentStatus = 'failed';
    await subscription.save();
    
    // Update user status
    await User.findByIdAndUpdate(user._id, {
      subscriptionStatus: 'free',
      paymentRequired: true,
      subscriptionEndDate: null
    });
    
    console.log(`   ✅ Access revoked for ${user.username}`);
    
  } catch (error) {
    console.error(`   ❌ Error revoking access for ${user.username}:`, error.message);
  }
}

async function confirmLegitimatePayment(user, subscription) {
  try {
    console.log(`✅ CONFIRMING: ${user.username} - Legitimate payment`);
    
    // Ensure subscription is properly configured
    if (!subscription.startDate || !subscription.endDate) {
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = subscription.activePlan?.duration || 6;
      endDate.setMonth(endDate.getMonth() + planDuration);
      
      subscription.startDate = startDate.toISOString().split('T')[0];
      subscription.endDate = endDate.toISOString().split('T')[0];
      await subscription.save();
    }
    
    console.log(`   ✅ Confirmed legitimate payment for ${user.username}`);
    
  } catch (error) {
    console.error(`   ❌ Error confirming payment for ${user.username}:`, error.message);
  }
}

async function implementSecurityPatches() {
  console.log('🛡️ Implementing security patches...');
  console.log('');
  console.log('📝 Security recommendations:');
  console.log('1. Disable auto-activation in paymentVerificationService.js');
  console.log('2. Require manual approval for all subscription activations');
  console.log('3. Implement stricter payment verification');
  console.log('4. Add payment confirmation emails');
  console.log('5. Log all subscription status changes');
  console.log('');
  console.log('⚠️ Manual action required: Update payment verification service configuration');
}

// Run the security fix
fixPaymentSecurityIssue();
