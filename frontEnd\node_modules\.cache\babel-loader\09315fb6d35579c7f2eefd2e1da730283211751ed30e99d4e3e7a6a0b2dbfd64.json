{"ast": null, "code": "import { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar SMOOTH_PTG = 14 / 15;\nexport default function useMobileTouchMove(inVirtual, listRef, callback) {\n  var touchedRef = useRef(false);\n  var touchYRef = useRef(0);\n  var elementRef = useRef(null);\n  // Smooth scroll\n  var intervalRef = useRef(null);\n  /* eslint-disable prefer-const */\n  var cleanUpEvents;\n  var onTouchMove = function onTouchMove(e) {\n    if (touchedRef.current) {\n      var currentY = Math.ceil(e.touches[0].pageY);\n      var offsetY = touchYRef.current - currentY;\n      touchYRef.current = currentY;\n      if (callback(offsetY)) {\n        e.preventDefault();\n      }\n      // Smooth interval\n      clearInterval(intervalRef.current);\n      intervalRef.current = setInterval(function () {\n        offsetY *= SMOOTH_PTG;\n        if (!callback(offsetY, true) || Math.abs(offsetY) <= 0.1) {\n          clearInterval(intervalRef.current);\n        }\n      }, 16);\n    }\n  };\n  var onTouchEnd = function onTouchEnd() {\n    touchedRef.current = false;\n    cleanUpEvents();\n  };\n  var onTouchStart = function onTouchStart(e) {\n    cleanUpEvents();\n    if (e.touches.length === 1 && !touchedRef.current) {\n      touchedRef.current = true;\n      touchYRef.current = Math.ceil(e.touches[0].pageY);\n      elementRef.current = e.target;\n      elementRef.current.addEventListener('touchmove', onTouchMove);\n      elementRef.current.addEventListener('touchend', onTouchEnd);\n    }\n  };\n  cleanUpEvents = function cleanUpEvents() {\n    if (elementRef.current) {\n      elementRef.current.removeEventListener('touchmove', onTouchMove);\n      elementRef.current.removeEventListener('touchend', onTouchEnd);\n    }\n  };\n  useLayoutEffect(function () {\n    if (inVirtual) {\n      listRef.current.addEventListener('touchstart', onTouchStart);\n    }\n    return function () {\n      var _listRef$current;\n      (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.removeEventListener('touchstart', onTouchStart);\n      cleanUpEvents();\n      clearInterval(intervalRef.current);\n    };\n  }, [inVirtual]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}