{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\n\n/**\n * 1. Click input to show picker\n * 2. Calculate next open index\n *\n * If click `confirm`:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n *\n * If not `changeOnBlur` and click outside:\n * 3. Hide picker\n *\n * If `changeOnBlur` and click outside:\n * 3. Hide current picker\n * 4. Open next index picker if exist\n */\n\n/**\n * Auto control of open state\n */\nexport default function useRangeOpen(defaultOpen, open, activePickerIndex, changeOnBlur, startInputRef, endInputRef, startSelectedValue, endSelectedValue, disabled, onOpenChange) {\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    firstTimeOpen = _React$useState2[0],\n    setFirstTimeOpen = _React$useState2[1];\n  var _useMergedState = useMergedState(defaultOpen || false, {\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    afferentOpen = _useMergedState2[0],\n    setAfferentOpen = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(defaultOpen || false, {\n      value: open,\n      onChange: function onChange(nextOpen) {\n        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedOpen = _useMergedState4[0],\n    setMergedOpen = _useMergedState4[1];\n  var _useMergedState5 = useMergedState(0, {\n      value: activePickerIndex\n    }),\n    _useMergedState6 = _slicedToArray(_useMergedState5, 2),\n    mergedActivePickerIndex = _useMergedState6[0],\n    setMergedActivePickerIndex = _useMergedState6[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    nextActiveIndex = _React$useState4[0],\n    setNextActiveIndex = _React$useState4[1];\n  React.useEffect(function () {\n    if (mergedOpen) {\n      setFirstTimeOpen(true);\n    }\n  }, [mergedOpen]);\n  var queryNextIndex = function queryNextIndex(index) {\n    return index === 0 ? 1 : 0;\n  };\n  var triggerOpen = useEvent(function (nextOpen, index, source) {\n    if (index === false) {\n      // Only when `nextOpen` is false and no need open to next index\n      setMergedOpen(nextOpen);\n    } else if (nextOpen) {\n      setMergedActivePickerIndex(index);\n      setMergedOpen(nextOpen);\n      var nextIndex = queryNextIndex(index);\n\n      // Record next open index\n      if (!mergedOpen ||\n      // Also set next index if next is empty\n      ![startSelectedValue, endSelectedValue][nextIndex]) {\n        setNextActiveIndex(nextIndex);\n      } else {\n        setFirstTimeOpen(false);\n        if (nextActiveIndex !== null) {\n          setNextActiveIndex(null);\n        }\n      }\n    } else if (source === 'confirm' || source === 'blur' && changeOnBlur) {\n      var customNextActiveIndex = afferentOpen ? queryNextIndex(index) : nextActiveIndex;\n      if (customNextActiveIndex !== null) {\n        setFirstTimeOpen(false);\n        setMergedActivePickerIndex(customNextActiveIndex);\n      }\n      setNextActiveIndex(null);\n\n      // Focus back\n      if (customNextActiveIndex !== null && !disabled[customNextActiveIndex]) {\n        raf(function () {\n          var _ref$current;\n          var ref = [startInputRef, endInputRef][customNextActiveIndex];\n          (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.focus();\n        });\n      } else {\n        setMergedOpen(false);\n      }\n    } else {\n      setMergedOpen(false);\n      setAfferentOpen(false);\n    }\n  });\n  return [mergedOpen, mergedActivePickerIndex, firstTimeOpen, triggerOpen];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}