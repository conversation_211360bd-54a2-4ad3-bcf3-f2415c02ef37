{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const slideUpIn = new Keyframes('antSlideUpIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideUpOut = new Keyframes('antSlideUpOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideDownIn = new Keyframes('antSlideDownIn', {\n  '0%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  }\n});\nexport const slideDownOut = new Keyframes('antSlideDownOut', {\n  '0%': {\n    transform: 'scaleY(1)',\n    transformOrigin: '100% 100%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleY(0.8)',\n    transformOrigin: '100% 100%',\n    opacity: 0\n  }\n});\nexport const slideLeftIn = new Keyframes('antSlideLeftIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  }\n});\nexport const slideLeftOut = new Keyframes('antSlideLeftOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '0% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '0% 0%',\n    opacity: 0\n  }\n});\nexport const slideRightIn = new Keyframes('antSlideRightIn', {\n  '0%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  }\n});\nexport const slideRightOut = new Keyframes('antSlideRightOut', {\n  '0%': {\n    transform: 'scaleX(1)',\n    transformOrigin: '100% 0%',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'scaleX(0.8)',\n    transformOrigin: '100% 0%',\n    opacity: 0\n  }\n});\nconst slideMotion = {\n  'slide-up': {\n    inKeyframes: slideUpIn,\n    outKeyframes: slideUpOut\n  },\n  'slide-down': {\n    inKeyframes: slideDownIn,\n    outKeyframes: slideDownOut\n  },\n  'slide-left': {\n    inKeyframes: slideLeftIn,\n    outKeyframes: slideLeftOut\n  },\n  'slide-right': {\n    inKeyframes: slideRightIn,\n    outKeyframes: slideRightOut\n  }\n};\nexport const initSlideMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = slideMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, token.motionDurationMid), {\n    [`\n      ${motionCls}-enter,\n      ${motionCls}-appear\n    `]: {\n      transform: 'scale(0)',\n      transformOrigin: '0% 0%',\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutQuint,\n      [`&-prepare`]: {\n        transform: 'scale(1)'\n      }\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInQuint\n    }\n  }];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}