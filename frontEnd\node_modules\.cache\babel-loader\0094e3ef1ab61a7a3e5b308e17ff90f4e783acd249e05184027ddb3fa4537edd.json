{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport clsx from 'classnames';\nimport React, { cloneElement, useRef } from 'react';\nimport { hasAddon, hasPrefixSuffix } from \"./utils/commonUtils\";\nvar BaseInput = function BaseInput(props) {\n  var _inputElement$props, _inputElement$props2;\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    prefix = props.prefix,\n    suffix = props.suffix,\n    addonBefore = props.addonBefore,\n    addonAfter = props.addonAfter,\n    className = props.className,\n    style = props.style,\n    disabled = props.disabled,\n    readOnly = props.readOnly,\n    focused = props.focused,\n    triggerFocus = props.triggerFocus,\n    allowClear = props.allowClear,\n    value = props.value,\n    handleReset = props.handleReset,\n    hidden = props.hidden,\n    classes = props.classes,\n    classNames = props.classNames,\n    dataAttrs = props.dataAttrs,\n    styles = props.styles,\n    components = props.components;\n  var AffixWrapperComponent = (components === null || components === void 0 ? void 0 : components.affixWrapper) || 'span';\n  var GroupWrapperComponent = (components === null || components === void 0 ? void 0 : components.groupWrapper) || 'span';\n  var WrapperComponent = (components === null || components === void 0 ? void 0 : components.wrapper) || 'span';\n  var GroupAddonComponent = (components === null || components === void 0 ? void 0 : components.groupAddon) || 'span';\n  var containerRef = useRef(null);\n  var onInputClick = function onInputClick(e) {\n    var _containerRef$current;\n    if ((_containerRef$current = containerRef.current) !== null && _containerRef$current !== void 0 && _containerRef$current.contains(e.target)) {\n      triggerFocus === null || triggerFocus === void 0 ? void 0 : triggerFocus();\n    }\n  };\n\n  // ================== Clear Icon ================== //\n  var getClearIcon = function getClearIcon() {\n    var _clsx;\n    if (!allowClear) {\n      return null;\n    }\n    var needClear = !disabled && !readOnly && value;\n    var clearIconCls = \"\".concat(prefixCls, \"-clear-icon\");\n    var iconNode = _typeof(allowClear) === 'object' && allowClear !== null && allowClear !== void 0 && allowClear.clearIcon ? allowClear.clearIcon : '✖';\n    return /*#__PURE__*/React.createElement(\"span\", {\n      onClick: handleReset\n      // Do not trigger onBlur when clear input\n      // https://github.com/ant-design/ant-design/issues/31200\n      ,\n\n      onMouseDown: function onMouseDown(e) {\n        return e.preventDefault();\n      },\n      className: clsx(clearIconCls, (_clsx = {}, _defineProperty(_clsx, \"\".concat(clearIconCls, \"-hidden\"), !needClear), _defineProperty(_clsx, \"\".concat(clearIconCls, \"-has-suffix\"), !!suffix), _clsx)),\n      role: \"button\",\n      tabIndex: -1\n    }, iconNode);\n  };\n  var element = /*#__PURE__*/cloneElement(inputElement, {\n    value: value,\n    hidden: hidden,\n    className: clsx((_inputElement$props = inputElement.props) === null || _inputElement$props === void 0 ? void 0 : _inputElement$props.className, !hasPrefixSuffix(props) && !hasAddon(props) && className) || null,\n    style: _objectSpread(_objectSpread({}, (_inputElement$props2 = inputElement.props) === null || _inputElement$props2 === void 0 ? void 0 : _inputElement$props2.style), !hasPrefixSuffix(props) && !hasAddon(props) ? style : {})\n  });\n\n  // ================== Prefix & Suffix ================== //\n  if (hasPrefixSuffix(props)) {\n    var _clsx2;\n    var affixWrapperPrefixCls = \"\".concat(prefixCls, \"-affix-wrapper\");\n    var affixWrapperCls = clsx(affixWrapperPrefixCls, (_clsx2 = {}, _defineProperty(_clsx2, \"\".concat(affixWrapperPrefixCls, \"-disabled\"), disabled), _defineProperty(_clsx2, \"\".concat(affixWrapperPrefixCls, \"-focused\"), focused), _defineProperty(_clsx2, \"\".concat(affixWrapperPrefixCls, \"-readonly\"), readOnly), _defineProperty(_clsx2, \"\".concat(affixWrapperPrefixCls, \"-input-with-clear-btn\"), suffix && allowClear && value), _clsx2), !hasAddon(props) && className, classes === null || classes === void 0 ? void 0 : classes.affixWrapper, classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper);\n    var suffixNode = (suffix || allowClear) && /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx(\"\".concat(prefixCls, \"-suffix\"), classNames === null || classNames === void 0 ? void 0 : classNames.suffix),\n      style: styles === null || styles === void 0 ? void 0 : styles.suffix\n    }, getClearIcon(), suffix);\n    element = /*#__PURE__*/React.createElement(AffixWrapperComponent, _extends({\n      className: affixWrapperCls,\n      style: _objectSpread(_objectSpread({}, !hasAddon(props) ? style : undefined), styles === null || styles === void 0 ? void 0 : styles.affixWrapper),\n      hidden: !hasAddon(props) && hidden,\n      onClick: onInputClick\n    }, dataAttrs === null || dataAttrs === void 0 ? void 0 : dataAttrs.affixWrapper, {\n      ref: containerRef\n    }), prefix && /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx(\"\".concat(prefixCls, \"-prefix\"), classNames === null || classNames === void 0 ? void 0 : classNames.prefix),\n      style: styles === null || styles === void 0 ? void 0 : styles.prefix\n    }, prefix), /*#__PURE__*/cloneElement(inputElement, {\n      value: value,\n      hidden: null\n    }), suffixNode);\n  }\n\n  // ================== Addon ================== //\n  if (hasAddon(props)) {\n    var wrapperCls = \"\".concat(prefixCls, \"-group\");\n    var addonCls = \"\".concat(wrapperCls, \"-addon\");\n    var mergedWrapperClassName = clsx(\"\".concat(prefixCls, \"-wrapper\"), wrapperCls, classes === null || classes === void 0 ? void 0 : classes.wrapper);\n    var mergedGroupClassName = clsx(\"\".concat(prefixCls, \"-group-wrapper\"), className, classes === null || classes === void 0 ? void 0 : classes.group);\n\n    // Need another wrapper for changing display:table to display:inline-block\n    // and put style prop in wrapper\n    return /*#__PURE__*/React.createElement(GroupWrapperComponent, {\n      className: mergedGroupClassName,\n      style: style,\n      hidden: hidden\n    }, /*#__PURE__*/React.createElement(WrapperComponent, {\n      className: mergedWrapperClassName\n    }, addonBefore && /*#__PURE__*/React.createElement(GroupAddonComponent, {\n      className: addonCls\n    }, addonBefore), /*#__PURE__*/cloneElement(element, {\n      hidden: null\n    }), addonAfter && /*#__PURE__*/React.createElement(GroupAddonComponent, {\n      className: addonCls\n    }, addonAfter)));\n  }\n  return element;\n};\nexport default BaseInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}