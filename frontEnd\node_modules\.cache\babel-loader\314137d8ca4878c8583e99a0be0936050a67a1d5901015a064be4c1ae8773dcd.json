{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport PanelContext from \"../../PanelContext\";\nimport RangeContext from \"../../RangeContext\";\nimport { getCellDateDisabled, isInRange, isSameWeek } from \"../../utils/dateUtil\";\nimport DatePanel from \"../DatePanel\";\nfunction WeekPanel(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    value = props.value,\n    disabledDate = props.disabledDate,\n    onSelect = props.onSelect;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var _React$useContext2 = React.useContext(PanelContext),\n    onDateMouseEnter = _React$useContext2.onDateMouseEnter,\n    onDateMouseLeave = _React$useContext2.onDateMouseLeave;\n  var rangeStart = (hoverRangedValue === null || hoverRangedValue === void 0 ? void 0 : hoverRangedValue[0]) || (rangedValue === null || rangedValue === void 0 ? void 0 : rangedValue[0]);\n  var rangeEnd = (hoverRangedValue === null || hoverRangedValue === void 0 ? void 0 : hoverRangedValue[1]) || (rangedValue === null || rangedValue === void 0 ? void 0 : rangedValue[1]);\n\n  // Render additional column\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var prefixColumn = function prefixColumn(date) {\n    // >>> Additional check for disabled\n    var disabled = getCellDateDisabled({\n      cellDate: date,\n      mode: 'week',\n      disabledDate: disabledDate,\n      generateConfig: generateConfig\n    });\n    return /*#__PURE__*/React.createElement(\"td\", {\n      key: \"week\",\n      className: classNames(cellPrefixCls, \"\".concat(cellPrefixCls, \"-week\"))\n      // Operation: Same as code in PanelBody\n      ,\n\n      onClick: function onClick() {\n        if (!disabled) {\n          onSelect(date, 'mouse');\n        }\n      },\n      onMouseEnter: function onMouseEnter() {\n        if (!disabled && onDateMouseEnter) {\n          onDateMouseEnter(date);\n        }\n      },\n      onMouseLeave: function onMouseLeave() {\n        if (!disabled && onDateMouseLeave) {\n          onDateMouseLeave(date);\n        }\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, generateConfig.locale.getWeek(locale.locale, date)));\n  };\n\n  // Add row className\n  var rowPrefixCls = \"\".concat(prefixCls, \"-week-panel-row\");\n  var rowClassName = function rowClassName(date) {\n    var _classNames;\n    var isRangeStart = isSameWeek(generateConfig, locale.locale, rangeStart, date);\n    var isRangeEnd = isSameWeek(generateConfig, locale.locale, rangeEnd, date);\n    return classNames(rowPrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(rowPrefixCls, \"-selected\"), !rangedValue && isSameWeek(generateConfig, locale.locale, value, date)), _defineProperty(_classNames, \"\".concat(rowPrefixCls, \"-range-start\"), isRangeStart), _defineProperty(_classNames, \"\".concat(rowPrefixCls, \"-range-end\"), isRangeEnd), _defineProperty(_classNames, \"\".concat(rowPrefixCls, \"-range-hover\"), !isRangeStart && !isRangeEnd && isInRange(generateConfig, rangeStart, rangeEnd, date)), _classNames));\n  };\n  return /*#__PURE__*/React.createElement(DatePanel, _extends({}, props, {\n    panelName: \"week\",\n    prefixColumn: prefixColumn,\n    rowClassName: rowClassName,\n    keyboardConfig: {\n      onLeftRight: null\n    }\n    // No need check cell level\n    ,\n\n    isSameCell: function isSameCell() {\n      return false;\n    }\n  }));\n}\nexport default WeekPanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}