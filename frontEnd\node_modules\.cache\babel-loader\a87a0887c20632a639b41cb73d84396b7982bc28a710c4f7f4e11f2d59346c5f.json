{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"direction\", \"component\", \"onScroll\", \"onVisibleChange\", \"innerProps\"];\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport Filler from './Filler';\nimport ScrollBar from './ScrollBar';\nimport useChildren from './hooks/useChildren';\nimport useHeights from './hooks/useHeights';\nimport useScrollTo from './hooks/useScrollTo';\nimport useDiffItem from './hooks/useDiffItem';\nimport useFrameWheel from './hooks/useFrameWheel';\nimport useMobileTouchMove from './hooks/useMobileTouchMove';\nimport useOriginScroll from './hooks/useOriginScroll';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n    className = props.className,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    _props$fullHeight = props.fullHeight,\n    fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n    style = props.style,\n    data = props.data,\n    children = props.children,\n    itemKey = props.itemKey,\n    virtual = props.virtual,\n    direction = props.direction,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    onScroll = props.onScroll,\n    onVisibleChange = props.onVisibleChange,\n    innerProps = props.innerProps,\n    restProps = _objectWithoutProperties(props, _excluded);\n  // ================================= MISC =================================\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var inVirtual = useVirtual && data && itemHeight * data.length > height;\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    scrollTop = _useState2[0],\n    setScrollTop = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    scrollMoving = _useState4[0],\n    setScrollMoving = _useState4[1];\n  var mergedClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var scrollBarRef = useRef(); // Hack on scrollbar to enable flash call\n  // =============================== Item Key ===============================\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n  var sharedConfig = {\n    getKey: getKey\n  };\n  // ================================ Scroll ================================\n  function syncScrollTop(newTop) {\n    setScrollTop(function (origin) {\n      var value;\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  }\n  // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n    _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n    diffItem = _useDiffItem2[0];\n  diffItemRef.current = diffItem;\n  // ================================ Height ================================\n  var _useHeights = useHeights(getKey, null, null),\n    _useHeights2 = _slicedToArray(_useHeights, 4),\n    setInstanceRef = _useHeights2[0],\n    collectHeight = _useHeights2[1],\n    heights = _useHeights2[2],\n    heightUpdatedMark = _useHeights2[3];\n  // ========================== Visible Calculation =========================\n  var _React$useMemo = React.useMemo(function () {\n      if (!useVirtual) {\n        return {\n          scrollHeight: undefined,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      // Always use virtual scroll bar in avoid shaking\n      if (!inVirtual) {\n        var _fillerInnerRef$curre;\n        return {\n          scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      var itemTop = 0;\n      var startIndex;\n      var startOffset;\n      var endIndex;\n      var dataLen = mergedData.length;\n      for (var i = 0; i < dataLen; i += 1) {\n        var item = mergedData[i];\n        var key = getKey(item);\n        var cacheHeight = heights.get(key);\n        var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n        // Check item top in the range\n        if (currentItemBottom >= scrollTop && startIndex === undefined) {\n          startIndex = i;\n          startOffset = itemTop;\n        }\n        // Check item bottom in the range. We will render additional one item for motion usage\n        if (currentItemBottom > scrollTop + height && endIndex === undefined) {\n          endIndex = i;\n        }\n        itemTop = currentItemBottom;\n      }\n      // When scrollTop at the end but data cut to small count will reach this\n      if (startIndex === undefined) {\n        startIndex = 0;\n        startOffset = 0;\n        endIndex = Math.ceil(height / itemHeight);\n      }\n      if (endIndex === undefined) {\n        endIndex = mergedData.length - 1;\n      }\n      // Give cache to improve scroll experience\n      endIndex = Math.min(endIndex + 1, mergedData.length);\n      return {\n        scrollHeight: itemTop,\n        start: startIndex,\n        end: endIndex,\n        offset: startOffset\n      };\n    }, [inVirtual, useVirtual, scrollTop, mergedData, heightUpdatedMark, height]),\n    scrollHeight = _React$useMemo.scrollHeight,\n    start = _React$useMemo.start,\n    end = _React$useMemo.end,\n    offset = _React$useMemo.offset;\n  rangeRef.current.start = start;\n  rangeRef.current.end = end;\n  // =============================== In Range ===============================\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n  var isScrollAtTop = scrollTop <= 0;\n  var isScrollAtBottom = scrollTop >= maxScrollHeight;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom);\n  // ================================ Scroll ================================\n  function onScrollBar(newScrollTop) {\n    var newTop = newScrollTop;\n    syncScrollTop(newTop);\n  }\n  // When data size reduce. It may trigger native scroll event back to fit scroll position\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n    if (newScrollTop !== scrollTop) {\n      syncScrollTop(newScrollTop);\n    }\n    // Trigger origin onScroll\n    onScroll === null || onScroll === void 0 ? void 0 : onScroll(e);\n  }\n  // Since this added in global,should use ref to keep update\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, function (offsetY) {\n      syncScrollTop(function (top) {\n        var newTop = top + offsetY;\n        return newTop;\n      });\n    }),\n    _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n    onRawWheel = _useFrameWheel2[0],\n    onFireFoxScroll = _useFrameWheel2[1];\n  // Mobile touch move\n  useMobileTouchMove(useVirtual, componentRef, function (deltaY, smoothOffset) {\n    if (originScroll(deltaY, smoothOffset)) {\n      return false;\n    }\n    onRawWheel({\n      preventDefault: function preventDefault() {},\n      deltaY: deltaY\n    });\n    return true;\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      if (useVirtual) {\n        e.preventDefault();\n      }\n    }\n    componentRef.current.addEventListener('wheel', onRawWheel);\n    componentRef.current.addEventListener('DOMMouseScroll', onFireFoxScroll);\n    componentRef.current.addEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    return function () {\n      if (componentRef.current) {\n        componentRef.current.removeEventListener('wheel', onRawWheel);\n        componentRef.current.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n        componentRef.current.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n      }\n    };\n  }, [useVirtual]);\n  // ================================= Ref ==================================\n  var scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, collectHeight, syncScrollTop, function () {\n    var _scrollBarRef$current;\n    (_scrollBarRef$current = scrollBarRef.current) === null || _scrollBarRef$current === void 0 ? void 0 : _scrollBarRef$current.delayHidden();\n  });\n  React.useImperativeHandle(ref, function () {\n    return {\n      scrollTo: scrollTo\n    };\n  });\n  // ================================ Effect ================================\n  /** We need told outside that some list not rendered */\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]);\n  // ================================ Render ================================\n  var listChildren = useChildren(mergedData, start, end, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, restProps), /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offset: offset,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef,\n    innerProps: innerProps\n  }, listChildren)), useVirtual && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: scrollBarRef,\n    prefixCls: prefixCls,\n    scrollTop: scrollTop,\n    height: height,\n    scrollHeight: scrollHeight,\n    count: mergedData.length,\n    direction: direction,\n    onScroll: onScrollBar,\n    onStartMove: function onStartMove() {\n      setScrollMoving(true);\n    },\n    onStopMove: function onStopMove() {\n      setScrollMoving(false);\n    }\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}