{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport { useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport { scrollTo, waitElementReady } from \"../../utils/uiUtil\";\nimport PanelContext from \"../../PanelContext\";\nfunction TimeUnitColumn(props) {\n  var prefixCls = props.prefixCls,\n    units = props.units,\n    onSelect = props.onSelect,\n    value = props.value,\n    active = props.active,\n    hideDisabledOptions = props.hideDisabledOptions,\n    info = props.info,\n    type = props.type;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var _React$useContext = React.useContext(PanelContext),\n    open = _React$useContext.open;\n  var ulRef = useRef(null);\n  var liRefs = useRef(new Map());\n  var scrollRef = useRef();\n\n  // `useLayoutEffect` here to avoid blink by duration is 0\n  useLayoutEffect(function () {\n    var li = liRefs.current.get(value);\n    if (li && open !== false) {\n      scrollTo(ulRef.current, li.offsetTop, 120);\n    }\n  }, [value]);\n  useLayoutEffect(function () {\n    if (open) {\n      var li = liRefs.current.get(value);\n      if (li) {\n        scrollRef.current = waitElementReady(li, function () {\n          scrollTo(ulRef.current, li.offsetTop, 0);\n        });\n      }\n    }\n    return function () {\n      var _scrollRef$current;\n      (_scrollRef$current = scrollRef.current) === null || _scrollRef$current === void 0 ? void 0 : _scrollRef$current.call(scrollRef);\n    };\n  }, [open]);\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(\"\".concat(prefixCls, \"-column\"), _defineProperty({}, \"\".concat(prefixCls, \"-column-active\"), active)),\n    ref: ulRef,\n    style: {\n      position: 'relative'\n    }\n  }, units.map(function (unit) {\n    var _classNames2;\n    if (hideDisabledOptions && unit.disabled) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: unit.value,\n      ref: function ref(element) {\n        liRefs.current.set(unit.value, element);\n      },\n      className: classNames(cellPrefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-disabled\"), unit.disabled), _defineProperty(_classNames2, \"\".concat(cellPrefixCls, \"-selected\"), value === unit.value), _classNames2)),\n      onClick: function onClick() {\n        if (unit.disabled) {\n          return;\n        }\n        onSelect(unit.value);\n      }\n    }, info.cellRender ? info.cellRender(unit.value, {\n      today: info.today,\n      locale: info.locale,\n      originNode: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(cellPrefixCls, \"-inner\")\n      }, unit.label),\n      type: 'time',\n      subType: type\n    }) : /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(cellPrefixCls, \"-inner\")\n    }, unit.label));\n  }));\n}\nexport default TimeUnitColumn;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}