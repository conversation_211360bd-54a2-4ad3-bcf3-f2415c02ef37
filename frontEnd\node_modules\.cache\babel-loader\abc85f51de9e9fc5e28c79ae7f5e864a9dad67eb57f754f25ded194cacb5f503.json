{"ast": null, "code": "import * as React from 'react';\nimport raf from \"rc-util/es/raf\";\nexport default (function () {\n  var nextFrameRef = React.useRef(null);\n  function cancelNextFrame() {\n    raf.cancel(nextFrameRef.current);\n  }\n  function nextFrame(callback) {\n    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2;\n    cancelNextFrame();\n    var nextFrameId = raf(function () {\n      if (delay <= 1) {\n        callback({\n          isCanceled: function isCanceled() {\n            return nextFrameId !== nextFrameRef.current;\n          }\n        });\n      } else {\n        nextFrame(callback, delay - 1);\n      }\n    });\n    nextFrameRef.current = nextFrameId;\n  }\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [nextFrame, cancelNextFrame];\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}