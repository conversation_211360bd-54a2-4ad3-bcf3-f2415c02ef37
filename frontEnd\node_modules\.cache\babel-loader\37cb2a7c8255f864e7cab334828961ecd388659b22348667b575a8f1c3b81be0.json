{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport RangeContext from \"../../RangeContext\";\nimport { formatValue, isSameMonth } from \"../../utils/dateUtil\";\nimport PanelBody from \"../PanelBody\";\nexport var MONTH_COL_COUNT = 3;\nvar MONTH_ROW_COUNT = 4;\nfunction MonthBody(props) {\n  var prefixCls = props.prefixCls,\n    locale = props.locale,\n    value = props.value,\n    viewDate = props.viewDate,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: cellPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameMonth(generateConfig, current, target);\n    },\n    isInView: function isInView() {\n      return true;\n    },\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addMonth(date, offset);\n    }\n  });\n  var monthsLocale = locale.shortMonths || (generateConfig.locale.getShortMonths ? generateConfig.locale.getShortMonths(locale.locale) : []);\n  var baseMonth = generateConfig.setMonth(viewDate, 0);\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      locale: locale,\n      today: generateConfig.getNow(),\n      type: 'month'\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: MONTH_ROW_COUNT,\n    colNum: MONTH_COL_COUNT,\n    baseDate: baseMonth,\n    getCellNode: getCellNode,\n    getCellText: function getCellText(date) {\n      return locale.monthFormat ? formatValue(date, {\n        locale: locale,\n        format: locale.monthFormat,\n        generateConfig: generateConfig\n      }) : monthsLocale[generateConfig.getMonth(date)];\n    },\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addMonth,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY-MM',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default MonthBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}