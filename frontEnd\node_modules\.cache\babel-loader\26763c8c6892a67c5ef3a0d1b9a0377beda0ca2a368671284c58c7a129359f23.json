{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nconst FormItemInput = props => {\n  const {\n    prefixCls,\n    status,\n    wrapperCol,\n    children,\n    errors,\n    warnings,\n    _internalItemRender: formItemRender,\n    extra,\n    help,\n    fieldId,\n    marginBottom,\n    onErrorVisibleChanged\n  } = props;\n  const baseClassName = `${prefixCls}-item`;\n  const formContext = React.useContext(FormContext);\n  const mergedWrapperCol = wrapperCol || formContext.wrapperCol || {};\n  const className = classNames(`${baseClassName}-control`, mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  const subFormContext = React.useMemo(() => Object.assign({}, formContext), [formContext]);\n  delete subFormContext.labelCol;\n  delete subFormContext.wrapperCol;\n  const inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input-content`\n  }, children));\n  const formItemContext = React.useMemo(() => ({\n    prefixCls,\n    status\n  }), [prefixCls, status]);\n  const errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexWrap: 'nowrap'\n    }\n  }, /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: `${baseClassName}-explain-connected`,\n    onVisibleChanged: onErrorVisibleChanged\n  })), !!marginBottom && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: 0,\n      height: marginBottom\n    }\n  })) : null;\n  const extraProps = {};\n  if (fieldId) {\n    extraProps.id = `${fieldId}_extra`;\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  const extraDom = extra ? /*#__PURE__*/React.createElement(\"div\", Object.assign({}, extraProps, {\n    className: `${baseClassName}-extra`\n  }), extra) : null;\n  const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : /*#__PURE__*/React.createElement(React.Fragment, null, inputDom, errorListDom, extraDom);\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedWrapperCol, {\n    className: className\n  }), dom));\n};\nexport default FormItemInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}