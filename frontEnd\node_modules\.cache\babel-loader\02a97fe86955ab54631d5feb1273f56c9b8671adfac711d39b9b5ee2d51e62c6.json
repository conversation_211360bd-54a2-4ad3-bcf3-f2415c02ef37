{"ast": null, "code": "import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen, customizedTrigger) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen,\n    customizedTrigger: customizedTrigger\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var _propsRef$current;\n      // If trigger is customized, <PERSON>gger will take control of popupVisible\n      if ((_propsRef$current = propsRef.current) !== null && _propsRef$current !== void 0 && _propsRef$current.customizedTrigger) {\n        return;\n      }\n      var target = event.target;\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}