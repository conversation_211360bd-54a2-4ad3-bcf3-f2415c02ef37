{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { warning } from 'rc-util';\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getWin } from \"../util\";\nexport default function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n  var openRef = React.useRef(open);\n\n  // Window click to hide should be lock to avoid trigger lock immediately\n  var lockRef = React.useRef(false);\n  if (openRef.current !== open) {\n    lockRef.current = true;\n    openRef.current = open;\n  }\n  React.useEffect(function () {\n    var id = raf(function () {\n      lockRef.current = false;\n    });\n    return function () {\n      raf.cancel(id);\n    };\n  }, [open]);\n\n  // Click to hide is special action since click popup element should not hide\n  React.useEffect(function () {\n    if (clickToHide && popupEle && (!mask || maskClosable)) {\n      var genClickEvents = function genClickEvents() {\n        var clickInside = false;\n\n        // User may mouseDown inside and drag out of popup and mouse up\n        // Record here to prevent close\n        var onWindowMouseDown = function onWindowMouseDown(_ref) {\n          var target = _ref.target;\n          clickInside = inPopupOrChild(target);\n        };\n        var onWindowClick = function onWindowClick(_ref2) {\n          var target = _ref2.target;\n          if (!lockRef.current && openRef.current && !clickInside && !inPopupOrChild(target)) {\n            triggerOpen(false);\n          }\n        };\n        return [onWindowMouseDown, onWindowClick];\n      };\n\n      // Events\n      var _genClickEvents = genClickEvents(),\n        _genClickEvents2 = _slicedToArray(_genClickEvents, 2),\n        onWinMouseDown = _genClickEvents2[0],\n        onWinClick = _genClickEvents2[1];\n      var _genClickEvents3 = genClickEvents(),\n        _genClickEvents4 = _slicedToArray(_genClickEvents3, 2),\n        onShadowMouseDown = _genClickEvents4[0],\n        onShadowClick = _genClickEvents4[1];\n      var win = getWin(popupEle);\n      win.addEventListener('mousedown', onWinMouseDown, true);\n      win.addEventListener('click', onWinClick, true);\n      win.addEventListener('contextmenu', onWinClick, true);\n\n      // shadow root\n      var targetShadowRoot = getShadowRoot(targetEle);\n      if (targetShadowRoot) {\n        targetShadowRoot.addEventListener('mousedown', onShadowMouseDown, true);\n        targetShadowRoot.addEventListener('click', onShadowClick, true);\n        targetShadowRoot.addEventListener('contextmenu', onShadowClick, true);\n      }\n\n      // Warning if target and popup not in same root\n      if (process.env.NODE_ENV !== 'production') {\n        var _targetEle$getRootNod, _popupEle$getRootNode;\n        var targetRoot = targetEle === null || targetEle === void 0 ? void 0 : (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n        var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n        warning(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n      }\n      return function () {\n        win.removeEventListener('mousedown', onWinMouseDown, true);\n        win.removeEventListener('click', onWinClick, true);\n        win.removeEventListener('contextmenu', onWinClick, true);\n        if (targetShadowRoot) {\n          targetShadowRoot.removeEventListener('mousedown', onShadowMouseDown, true);\n          targetShadowRoot.removeEventListener('click', onShadowClick, true);\n          targetShadowRoot.removeEventListener('contextmenu', onShadowClick, true);\n        }\n      };\n    }\n  }, [clickToHide, targetEle, popupEle, mask, maskClosable]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}