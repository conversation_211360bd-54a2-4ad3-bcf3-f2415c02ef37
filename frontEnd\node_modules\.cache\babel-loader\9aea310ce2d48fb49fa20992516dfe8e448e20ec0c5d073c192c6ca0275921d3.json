{"ast": null, "code": "import { responseImmutable, useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport Summary from \"./Summary\";\nimport SummaryContext from \"./SummaryContext\";\nfunction Footer(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var children = props.children,\n    stickyOffsets = props.stickyOffsets,\n    flattenColumns = props.flattenColumns,\n    columns = props.columns;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var lastColumnIndex = flattenColumns.length - 1;\n  var scrollColumn = flattenColumns[lastColumnIndex];\n  var summaryContext = React.useMemo(function () {\n    return {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns,\n      scrollColumnIndex: scrollColumn !== null && scrollColumn !== void 0 && scrollColumn.scrollbar ? lastColumnIndex : null,\n      columns: columns\n    };\n  }, [scrollColumn, flattenColumns, lastColumnIndex, stickyOffsets, columns]);\n  return /*#__PURE__*/React.createElement(SummaryContext.Provider, {\n    value: summaryContext\n  }, /*#__PURE__*/React.createElement(\"tfoot\", {\n    className: \"\".concat(prefixCls, \"-summary\")\n  }, children));\n}\nexport default responseImmutable(Footer);\nexport var FooterComponents = Summary;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}