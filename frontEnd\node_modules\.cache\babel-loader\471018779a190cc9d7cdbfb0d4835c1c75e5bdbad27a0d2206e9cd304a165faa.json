{"ast": null, "code": "import React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport classNames from 'classnames';\nfunction Star(props, ref) {\n  var disabled = props.disabled,\n    prefixCls = props.prefixCls,\n    character = props.character,\n    characterRender = props.characterRender,\n    index = props.index,\n    count = props.count,\n    value = props.value,\n    allowHalf = props.allowHalf,\n    focused = props.focused,\n    onHover = props.onHover,\n    onClick = props.onClick;\n  // =========================== Events ===========================\n  var onInternalHover = function onInternalHover(e) {\n    onHover(e, index);\n  };\n  var onInternalClick = function onInternalClick(e) {\n    onClick(e, index);\n  };\n  var onInternalKeyDown = function onInternalKeyDown(e) {\n    if (e.keyCode === KeyCode.ENTER) {\n      onClick(e, index);\n    }\n  };\n  // =========================== Render ===========================\n  // >>>>> ClassName\n  var starValue = index + 1;\n  var classNameList = new Set([prefixCls]);\n  // TODO: Current we just refactor from CC to FC. This logic seems can be optimized.\n  if (value === 0 && index === 0 && focused) {\n    classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n  } else if (allowHalf && value + 0.5 >= starValue && value < starValue) {\n    classNameList.add(\"\".concat(prefixCls, \"-half\"));\n    classNameList.add(\"\".concat(prefixCls, \"-active\"));\n    if (focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  } else {\n    if (starValue <= value) {\n      classNameList.add(\"\".concat(prefixCls, \"-full\"));\n    } else {\n      classNameList.add(\"\".concat(prefixCls, \"-zero\"));\n    }\n    if (starValue === value && focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  }\n  // >>>>> Node\n  var characterNode = typeof character === 'function' ? character(props) : character;\n  var start = /*#__PURE__*/React.createElement(\"li\", {\n    className: classNames(Array.from(classNameList)),\n    ref: ref\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onClick: disabled ? null : onInternalClick,\n    onKeyDown: disabled ? null : onInternalKeyDown,\n    onMouseMove: disabled ? null : onInternalHover,\n    role: \"radio\",\n    \"aria-checked\": value > index ? 'true' : 'false',\n    \"aria-posinset\": index + 1,\n    \"aria-setsize\": count,\n    tabIndex: disabled ? -1 : 0\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-first\")\n  }, characterNode), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-second\")\n  }, characterNode)));\n  if (characterRender) {\n    start = characterRender(start, props);\n  }\n  return start;\n}\nexport default /*#__PURE__*/React.forwardRef(Star);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}