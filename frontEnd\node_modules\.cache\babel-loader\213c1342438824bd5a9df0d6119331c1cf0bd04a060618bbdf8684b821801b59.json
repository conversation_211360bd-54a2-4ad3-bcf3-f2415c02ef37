{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// ================================== Cache ==================================\n\nexport function sameDerivativeOption(left, right) {\n  if (left.length !== right.length) {\n    return false;\n  }\n  for (var i = 0; i < left.length; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nvar ThemeCache = /*#__PURE__*/function () {\n  function ThemeCache() {\n    _classCallCheck(this, ThemeCache);\n    _defineProperty(this, \"cache\", void 0);\n    _defineProperty(this, \"keys\", void 0);\n    _defineProperty(this, \"cacheCallTimes\", void 0);\n    this.cache = new Map();\n    this.keys = [];\n    this.cacheCallTimes = 0;\n  }\n  _createClass(ThemeCache, [{\n    key: \"size\",\n    value: function size() {\n      return this.keys.length;\n    }\n  }, {\n    key: \"internalGet\",\n    value: function internalGet(derivativeOption) {\n      var _cache2, _cache3;\n      var updateCallTimes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var cache = {\n        map: this.cache\n      };\n      derivativeOption.forEach(function (derivative) {\n        if (!cache) {\n          cache = undefined;\n        } else {\n          var _cache, _cache$map;\n          cache = (_cache = cache) === null || _cache === void 0 ? void 0 : (_cache$map = _cache.map) === null || _cache$map === void 0 ? void 0 : _cache$map.get(derivative);\n        }\n      });\n      if ((_cache2 = cache) !== null && _cache2 !== void 0 && _cache2.value && updateCallTimes) {\n        cache.value[1] = this.cacheCallTimes++;\n      }\n      return (_cache3 = cache) === null || _cache3 === void 0 ? void 0 : _cache3.value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(derivativeOption) {\n      var _this$internalGet;\n      return (_this$internalGet = this.internalGet(derivativeOption, true)) === null || _this$internalGet === void 0 ? void 0 : _this$internalGet[0];\n    }\n  }, {\n    key: \"has\",\n    value: function has(derivativeOption) {\n      return !!this.internalGet(derivativeOption);\n    }\n  }, {\n    key: \"set\",\n    value: function set(derivativeOption, value) {\n      var _this = this;\n      // New cache\n      if (!this.has(derivativeOption)) {\n        if (this.size() + 1 > ThemeCache.MAX_CACHE_SIZE + ThemeCache.MAX_CACHE_OFFSET) {\n          var _this$keys$reduce = this.keys.reduce(function (result, key) {\n              var _result = _slicedToArray(result, 2),\n                callTimes = _result[1];\n              if (_this.internalGet(key)[1] < callTimes) {\n                return [key, _this.internalGet(key)[1]];\n              }\n              return result;\n            }, [this.keys[0], this.cacheCallTimes]),\n            _this$keys$reduce2 = _slicedToArray(_this$keys$reduce, 1),\n            targetKey = _this$keys$reduce2[0];\n          this.delete(targetKey);\n        }\n        this.keys.push(derivativeOption);\n      }\n      var cache = this.cache;\n      derivativeOption.forEach(function (derivative, index) {\n        if (index === derivativeOption.length - 1) {\n          cache.set(derivative, {\n            value: [value, _this.cacheCallTimes++]\n          });\n        } else {\n          var cacheValue = cache.get(derivative);\n          if (!cacheValue) {\n            cache.set(derivative, {\n              map: new Map()\n            });\n          } else if (!cacheValue.map) {\n            cacheValue.map = new Map();\n          }\n          cache = cache.get(derivative).map;\n        }\n      });\n    }\n  }, {\n    key: \"deleteByPath\",\n    value: function deleteByPath(currentCache, derivatives) {\n      var cache = currentCache.get(derivatives[0]);\n      if (derivatives.length === 1) {\n        var _cache$value;\n        if (!cache.map) {\n          currentCache.delete(derivatives[0]);\n        } else {\n          currentCache.set(derivatives[0], {\n            map: cache.map\n          });\n        }\n        return (_cache$value = cache.value) === null || _cache$value === void 0 ? void 0 : _cache$value[0];\n      }\n      var result = this.deleteByPath(cache.map, derivatives.slice(1));\n      if ((!cache.map || cache.map.size === 0) && !cache.value) {\n        currentCache.delete(derivatives[0]);\n      }\n      return result;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(derivativeOption) {\n      // If cache exists\n      if (this.has(derivativeOption)) {\n        this.keys = this.keys.filter(function (item) {\n          return !sameDerivativeOption(item, derivativeOption);\n        });\n        return this.deleteByPath(this.cache, derivativeOption);\n      }\n      return undefined;\n    }\n  }]);\n  return ThemeCache;\n}();\n_defineProperty(ThemeCache, \"MAX_CACHE_SIZE\", 20);\n_defineProperty(ThemeCache, \"MAX_CACHE_OFFSET\", 5);\nexport { ThemeCache as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}