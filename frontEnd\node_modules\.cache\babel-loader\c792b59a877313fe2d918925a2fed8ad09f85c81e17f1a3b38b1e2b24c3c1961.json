{"ast": null, "code": "function loopFiles(item, callback) {\n  var dirReader = item.createReader();\n  var fileList = [];\n  function sequence() {\n    dirReader.readEntries(function (entries) {\n      var entryList = Array.prototype.slice.apply(entries);\n      fileList = fileList.concat(entryList); // Check if all the file has been viewed\n\n      var isFinished = !entryList.length;\n      if (isFinished) {\n        callback(fileList);\n      } else {\n        sequence();\n      }\n    });\n  }\n  sequence();\n}\nvar traverseFileTree = function traverseFileTree(files, callback, isAccepted) {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  var _traverseFileTree = function _traverseFileTree(item, path) {\n    // eslint-disable-next-line no-param-reassign\n    item.path = path || '';\n    if (item.isFile) {\n      item.file(function (file) {\n        if (isAccepted(file)) {\n          // https://github.com/ant-design/ant-design/issues/16426\n          if (item.fullPath && !file.webkitRelativePath) {\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: true\n              }\n            }); // eslint-disable-next-line no-param-reassign\n\n            file.webkitRelativePath = item.fullPath.replace(/^\\//, '');\n            Object.defineProperties(file, {\n              webkitRelativePath: {\n                writable: false\n              }\n            });\n          }\n          callback([file]);\n        }\n      });\n    } else if (item.isDirectory) {\n      loopFiles(item, function (entries) {\n        entries.forEach(function (entryItem) {\n          _traverseFileTree(entryItem, \"\".concat(path).concat(item.name, \"/\"));\n        });\n      });\n    }\n  };\n  files.forEach(function (file) {\n    _traverseFileTree(file.webkitGetAsEntry());\n  });\n};\nexport default traverseFileTree;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}