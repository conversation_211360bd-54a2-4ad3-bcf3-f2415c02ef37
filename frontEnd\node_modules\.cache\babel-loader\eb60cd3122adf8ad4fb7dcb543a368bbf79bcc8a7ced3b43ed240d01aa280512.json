{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from \"../Header\";\nimport PanelContext from \"../../PanelContext\";\nimport { formatValue } from \"../../utils/dateUtil\";\nfunction DateHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    locale = props.locale,\n    viewDate = props.viewDate,\n    onNextMonth = props.onNextMonth,\n    onPrevMonth = props.onPrevMonth,\n    onNextYear = props.onNextYear,\n    onPrevYear = props.onPrevYear,\n    onYearClick = props.onYearClick,\n    onMonthClick = props.onMonthClick;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var monthsLocale = locale.shortMonths || (generateConfig.locale.getShortMonths ? generateConfig.locale.getShortMonths(locale.locale) : []);\n  var month = generateConfig.getMonth(viewDate);\n\n  // =================== Month & Year ===================\n  var yearNode = /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    key: \"year\",\n    onClick: onYearClick,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-year-btn\")\n  }, formatValue(viewDate, {\n    locale: locale,\n    format: locale.yearFormat,\n    generateConfig: generateConfig\n  }));\n  var monthNode = /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    key: \"month\",\n    onClick: onMonthClick,\n    tabIndex: -1,\n    className: \"\".concat(prefixCls, \"-month-btn\")\n  }, locale.monthFormat ? formatValue(viewDate, {\n    locale: locale,\n    format: locale.monthFormat,\n    generateConfig: generateConfig\n  }) : monthsLocale[month]);\n  var monthYearNodes = locale.monthBeforeYear ? [monthNode, yearNode] : [yearNode, monthNode];\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevYear,\n    onPrev: onPrevMonth,\n    onNext: onNextMonth,\n    onSuperNext: onNextYear\n  }), monthYearNodes);\n}\nexport default DateHeader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}