{"ast": null, "code": "import * as React from 'react';\nimport { getQuarter, isSameDate } from \"../utils/dateUtil\";\nimport { getValue } from \"../utils/miscUtil\";\nexport default function useRangeDisabled(_ref, firstTimeOpen) {\n  var picker = _ref.picker,\n    locale = _ref.locale,\n    selectedValue = _ref.selectedValue,\n    disabledDate = _ref.disabledDate,\n    disabled = _ref.disabled,\n    generateConfig = _ref.generateConfig;\n  var startDate = getValue(selectedValue, 0);\n  var endDate = getValue(selectedValue, 1);\n  function weekFirstDate(date) {\n    return generateConfig.locale.getWeekFirstDate(locale.locale, date);\n  }\n  function monthNumber(date) {\n    var year = generateConfig.getYear(date);\n    var month = generateConfig.getMonth(date);\n    return year * 100 + month;\n  }\n  function quarterNumber(date) {\n    var year = generateConfig.getYear(date);\n    var quarter = getQuarter(generateConfig, date);\n    return year * 10 + quarter;\n  }\n  var disabledStartDate = React.useCallback(function (date) {\n    if (disabled[0] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[1] && endDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && endDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) > quarterNumber(endDate);\n        case 'month':\n          return monthNumber(date) > monthNumber(endDate);\n        case 'week':\n          return weekFirstDate(date) > weekFirstDate(endDate);\n        default:\n          return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(date, endDate);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[1], endDate, firstTimeOpen]);\n  var disabledEndDate = React.useCallback(function (date) {\n    if (disabled[1] || disabledDate && disabledDate(date)) {\n      return true;\n    }\n\n    // Disabled range\n    if (disabled[0] && startDate) {\n      return !isSameDate(generateConfig, date, endDate) && generateConfig.isAfter(startDate, date);\n    }\n\n    // Disabled part\n    if (!firstTimeOpen && startDate) {\n      switch (picker) {\n        case 'quarter':\n          return quarterNumber(date) < quarterNumber(startDate);\n        case 'month':\n          return monthNumber(date) < monthNumber(startDate);\n        case 'week':\n          return weekFirstDate(date) < weekFirstDate(startDate);\n        default:\n          return !isSameDate(generateConfig, date, startDate) && generateConfig.isAfter(startDate, date);\n      }\n    }\n    return false;\n  }, [disabledDate, disabled[0], startDate, firstTimeOpen]);\n  return [disabledStartDate, disabledEndDate];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}