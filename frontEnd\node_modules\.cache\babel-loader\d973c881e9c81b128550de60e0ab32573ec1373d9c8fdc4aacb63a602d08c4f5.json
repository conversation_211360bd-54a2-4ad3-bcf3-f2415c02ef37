{"ast": null, "code": "import { useRef } from 'react';\nimport raf from \"rc-util/es/raf\";\nimport isFF from '../utils/isFirefox';\nimport useOriginScroll from './useOriginScroll';\nexport default function useFrameWheel(inVirtual, isScrollAtTop, isScrollAtBottom, onWheelDelta) {\n  var offsetRef = useRef(0);\n  var nextFrameRef = useRef(null);\n  // Firefox patch\n  var wheelValueRef = useRef(null);\n  var isMouseScrollRef = useRef(false);\n  // Scroll status sync\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom);\n  function onWheel(event) {\n    if (!inVirtual) return;\n    raf.cancel(nextFrameRef.current);\n    var deltaY = event.deltaY;\n    offsetRef.current += deltaY;\n    wheelValueRef.current = deltaY;\n    // Do nothing when scroll at the edge, Skip check when is in scroll\n    if (originScroll(deltaY)) return;\n    // Proxy of scroll events\n    if (!isFF) {\n      event.preventDefault();\n    }\n    nextFrameRef.current = raf(function () {\n      // Patch a multiple for Firefox to fix wheel number too small\n      // ref: https://github.com/ant-design/ant-design/issues/26372#issuecomment-679460266\n      var patchMultiple = isMouseScrollRef.current ? 10 : 1;\n      onWheelDelta(offsetRef.current * patchMultiple);\n      offsetRef.current = 0;\n    });\n  }\n  // A patch for firefox\n  function onFireFoxScroll(event) {\n    if (!inVirtual) return;\n    isMouseScrollRef.current = event.detail === wheelValueRef.current;\n  }\n  return [onWheel, onFireFoxScroll];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}