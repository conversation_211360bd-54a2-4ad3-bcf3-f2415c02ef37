{"ast": null, "code": "import { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    itemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    itemMarginBlock\n  } = token;\n  const paddingWithArrow = padding + menuArrowSize + marginXS;\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: `${itemHeight}px`,\n      paddingInline: padding,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: itemMarginBlock,\n      width: `calc(100% - ${itemMarginInline * 2}px)`\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: `${itemHeight}px`\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    itemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionDurationMid,\n    motionEaseOut,\n    paddingXL,\n    itemMarginInline,\n    fontSizeLG,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary,\n    collapsedWidth,\n    collapsedIconSize\n  } = token;\n  const inlineItemStyle = {\n    height: itemHeight,\n    lineHeight: `${itemHeight}px`,\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      [`&-inline, &-vertical`]: Object.assign({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: Object.assign(Object.assign({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${controlHeightLG * 2.5}px)`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationMid} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: collapsedWidth,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${fontSizeLG / 2}px - ${itemMarginInline}px)`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: collapsedIconSize,\n          lineHeight: `${itemHeight}px`,\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}