{"ast": null, "code": "import { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nexport var VALUE_SPLIT = '__RC_CASCADER_SPLIT__';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\n\n/**\n * Will convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKey(value) {\n  return value.join(VALUE_SPLIT);\n}\n\n/**\n * Batch convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKeys(value) {\n  return value.map(toPathKey);\n}\nexport function toPathValueStr(pathKey) {\n  return pathKey.split(VALUE_SPLIT);\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  var val = value || 'value';\n  return {\n    label: label || 'label',\n    value: val,\n    key: val,\n    children: children || 'children'\n  };\n}\nexport function isLeaf(option, fieldNames) {\n  var _option$isLeaf, _option$fieldNames$ch;\n  return (_option$isLeaf = option.isLeaf) !== null && _option$isLeaf !== void 0 ? _option$isLeaf : !((_option$fieldNames$ch = option[fieldNames.children]) !== null && _option$fieldNames$ch !== void 0 && _option$fieldNames$ch.length);\n}\nexport function scrollIntoParentView(element) {\n  var parent = element.parentElement;\n  if (!parent) {\n    return;\n  }\n  var elementToParent = element.offsetTop - parent.offsetTop; // offsetParent may not be parent.\n  if (elementToParent - parent.scrollTop < 0) {\n    parent.scrollTo({\n      top: elementToParent\n    });\n  } else if (elementToParent + element.offsetHeight - parent.scrollTop > parent.offsetHeight) {\n    parent.scrollTo({\n      top: elementToParent + element.offsetHeight - parent.offsetHeight\n    });\n  }\n}\nexport function getFullPathKeys(options, fieldNames) {\n  return options.map(function (item) {\n    var _item$SEARCH_MARK;\n    return (_item$SEARCH_MARK = item[SEARCH_MARK]) === null || _item$SEARCH_MARK === void 0 ? void 0 : _item$SEARCH_MARK.map(function (opt) {\n      return opt[fieldNames.value];\n    });\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}