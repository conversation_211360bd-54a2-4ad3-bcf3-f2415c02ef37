{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * This function will try to call requestIdleCallback if available to save performance.\n * No need `getLabel` here since already fetch on `rawLabeledValue`.\n */\nexport default (function (values) {\n  var cacheRef = React.useRef({\n    valueLabels: new Map()\n  });\n  return React.useMemo(function () {\n    var valueLabels = cacheRef.current.valueLabels;\n    var valueLabelsCache = new Map();\n    var filledValues = values.map(function (item) {\n      var _item$label;\n      var value = item.value;\n      var mergedLabel = (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : valueLabels.get(value);\n\n      // Save in cache\n      valueLabelsCache.set(value, mergedLabel);\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: mergedLabel\n      });\n    });\n    cacheRef.current.valueLabels = valueLabelsCache;\n    return [filledValues];\n  }, [values]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}