{"ast": null, "code": "import { complex } from '../../value/types/complex/index.mjs';\n\n/**\n * Check if a value is animatable. Examples:\n *\n * ✅: 100, \"100px\", \"#fff\"\n * ❌: \"block\", \"url(2.jpg)\"\n * @param value\n *\n * @internal\n */\nconst isAnimatable = (key, value) => {\n  // If the list of keys tat might be non-animatable grows, replace with Set\n  if (key === \"zIndex\") return false;\n  // If it's a number or a keyframes array, we can animate it. We might at some point\n  // need to do a deep isAnimatable check of keyframes, or let Popmotion handle this,\n  // but for now lets leave it like this for performance reasons\n  if (typeof value === \"number\" || Array.isArray(value)) return true;\n  if (typeof value === \"string\" && (\n  // It's animatable if we have a string\n  complex.test(value) || value === \"0\") &&\n  // And it contains numbers and/or colors\n  !value.startsWith(\"url(\") // Unless it starts with \"url(\"\n  ) {\n    return true;\n  }\n  return false;\n};\nexport { isAnimatable };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}