{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nvar Input = function Input(_ref, ref) {\n  var _inputNode2, _inputNode2$props;\n  var prefixCls = _ref.prefixCls,\n    id = _ref.id,\n    inputElement = _ref.inputElement,\n    disabled = _ref.disabled,\n    tabIndex = _ref.tabIndex,\n    autoFocus = _ref.autoFocus,\n    autoComplete = _ref.autoComplete,\n    editable = _ref.editable,\n    activeDescendantId = _ref.activeDescendantId,\n    value = _ref.value,\n    maxLength = _ref.maxLength,\n    _onKeyDown = _ref.onKeyDown,\n    _onMouseDown = _ref.onMouseDown,\n    _onChange = _ref.onChange,\n    onPaste = _ref.onPaste,\n    _onCompositionStart = _ref.onCompositionStart,\n    _onCompositionEnd = _ref.onCompositionEnd,\n    open = _ref.open,\n    attrs = _ref.attrs;\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  var onOriginKeyDown = originProps.onKeyDown,\n    onOriginChange = originProps.onChange,\n    onOriginMouseDown = originProps.onMouseDown,\n    onOriginCompositionStart = originProps.onCompositionStart,\n    onOriginCompositionEnd = originProps.onCompositionEnd,\n    style = originProps.style;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, originProps), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    disabled: disabled,\n    tabIndex: tabIndex,\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), (_inputNode2 = inputNode) === null || _inputNode2 === void 0 ? void 0 : (_inputNode2$props = _inputNode2.props) === null || _inputNode2$props === void 0 ? void 0 : _inputNode2$props.className),\n    role: 'combobox',\n    'aria-label': 'Search',\n    'aria-expanded': open,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    maxLength: maxLength,\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      opacity: editable ? null : 0\n    }),\n    onKeyDown: function onKeyDown(event) {\n      _onKeyDown(event);\n      if (onOriginKeyDown) {\n        onOriginKeyDown(event);\n      }\n    },\n    onMouseDown: function onMouseDown(event) {\n      _onMouseDown(event);\n      if (onOriginMouseDown) {\n        onOriginMouseDown(event);\n      }\n    },\n    onChange: function onChange(event) {\n      _onChange(event);\n      if (onOriginChange) {\n        onOriginChange(event);\n      }\n    },\n    onCompositionStart: function onCompositionStart(event) {\n      _onCompositionStart(event);\n      if (onOriginCompositionStart) {\n        onOriginCompositionStart(event);\n      }\n    },\n    onCompositionEnd: function onCompositionEnd(event) {\n      _onCompositionEnd(event);\n      if (onOriginCompositionEnd) {\n        onOriginCompositionEnd(event);\n      }\n    },\n    onPaste: onPaste\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nRefInput.displayName = 'Input';\nexport default RefInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}