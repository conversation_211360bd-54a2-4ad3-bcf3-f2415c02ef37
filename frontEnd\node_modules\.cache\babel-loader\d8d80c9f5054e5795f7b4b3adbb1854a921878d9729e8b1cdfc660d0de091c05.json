{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const zoomIn = new Keyframes('antZoomIn', {\n  '0%': {\n    transform: 'scale(0.2)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    opacity: 1\n  }\n});\nexport const zoomOut = new Keyframes('antZoomOut', {\n  '0%': {\n    transform: 'scale(1)'\n  },\n  '100%': {\n    transform: 'scale(0.2)',\n    opacity: 0\n  }\n});\nexport const zoomBigIn = new Keyframes('antZoomBigIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    opacity: 1\n  }\n});\nexport const zoomBigOut = new Keyframes('antZoomBigOut', {\n  '0%': {\n    transform: 'scale(1)'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    opacity: 0\n  }\n});\nexport const zoomUpIn = new Keyframes('antZoomUpIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 0%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 0%'\n  }\n});\nexport const zoomUpOut = new Keyframes('antZoomUpOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 0%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 0%',\n    opacity: 0\n  }\n});\nexport const zoomLeftIn = new Keyframes('antZoomLeftIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '0% 50%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '0% 50%'\n  }\n});\nexport const zoomLeftOut = new Keyframes('antZoomLeftOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '0% 50%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '0% 50%',\n    opacity: 0\n  }\n});\nexport const zoomRightIn = new Keyframes('antZoomRightIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '100% 50%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '100% 50%'\n  }\n});\nexport const zoomRightOut = new Keyframes('antZoomRightOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '100% 50%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '100% 50%',\n    opacity: 0\n  }\n});\nexport const zoomDownIn = new Keyframes('antZoomDownIn', {\n  '0%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 100%',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 100%'\n  }\n});\nexport const zoomDownOut = new Keyframes('antZoomDownOut', {\n  '0%': {\n    transform: 'scale(1)',\n    transformOrigin: '50% 100%'\n  },\n  '100%': {\n    transform: 'scale(0.8)',\n    transformOrigin: '50% 100%',\n    opacity: 0\n  }\n});\nconst zoomMotion = {\n  zoom: {\n    inKeyframes: zoomIn,\n    outKeyframes: zoomOut\n  },\n  'zoom-big': {\n    inKeyframes: zoomBigIn,\n    outKeyframes: zoomBigOut\n  },\n  'zoom-big-fast': {\n    inKeyframes: zoomBigIn,\n    outKeyframes: zoomBigOut\n  },\n  'zoom-left': {\n    inKeyframes: zoomLeftIn,\n    outKeyframes: zoomLeftOut\n  },\n  'zoom-right': {\n    inKeyframes: zoomRightIn,\n    outKeyframes: zoomRightOut\n  },\n  'zoom-up': {\n    inKeyframes: zoomUpIn,\n    outKeyframes: zoomUpOut\n  },\n  'zoom-down': {\n    inKeyframes: zoomDownIn,\n    outKeyframes: zoomDownOut\n  }\n};\nexport const initZoomMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = zoomMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, motionName === 'zoom-big-fast' ? token.motionDurationFast : token.motionDurationMid), {\n    [`\n        ${motionCls}-enter,\n        ${motionCls}-appear\n      `]: {\n      transform: 'scale(0)',\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutCirc,\n      '&-prepare': {\n        transform: 'none'\n      }\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInOutCirc\n    }\n  }];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}