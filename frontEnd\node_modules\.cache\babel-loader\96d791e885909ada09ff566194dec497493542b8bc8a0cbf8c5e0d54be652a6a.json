{"ast": null, "code": "const genStatusStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    colorError,\n    colorTextLightSolid\n  } = token;\n  const itemCls = `${menuCls}-item`;\n  return {\n    [`${componentCls}, ${componentCls}-menu-submenu`]: {\n      [`${menuCls} ${itemCls}`]: {\n        [`&${itemCls}-danger:not(${itemCls}-disabled)`]: {\n          color: colorError,\n          '&:hover': {\n            color: colorTextLightSolid,\n            backgroundColor: colorError\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStatusStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}