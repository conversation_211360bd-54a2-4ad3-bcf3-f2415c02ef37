{"ast": null, "code": "/*!\n  Copyright (c) 2015 <PERSON>.\n  Based on code that is Copyright 2013-2015, Facebook, Inc.\n  All rights reserved.\n*/\n/* global define */\n\n(function () {\n  'use strict';\n\n  var canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n  var ExecutionEnvironment = {\n    canUseDOM: canUseDOM,\n    canUseWorkers: typeof Worker !== 'undefined',\n    canUseEventListeners: canUseDOM && !!(window.addEventListener || window.attachEvent),\n    canUseViewport: canUseDOM && !!window.screen\n  };\n  if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n    define(function () {\n      return ExecutionEnvironment;\n    });\n  } else if (typeof module !== 'undefined' && module.exports) {\n    module.exports = ExecutionEnvironment;\n  } else {\n    window.ExecutionEnvironment = ExecutionEnvironment;\n  }\n})();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}