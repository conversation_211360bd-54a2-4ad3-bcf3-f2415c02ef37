{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nexports.handleBlur = handleBlur;\nexports.handleFocus = handleFocus;\nexports.markForFocusLater = markForFocusLater;\nexports.returnFocus = returnFocus;\nexports.popWithoutFocus = popWithoutFocus;\nexports.setupScopedFocus = setupScopedFocus;\nexports.teardownScopedFocus = teardownScopedFocus;\nvar _tabbable = require(\"../helpers/tabbable\");\nvar _tabbable2 = _interopRequireDefault(_tabbable);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar focusLaterElements = [];\nvar modalElement = null;\nvar needToFocus = false;\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  focusLaterElements = [];\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    console.log(\"focusManager ----------\");\n    focusLaterElements.forEach(function (f) {\n      var check = f || {};\n      console.log(check.nodeName, check.className, check.id);\n    });\n    console.log(\"end focusManager ----------\");\n  }\n}\n/* eslint-enable no-console */\n\nfunction handleBlur() {\n  needToFocus = true;\n}\nfunction handleFocus() {\n  if (needToFocus) {\n    needToFocus = false;\n    if (!modalElement) {\n      return;\n    }\n    // need to see how jQuery shims document.on('focusin') so we don't need the\n    // setTimeout, firefox doesn't support focusin, if it did, we could focus\n    // the element outside of a setTimeout. Side-effect of this implementation\n    // is that the document.body gets focus, and then we focus our element right\n    // after, seems fine.\n    setTimeout(function () {\n      if (modalElement.contains(document.activeElement)) {\n        return;\n      }\n      var el = (0, _tabbable2.default)(modalElement)[0] || modalElement;\n      el.focus();\n    }, 0);\n  }\n}\nfunction markForFocusLater() {\n  focusLaterElements.push(document.activeElement);\n}\n\n/* eslint-disable no-console */\nfunction returnFocus() {\n  var preventScroll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  var toFocus = null;\n  try {\n    if (focusLaterElements.length !== 0) {\n      toFocus = focusLaterElements.pop();\n      toFocus.focus({\n        preventScroll: preventScroll\n      });\n    }\n    return;\n  } catch (e) {\n    console.warn([\"You tried to return focus to\", toFocus, \"but it is not in the DOM anymore\"].join(\" \"));\n  }\n}\n/* eslint-enable no-console */\n\nfunction popWithoutFocus() {\n  focusLaterElements.length > 0 && focusLaterElements.pop();\n}\nfunction setupScopedFocus(element) {\n  modalElement = element;\n  if (window.addEventListener) {\n    window.addEventListener(\"blur\", handleBlur, false);\n    document.addEventListener(\"focus\", handleFocus, true);\n  } else {\n    window.attachEvent(\"onBlur\", handleBlur);\n    document.attachEvent(\"onFocus\", handleFocus);\n  }\n}\nfunction teardownScopedFocus() {\n  modalElement = null;\n  if (window.addEventListener) {\n    window.removeEventListener(\"blur\", handleBlur);\n    document.removeEventListener(\"focus\", handleFocus);\n  } else {\n    window.detachEvent(\"onBlur\", handleBlur);\n    document.detachEvent(\"onFocus\", handleFocus);\n  }\n}", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}