const paymentValidator = require('./paymentValidator');
const User = require('../models/userModel');
const Subscription = require('../models/subscriptionModel');

/**
 * SECURE SUBSCRIPTION ACTIVATION UTILITY
 * 
 * This utility ensures that subscriptions are only activated after:
 * 1. Payment is verified with ZenoPay API
 * 2. Manual admin approval is given
 * 3. All security checks pass
 * 
 * SECURITY FEATURES:
 * - No automatic activation
 * - Requires manual admin approval
 * - Validates payment status before activation
 * - Prevents activation of cancelled/failed payments
 * - Comprehensive audit logging
 */

class SecureActivation {
  constructor() {
    this.autoActivationEnabled = false; // SECURITY: Never auto-activate
    this.requireManualApproval = true; // SECURITY: Always require manual approval
  }

  /**
   * Request activation for a subscription (does not activate automatically)
   * @param {Object} subscription - The subscription to request activation for
   * @param {string} adminUserId - The admin user requesting activation (optional)
   * @returns {Object} Activation request result
   */
  async requestActivation(subscription, adminUserId = null) {
    try {
      console.log(`🔍 Processing activation request for subscription: ${subscription._id}`);

      // Validate payment first
      const validationResult = await paymentValidator.validateSubscriptionForActivation(subscription);
      
      if (!validationResult.canActivate) {
        console.log(`❌ Activation request denied: ${validationResult.reason}`);
        
        // Log the failed request
        await this.logActivationRequest(subscription, 'DENIED', validationResult.reason, adminUserId);
        
        return {
          success: false,
          activated: false,
          reason: validationResult.reason,
          message: validationResult.message,
          requiresManualReview: true
        };
      }

      // Mark subscription as pending manual activation
      subscription.activationStatus = 'pending_manual_approval';
      subscription.paymentVerified = true;
      subscription.verifiedAt = new Date();
      subscription.activationRequestedAt = new Date();
      subscription.activationRequestedBy = adminUserId;

      // Update payment history
      if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        latestPayment.verificationStatus = 'verified_pending_activation';
        latestPayment.verifiedAt = new Date();
      }

      await subscription.save();

      // Log the activation request
      await this.logActivationRequest(subscription, 'REQUESTED', 'payment_verified', adminUserId);

      console.log(`✅ Activation request logged for subscription: ${subscription._id}`);
      console.log(`🔒 MANUAL ADMIN APPROVAL REQUIRED`);

      return {
        success: true,
        activated: false,
        status: 'pending_manual_approval',
        message: 'Payment verified. Manual admin approval required for activation.',
        subscriptionId: subscription._id,
        orderId: validationResult.orderId,
        requiresManualReview: true
      };

    } catch (error) {
      console.error(`❌ Error processing activation request:`, error.message);
      
      await this.logActivationRequest(subscription, 'ERROR', error.message, adminUserId);
      
      return {
        success: false,
        activated: false,
        reason: 'processing_error',
        message: `Error processing activation request: ${error.message}`,
        requiresManualReview: true
      };
    }
  }

  /**
   * Manually activate a subscription (admin only)
   * @param {string} subscriptionId - The subscription ID to activate
   * @param {string} adminUserId - The admin user performing the activation
   * @param {string} reason - Reason for manual activation
   * @returns {Object} Activation result
   */
  async manuallyActivateSubscription(subscriptionId, adminUserId, reason = 'manual_admin_approval') {
    try {
      console.log(`🔐 Manual activation requested by admin ${adminUserId} for subscription: ${subscriptionId}`);

      const subscription = await Subscription.findById(subscriptionId).populate('activePlan');
      if (!subscription) {
        return {
          success: false,
          message: 'Subscription not found'
        };
      }

      // Final payment validation before activation
      const validationResult = await paymentValidator.validateSubscriptionForActivation(subscription);
      
      if (!validationResult.canActivate) {
        console.log(`❌ Manual activation denied - payment validation failed: ${validationResult.reason}`);
        
        await this.logActivationRequest(subscription, 'MANUAL_DENIED', validationResult.reason, adminUserId);
        
        return {
          success: false,
          message: `Cannot activate: ${validationResult.message}`,
          reason: validationResult.reason
        };
      }

      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = subscription.activePlan?.duration || 1;
      endDate.setMonth(endDate.getMonth() + planDuration);

      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];

      // Update subscription
      subscription.paymentStatus = 'paid';
      subscription.status = 'active';
      subscription.startDate = formattedStartDate;
      subscription.endDate = formattedEndDate;
      subscription.activatedAt = new Date();
      subscription.activatedBy = adminUserId;
      subscription.activationMethod = 'manual_admin';
      subscription.activationReason = reason;

      // Update payment history
      if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.activatedAt = new Date();
        latestPayment.activatedBy = adminUserId;
      }

      await subscription.save();

      // Update user status
      const user = await User.findById(subscription.user);
      if (user) {
        user.subscriptionStatus = 'active';
        user.paymentRequired = false;
        user.subscriptionEndDate = new Date(formattedEndDate);
        user.subscriptionActivatedAt = new Date();
        await user.save();

        console.log(`✅ Manually activated subscription for ${user.firstName} ${user.lastName}`);
        console.log(`📅 Subscription period: ${formattedStartDate} to ${formattedEndDate}`);
        console.log(`📋 Plan: ${subscription.activePlan?.title}`);
        console.log(`👤 Activated by admin: ${adminUserId}`);
      }

      // Log the successful activation
      await this.logActivationRequest(subscription, 'ACTIVATED', reason, adminUserId);

      return {
        success: true,
        activated: true,
        message: 'Subscription activated successfully',
        subscriptionId: subscription._id,
        userId: subscription.user,
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        activatedBy: adminUserId
      };

    } catch (error) {
      console.error(`❌ Error in manual activation:`, error.message);
      
      return {
        success: false,
        message: `Activation error: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Get subscriptions pending manual activation
   * @returns {Array} List of subscriptions pending activation
   */
  async getPendingActivations() {
    try {
      const pendingSubscriptions = await Subscription.find({
        paymentStatus: 'paid',
        status: 'pending',
        paymentVerified: true,
        $or: [
          { activationStatus: 'pending_manual_approval' },
          { requiresManualActivation: true }
        ]
      }).populate('user activePlan');

      console.log(`📋 Found ${pendingSubscriptions.length} subscriptions pending manual activation`);

      return pendingSubscriptions.map(sub => ({
        subscriptionId: sub._id,
        userId: sub.user._id,
        userName: `${sub.user.firstName} ${sub.user.lastName}`,
        userEmail: sub.user.email,
        planTitle: sub.activePlan?.title,
        createdAt: sub.createdAt,
        verifiedAt: sub.verifiedAt,
        orderId: sub.paymentHistory?.[sub.paymentHistory.length - 1]?.orderId
      }));

    } catch (error) {
      console.error(`❌ Error getting pending activations:`, error.message);
      return [];
    }
  }

  /**
   * Log activation request for audit purposes
   * @param {Object} subscription - The subscription
   * @param {string} action - The action taken
   * @param {string} reason - The reason for the action
   * @param {string} adminUserId - The admin user (if applicable)
   */
  async logActivationRequest(subscription, action, reason, adminUserId = null) {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        action,
        reason,
        subscriptionId: subscription._id,
        userId: subscription.user,
        adminUserId,
        orderId: subscription.paymentHistory?.[subscription.paymentHistory.length - 1]?.orderId
      };

      console.log(`📝 ACTIVATION AUDIT LOG [${timestamp}]:`);
      console.log(`   Action: ${action}`);
      console.log(`   Subscription: ${subscription._id}`);
      console.log(`   User: ${subscription.user}`);
      console.log(`   Reason: ${reason}`);
      console.log(`   Admin: ${adminUserId || 'N/A'}`);

      // In production, this should be stored in a secure audit log database
      // For now, we'll just console log for visibility

    } catch (error) {
      console.error(`❌ Error logging activation request:`, error.message);
    }
  }
}

// Export singleton instance
const secureActivation = new SecureActivation();
module.exports = secureActivation;
