{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nvar _excluded = [\"component\", \"prefixCls\", \"className\", \"disabled\", \"id\", \"style\", \"multiple\", \"accept\", \"capture\", \"children\", \"directory\", \"openFileDialogOnClick\", \"onMouseEnter\", \"onMouseLeave\"];\nimport React, { Component } from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport defaultRequest from './request';\nimport getUid from './uid';\nimport attrAccept from './attr-accept';\nimport traverseFileTree from './traverseFileTree';\nvar AjaxUploader = /*#__PURE__*/function (_Component) {\n  _inherits(AjaxUploader, _Component);\n  var _super = _createSuper(AjaxUploader);\n  function AjaxUploader() {\n    var _this;\n    _classCallCheck(this, AjaxUploader);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _this.state = {\n      uid: getUid()\n    };\n    _this.reqs = {};\n    _this.fileInput = void 0;\n    _this._isMounted = void 0;\n    _this.onChange = function (e) {\n      var _this$props = _this.props,\n        accept = _this$props.accept,\n        directory = _this$props.directory;\n      var files = e.target.files;\n      var acceptedFiles = _toConsumableArray(files).filter(function (file) {\n        return !directory || attrAccept(file, accept);\n      });\n      _this.uploadFiles(acceptedFiles);\n      _this.reset();\n    };\n    _this.onClick = function (e) {\n      var el = _this.fileInput;\n      if (!el) {\n        return;\n      }\n      var _this$props2 = _this.props,\n        children = _this$props2.children,\n        onClick = _this$props2.onClick;\n      if (children && children.type === 'button') {\n        var parent = el.parentNode;\n        parent.focus();\n        parent.querySelector('button').blur();\n      }\n      el.click();\n      if (onClick) {\n        onClick(e);\n      }\n    };\n    _this.onKeyDown = function (e) {\n      if (e.key === 'Enter') {\n        _this.onClick(e);\n      }\n    };\n    _this.onFileDrop = function (e) {\n      var multiple = _this.props.multiple;\n      e.preventDefault();\n      if (e.type === 'dragover') {\n        return;\n      }\n      if (_this.props.directory) {\n        traverseFileTree(Array.prototype.slice.call(e.dataTransfer.items), _this.uploadFiles, function (_file) {\n          return attrAccept(_file, _this.props.accept);\n        });\n      } else {\n        var files = _toConsumableArray(e.dataTransfer.files).filter(function (file) {\n          return attrAccept(file, _this.props.accept);\n        });\n        if (multiple === false) {\n          files = files.slice(0, 1);\n        }\n        _this.uploadFiles(files);\n      }\n    };\n    _this.uploadFiles = function (files) {\n      var originFiles = _toConsumableArray(files);\n      var postFiles = originFiles.map(function (file) {\n        // eslint-disable-next-line no-param-reassign\n        file.uid = getUid();\n        return _this.processFile(file, originFiles);\n      }); // Batch upload files\n\n      Promise.all(postFiles).then(function (fileList) {\n        var onBatchStart = _this.props.onBatchStart;\n        onBatchStart === null || onBatchStart === void 0 ? void 0 : onBatchStart(fileList.map(function (_ref) {\n          var origin = _ref.origin,\n            parsedFile = _ref.parsedFile;\n          return {\n            file: origin,\n            parsedFile: parsedFile\n          };\n        }));\n        fileList.filter(function (file) {\n          return file.parsedFile !== null;\n        }).forEach(function (file) {\n          _this.post(file);\n        });\n      });\n    };\n    _this.processFile = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(file, fileList) {\n        var beforeUpload, transformedFile, action, mergedAction, data, mergedData, parsedData, parsedFile, mergedParsedFile;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) {\n            switch (_context.prev = _context.next) {\n              case 0:\n                beforeUpload = _this.props.beforeUpload;\n                transformedFile = file;\n                if (!beforeUpload) {\n                  _context.next = 14;\n                  break;\n                }\n                _context.prev = 3;\n                _context.next = 6;\n                return beforeUpload(file, fileList);\n              case 6:\n                transformedFile = _context.sent;\n                _context.next = 12;\n                break;\n              case 9:\n                _context.prev = 9;\n                _context.t0 = _context[\"catch\"](3);\n                // Rejection will also trade as false\n                transformedFile = false;\n              case 12:\n                if (!(transformedFile === false)) {\n                  _context.next = 14;\n                  break;\n                }\n                return _context.abrupt(\"return\", {\n                  origin: file,\n                  parsedFile: null,\n                  action: null,\n                  data: null\n                });\n              case 14:\n                // Get latest action\n                action = _this.props.action;\n                if (!(typeof action === 'function')) {\n                  _context.next = 21;\n                  break;\n                }\n                _context.next = 18;\n                return action(file);\n              case 18:\n                mergedAction = _context.sent;\n                _context.next = 22;\n                break;\n              case 21:\n                mergedAction = action;\n              case 22:\n                // Get latest data\n                data = _this.props.data;\n                if (!(typeof data === 'function')) {\n                  _context.next = 29;\n                  break;\n                }\n                _context.next = 26;\n                return data(file);\n              case 26:\n                mergedData = _context.sent;\n                _context.next = 30;\n                break;\n              case 29:\n                mergedData = data;\n              case 30:\n                parsedData =\n                // string type is from legacy `transformFile`.\n                // Not sure if this will work since no related test case works with it\n                (_typeof(transformedFile) === 'object' || typeof transformedFile === 'string') && transformedFile ? transformedFile : file;\n                if (parsedData instanceof File) {\n                  parsedFile = parsedData;\n                } else {\n                  parsedFile = new File([parsedData], file.name, {\n                    type: file.type\n                  });\n                }\n                mergedParsedFile = parsedFile;\n                mergedParsedFile.uid = file.uid;\n                return _context.abrupt(\"return\", {\n                  origin: file,\n                  data: mergedData,\n                  parsedFile: mergedParsedFile,\n                  action: mergedAction\n                });\n              case 35:\n              case \"end\":\n                return _context.stop();\n            }\n          }\n        }, _callee, null, [[3, 9]]);\n      }));\n      return function (_x, _x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    _this.saveFileInput = function (node) {\n      _this.fileInput = node;\n    };\n    return _this;\n  }\n  _createClass(AjaxUploader, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._isMounted = true;\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      this.abort();\n    }\n  }, {\n    key: \"post\",\n    value: function post(_ref3) {\n      var _this2 = this;\n      var data = _ref3.data,\n        origin = _ref3.origin,\n        action = _ref3.action,\n        parsedFile = _ref3.parsedFile;\n      if (!this._isMounted) {\n        return;\n      }\n      var _this$props3 = this.props,\n        onStart = _this$props3.onStart,\n        customRequest = _this$props3.customRequest,\n        name = _this$props3.name,\n        headers = _this$props3.headers,\n        withCredentials = _this$props3.withCredentials,\n        method = _this$props3.method;\n      var uid = origin.uid;\n      var request = customRequest || defaultRequest;\n      var requestOption = {\n        action: action,\n        filename: name,\n        data: data,\n        file: parsedFile,\n        headers: headers,\n        withCredentials: withCredentials,\n        method: method || 'post',\n        onProgress: function onProgress(e) {\n          var onProgress = _this2.props.onProgress;\n          onProgress === null || onProgress === void 0 ? void 0 : onProgress(e, parsedFile);\n        },\n        onSuccess: function onSuccess(ret, xhr) {\n          var onSuccess = _this2.props.onSuccess;\n          onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(ret, parsedFile, xhr);\n          delete _this2.reqs[uid];\n        },\n        onError: function onError(err, ret) {\n          var onError = _this2.props.onError;\n          onError === null || onError === void 0 ? void 0 : onError(err, ret, parsedFile);\n          delete _this2.reqs[uid];\n        }\n      };\n      onStart(origin);\n      this.reqs[uid] = request(requestOption);\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.setState({\n        uid: getUid()\n      });\n    }\n  }, {\n    key: \"abort\",\n    value: function abort(file) {\n      var reqs = this.reqs;\n      if (file) {\n        var uid = file.uid ? file.uid : file;\n        if (reqs[uid] && reqs[uid].abort) {\n          reqs[uid].abort();\n        }\n        delete reqs[uid];\n      } else {\n        Object.keys(reqs).forEach(function (uid) {\n          if (reqs[uid] && reqs[uid].abort) {\n            reqs[uid].abort();\n          }\n          delete reqs[uid];\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _classNames;\n      var _this$props4 = this.props,\n        Tag = _this$props4.component,\n        prefixCls = _this$props4.prefixCls,\n        className = _this$props4.className,\n        disabled = _this$props4.disabled,\n        id = _this$props4.id,\n        style = _this$props4.style,\n        multiple = _this$props4.multiple,\n        accept = _this$props4.accept,\n        capture = _this$props4.capture,\n        children = _this$props4.children,\n        directory = _this$props4.directory,\n        openFileDialogOnClick = _this$props4.openFileDialogOnClick,\n        onMouseEnter = _this$props4.onMouseEnter,\n        onMouseLeave = _this$props4.onMouseLeave,\n        otherProps = _objectWithoutProperties(_this$props4, _excluded);\n      var cls = classNames((_classNames = {}, _defineProperty(_classNames, prefixCls, true), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames, className, className), _classNames)); // because input don't have directory/webkitdirectory type declaration\n\n      var dirProps = directory ? {\n        directory: 'directory',\n        webkitdirectory: 'webkitdirectory'\n      } : {};\n      var events = disabled ? {} : {\n        onClick: openFileDialogOnClick ? this.onClick : function () {},\n        onKeyDown: openFileDialogOnClick ? this.onKeyDown : function () {},\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onDrop: this.onFileDrop,\n        onDragOver: this.onFileDrop,\n        tabIndex: '0'\n      };\n      return /*#__PURE__*/React.createElement(Tag, _extends({}, events, {\n        className: cls,\n        role: \"button\",\n        style: style\n      }), /*#__PURE__*/React.createElement(\"input\", _extends({}, pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      }), {\n        id: id,\n        type: \"file\",\n        ref: this.saveFileInput,\n        onClick: function onClick(e) {\n          return e.stopPropagation();\n        } // https://github.com/ant-design/ant-design/issues/19948\n        ,\n\n        key: this.state.uid,\n        style: {\n          display: 'none'\n        },\n        accept: accept\n      }, dirProps, {\n        multiple: multiple,\n        onChange: this.onChange\n      }, capture != null ? {\n        capture: capture\n      } : {})), children);\n    }\n  }]);\n  return AjaxUploader;\n}(Component);\nexport default AjaxUploader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}