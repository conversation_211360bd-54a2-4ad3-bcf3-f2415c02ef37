{"ast": null, "code": "const initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = function (motionCls, inKeyframes, outKeyframes, duration) {\n  let sameLevel = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [`\n      ${sameLevelPrefix}${motionCls}-enter,\n      ${sameLevelPrefix}${motionCls}-appear\n    `]: Object.assign(Object.assign({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`${sameLevelPrefix}${motionCls}-leave`]: Object.assign(Object.assign({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`\n      ${sameLevelPrefix}${motionCls}-enter${motionCls}-enter-active,\n      ${sameLevelPrefix}${motionCls}-appear${motionCls}-appear-active\n    `]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave${motionCls}-leave-active`]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}