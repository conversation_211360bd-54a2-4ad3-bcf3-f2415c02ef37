{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [hsbValue, setHsbValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHsbValue(value);\n    }\n  }, [value]);\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    if (!value) {\n      setHsbValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}