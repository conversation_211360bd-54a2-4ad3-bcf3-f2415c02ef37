{"ast": null, "code": "import { setTime as utilSetTime } from \"../utils/timeUtil\";\nexport default function useTimeSelection(_ref) {\n  var value = _ref.value,\n    generateConfig = _ref.generateConfig,\n    disabledMinutes = _ref.disabledMinutes,\n    disabledSeconds = _ref.disabledSeconds,\n    minutes = _ref.minutes,\n    seconds = _ref.seconds,\n    use12Hours = _ref.use12Hours;\n  var setTime = function setTime(isNewPM, newHour, newMinute, newSecond) {\n    var newDate = value || generateConfig.getNow();\n    var mergedHour = Math.max(0, newHour);\n    var mergedMinute = Math.max(0, newMinute);\n    var mergedSecond = Math.max(0, newSecond);\n    var newDisabledMinutes = disabledMinutes && disabledMinutes(mergedHour);\n    if (newDisabledMinutes !== null && newDisabledMinutes !== void 0 && newDisabledMinutes.includes(mergedMinute)) {\n      // find the first available minute in minutes\n      var availableMinute = minutes.find(function (i) {\n        return !newDisabledMinutes.includes(i.value);\n      });\n      if (availableMinute) {\n        mergedMinute = availableMinute.value;\n      } else {\n        return null;\n      }\n    }\n    var newDisabledSeconds = disabledSeconds && disabledSeconds(mergedHour, mergedMinute);\n    if (newDisabledSeconds !== null && newDisabledSeconds !== void 0 && newDisabledSeconds.includes(mergedSecond)) {\n      // find the first available second in seconds\n      var availableSecond = seconds.find(function (i) {\n        return !newDisabledSeconds.includes(i.value);\n      });\n      if (availableSecond) {\n        mergedSecond = availableSecond.value;\n      } else {\n        return null;\n      }\n    }\n    newDate = utilSetTime(generateConfig, newDate, !use12Hours || !isNewPM ? mergedHour : mergedHour + 12, mergedMinute, mergedSecond);\n    return newDate;\n  };\n  return setTime;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}