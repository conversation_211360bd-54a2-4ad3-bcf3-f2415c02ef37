{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar ImmutableContext = /*#__PURE__*/React.createContext(0);\n\n/**\n * Get render update mark by `makeImmutable` root.\n * Do not deps on the return value as render times\n * but only use for `useMemo` or `useCallback` deps.\n */\nexport function useImmutableMark() {\n  return React.useContext(ImmutableContext);\n}\n/**\n * Wrapped Component will be marked as Immutable.\n * When Component parent trigger render,\n * it will notice children component (use with `responseImmutable`) node that parent has updated.\n\n * @param Component Passed Component\n * @param triggerRender Customize trigger `responseImmutable` children re-render logic. Default will always trigger re-render when this component re-render.\n */\n\nexport function makeImmutable(Component, shouldTriggerRender) {\n  var refAble = supportRef(Component);\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    var renderTimesRef = React.useRef(0);\n    var prevProps = React.useRef(props);\n    if (\n    // Always trigger re-render if not provide `notTriggerRender`\n    !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n      renderTimesRef.current += 1;\n    }\n    prevProps.current = props;\n    return /*#__PURE__*/React.createElement(ImmutableContext.Provider, {\n      value: renderTimesRef.current\n    }, /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps)));\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n  }\n  return refAble ? /*#__PURE__*/React.forwardRef(ImmutableComponent) : ImmutableComponent;\n}\n/**\n * Wrapped Component with `React.memo`.\n * But will rerender when parent with `makeImmutable` rerender.\n */\n\nexport function responseImmutable(Component, propsAreEqual) {\n  var refAble = supportRef(Component);\n  var ImmutableComponent = function ImmutableComponent(props, ref) {\n    var refProps = refAble ? {\n      ref: ref\n    } : {};\n    useImmutableMark();\n    return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n  }\n  return refAble ? /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/React.memo(ImmutableComponent, propsAreEqual);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}