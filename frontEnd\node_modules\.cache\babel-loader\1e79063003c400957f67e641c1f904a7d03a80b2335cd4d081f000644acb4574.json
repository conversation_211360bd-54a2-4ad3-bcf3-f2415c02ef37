{"ast": null, "code": "/* eslint-disable no-param-reassign */\n\nvar cached;\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    var inner = document.createElement('div');\n    inner.style.width = '100%';\n    inner.style.height = '200px';\n    var outer = document.createElement('div');\n    var outerStyle = outer.style;\n    outerStyle.position = 'absolute';\n    outerStyle.top = '0';\n    outerStyle.left = '0';\n    outerStyle.pointerEvents = 'none';\n    outerStyle.visibility = 'hidden';\n    outerStyle.width = '200px';\n    outerStyle.height = '150px';\n    outerStyle.overflow = 'hidden';\n    outer.appendChild(inner);\n    document.body.appendChild(outer);\n    var widthContained = inner.offsetWidth;\n    outer.style.overflow = 'scroll';\n    var widthScroll = inner.offsetWidth;\n    if (widthContained === widthScroll) {\n      widthScroll = outer.clientWidth;\n    }\n    document.body.removeChild(outer);\n    cached = widthContained - widthScroll;\n  }\n  return cached;\n}\nfunction ensureSize(str) {\n  var match = str.match(/^(.*)px$/);\n  var value = Number(match === null || match === void 0 ? void 0 : match[1]);\n  return Number.isNaN(value) ? getScrollBarSize() : value;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  var _getComputedStyle = getComputedStyle(target, '::-webkit-scrollbar'),\n    width = _getComputedStyle.width,\n    height = _getComputedStyle.height;\n  return {\n    width: ensureSize(width),\n    height: ensureSize(height)\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}