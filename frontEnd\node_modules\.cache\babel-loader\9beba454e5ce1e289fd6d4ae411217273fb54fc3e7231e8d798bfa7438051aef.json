{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useEffect, useRef } from 'react';\nimport useValueTexts from \"./useValueTexts\";\nexport default function useHoverValue(valueText, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    value = _useState2[0],\n    internalSetValue = _useState2[1];\n  var raf = useRef(null);\n  function setValue(val) {\n    var immediately = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    cancelAnimationFrame(raf.current);\n    if (immediately) {\n      internalSetValue(val);\n      return;\n    }\n    raf.current = requestAnimationFrame(function () {\n      internalSetValue(val);\n    });\n  }\n  var _useValueTexts = useValueTexts(value, {\n      formatList: formatList,\n      generateConfig: generateConfig,\n      locale: locale\n    }),\n    _useValueTexts2 = _slicedToArray(_useValueTexts, 2),\n    firstText = _useValueTexts2[1];\n  function onEnter(date) {\n    setValue(date);\n  }\n  function onLeave() {\n    var immediately = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    setValue(null, immediately);\n  }\n  useEffect(function () {\n    onLeave(true);\n  }, [valueText]);\n  useEffect(function () {\n    return function () {\n      return cancelAnimationFrame(raf.current);\n    };\n  }, []);\n  return [firstText, onEnter, onLeave];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}