{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorPickerPrefixCls, defaultColor, generateColor } from \"./util\";\nimport classNames from 'classnames';\nimport ColorBlock from \"./components/ColorBlock\";\nimport Picker from \"./components/Picker\";\nimport Slider from \"./components/Slider\";\nimport useColorState from \"./hooks/useColorState\";\nvar hueColor = ['rgb(255, 0, 0) 0%', 'rgb(255, 255, 0) 17%', 'rgb(0, 255, 0) 33%', 'rgb(0, 255, 255) 50%', 'rgb(0, 0, 255) 67%', 'rgb(255, 0, 255) 83%', 'rgb(255, 0, 0) 100%'];\nexport default /*#__PURE__*/forwardRef(function (props, ref) {\n  var value = props.value,\n    defaultValue = props.defaultValue,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? ColorPickerPrefixCls : _props$prefixCls,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    className = props.className,\n    style = props.style,\n    panelRender = props.panelRender,\n    _props$disabledAlpha = props.disabledAlpha,\n    disabledAlpha = _props$disabledAlpha === void 0 ? false : _props$disabledAlpha,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled;\n  var _useColorState = useColorState(defaultColor, {\n      value: value,\n      defaultValue: defaultValue\n    }),\n    _useColorState2 = _slicedToArray(_useColorState, 2),\n    colorValue = _useColorState2[0],\n    setColorValue = _useColorState2[1];\n  var alphaColor = useMemo(function () {\n    var rgb = generateColor(colorValue.toRgbString());\n    // alpha color need equal 1 for base color\n    rgb.setAlpha(1);\n    return rgb.toRgbString();\n  }, [colorValue]);\n  var mergeCls = classNames(\"\".concat(prefixCls, \"-panel\"), className, _defineProperty({}, \"\".concat(prefixCls, \"-panel-disabled\"), disabled));\n  var basicProps = {\n    prefixCls: prefixCls,\n    onChangeComplete: onChangeComplete,\n    disabled: disabled\n  };\n  var handleChange = function handleChange(data, type) {\n    if (!value) {\n      setColorValue(data);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(data, type);\n  };\n  var defaultPanel = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Picker, _extends({\n    color: colorValue,\n    onChange: handleChange\n  }, basicProps)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-slider-container\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-slider-group\"), _defineProperty({}, \"\".concat(prefixCls, \"-slider-group-disabled-alpha\"), disabledAlpha))\n  }, /*#__PURE__*/React.createElement(Slider, _extends({\n    gradientColors: hueColor,\n    color: colorValue,\n    value: \"hsl(\".concat(colorValue.toHsb().h, \",100%, 50%)\"),\n    onChange: function onChange(color) {\n      return handleChange(color, 'hue');\n    }\n  }, basicProps)), !disabledAlpha && /*#__PURE__*/React.createElement(Slider, _extends({\n    type: \"alpha\",\n    gradientColors: ['rgba(255, 0, 4, 0) 0%', alphaColor],\n    color: colorValue,\n    value: colorValue.toRgbString(),\n    onChange: function onChange(color) {\n      return handleChange(color, 'alpha');\n    }\n  }, basicProps))), /*#__PURE__*/React.createElement(ColorBlock, {\n    color: colorValue.toRgbString(),\n    prefixCls: prefixCls\n  })));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: mergeCls,\n    style: style,\n    ref: ref\n  }, typeof panelRender === 'function' ? panelRender(defaultPanel) : defaultPanel);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}