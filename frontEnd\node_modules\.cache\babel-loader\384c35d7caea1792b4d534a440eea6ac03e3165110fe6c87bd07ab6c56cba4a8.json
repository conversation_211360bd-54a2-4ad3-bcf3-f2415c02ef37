{"ast": null, "code": "import _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport KeyCode from 'rc-util/es/KeyCode';\nimport React from 'react';\nimport PanelContent from './PanelContent';\nvar _excluded = ['showArrow', 'headerClass', 'isActive', 'onItemClick', 'forceRender', 'className', 'prefixCls', 'collapsible', 'accordion', 'panelKey', 'extra', 'header', 'expandIcon', 'openMotion', 'destroyInactivePanel', 'children'];\nvar CollapsePanel = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _classNames2;\n  var _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    headerClass = props.headerClass,\n    isActive = props.isActive,\n    onItemClick = props.onItemClick,\n    forceRender = props.forceRender,\n    className = props.className,\n    prefixCls = props.prefixCls,\n    collapsible = props.collapsible,\n    accordion = props.accordion,\n    panelKey = props.panelKey,\n    extra = props.extra,\n    header = props.header,\n    expandIcon = props.expandIcon,\n    openMotion = props.openMotion,\n    destroyInactivePanel = props.destroyInactivePanel,\n    children = props.children,\n    resetProps = _objectWithoutProperties(props, _excluded);\n  var disabled = collapsible === 'disabled';\n  var collapsibleHeader = collapsible === 'header';\n  var collapsibleIcon = collapsible === 'icon';\n  var ifExtraExist = extra !== null && extra !== undefined && typeof extra !== 'boolean';\n  var handleItemClick = function handleItemClick() {\n    onItemClick === null || onItemClick === void 0 ? void 0 : onItemClick(panelKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    if (e.key === 'Enter' || e.keyCode === KeyCode.ENTER || e.which === KeyCode.ENTER) {\n      handleItemClick();\n    }\n  };\n\n  // ======================== Icon ========================\n  var iconNode = typeof expandIcon === 'function' ? expandIcon(props) : /*#__PURE__*/React.createElement('i', {\n    className: 'arrow'\n  });\n  if (iconNode) {\n    iconNode = /*#__PURE__*/React.createElement('div', {\n      className: ''.concat(prefixCls, '-expand-icon'),\n      onClick: ['header', 'icon'].includes(collapsible) ? handleItemClick : undefined\n    }, iconNode);\n  }\n  var collapsePanelClassNames = classNames((_classNames = {}, _defineProperty(_classNames, ''.concat(prefixCls, '-item'), true), _defineProperty(_classNames, ''.concat(prefixCls, '-item-active'), isActive), _defineProperty(_classNames, ''.concat(prefixCls, '-item-disabled'), disabled), _classNames), className);\n  var headerClassName = classNames(headerClass, (_classNames2 = {}, _defineProperty(_classNames2, ''.concat(prefixCls, '-header'), true), _defineProperty(_classNames2, ''.concat(prefixCls, '-header-collapsible-only'), collapsibleHeader), _defineProperty(_classNames2, ''.concat(prefixCls, '-icon-collapsible-only'), collapsibleIcon), _classNames2));\n\n  // ======================== HeaderProps ========================\n  var headerProps = {\n    className: headerClassName,\n    'aria-expanded': isActive,\n    'aria-disabled': disabled,\n    onKeyDown: handleKeyDown\n  };\n  if (!collapsibleHeader && !collapsibleIcon) {\n    headerProps.onClick = handleItemClick;\n    headerProps.role = accordion ? 'tab' : 'button';\n    headerProps.tabIndex = disabled ? -1 : 0;\n  }\n\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement('div', _extends({}, resetProps, {\n    ref: ref,\n    className: collapsePanelClassNames\n  }), /*#__PURE__*/React.createElement('div', headerProps, showArrow && iconNode, /*#__PURE__*/React.createElement('span', {\n    className: ''.concat(prefixCls, '-header-text'),\n    onClick: collapsible === 'header' ? handleItemClick : undefined\n  }, header), ifExtraExist && /*#__PURE__*/React.createElement('div', {\n    className: ''.concat(prefixCls, '-extra')\n  }, extra)), /*#__PURE__*/React.createElement(CSSMotion, _extends({\n    visible: isActive,\n    leavedClassName: ''.concat(prefixCls, '-content-hidden')\n  }, openMotion, {\n    forceRender: forceRender,\n    removeOnLeave: destroyInactivePanel\n  }), function (_ref, motionRef) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(PanelContent, {\n      ref: motionRef,\n      prefixCls: prefixCls,\n      className: motionClassName,\n      style: motionStyle,\n      isActive: isActive,\n      forceRender: forceRender,\n      role: accordion ? 'tabpanel' : void 0\n    }, children);\n  }));\n});\nexport default CollapsePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}