{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"onVisibleChange\", \"getContainer\", \"current\", \"movable\", \"minScale\", \"maxScale\", \"countRender\", \"closeIcon\", \"onChange\", \"onTransform\", \"toolbarRender\", \"imageRender\"],\n  _excluded2 = [\"src\"];\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport usePreviewItems from \"./hooks/usePreviewItems\";\nimport Preview from \"./Preview\";\nvar Group = function Group(_ref) {\n  var _mergedItems$current;\n  var _ref$previewPrefixCls = _ref.previewPrefixCls,\n    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,\n    children = _ref.children,\n    _ref$icons = _ref.icons,\n    icons = _ref$icons === void 0 ? {} : _ref$icons,\n    items = _ref.items,\n    preview = _ref.preview,\n    fallback = _ref.fallback;\n  var _ref2 = _typeof(preview) === 'object' ? preview : {},\n    previewVisible = _ref2.visible,\n    onVisibleChange = _ref2.onVisibleChange,\n    getContainer = _ref2.getContainer,\n    currentIndex = _ref2.current,\n    movable = _ref2.movable,\n    minScale = _ref2.minScale,\n    maxScale = _ref2.maxScale,\n    countRender = _ref2.countRender,\n    closeIcon = _ref2.closeIcon,\n    onChange = _ref2.onChange,\n    onTransform = _ref2.onTransform,\n    toolbarRender = _ref2.toolbarRender,\n    imageRender = _ref2.imageRender,\n    dialogProps = _objectWithoutProperties(_ref2, _excluded);\n\n  // ========================== Items ===========================\n  var _usePreviewItems = usePreviewItems(items),\n    _usePreviewItems2 = _slicedToArray(_usePreviewItems, 2),\n    mergedItems = _usePreviewItems2[0],\n    register = _usePreviewItems2[1];\n\n  // ========================= Preview ==========================\n  // >>> Index\n  var _useMergedState = useMergedState(0, {\n      value: currentIndex\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    current = _useMergedState2[0],\n    setCurrent = _useMergedState2[1];\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    keepOpenIndex = _useState2[0],\n    setKeepOpenIndex = _useState2[1];\n\n  // >>> Image\n  var _ref3 = ((_mergedItems$current = mergedItems[current]) === null || _mergedItems$current === void 0 ? void 0 : _mergedItems$current.data) || {},\n    src = _ref3.src,\n    imgCommonProps = _objectWithoutProperties(_ref3, _excluded2);\n  // >>> Visible\n  var _useMergedState3 = useMergedState(!!previewVisible, {\n      value: previewVisible,\n      onChange: function onChange(val, prevVal) {\n        onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(val, prevVal, current);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    isShowPreview = _useMergedState4[0],\n    setShowPreview = _useMergedState4[1];\n\n  // >>> Position\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    mousePosition = _useState4[0],\n    setMousePosition = _useState4[1];\n  var onPreviewFromImage = React.useCallback(function (id, mouseX, mouseY) {\n    var index = mergedItems.findIndex(function (item) {\n      return item.id === id;\n    });\n    setShowPreview(true);\n    setMousePosition({\n      x: mouseX,\n      y: mouseY\n    });\n    setCurrent(index < 0 ? 0 : index);\n    setKeepOpenIndex(true);\n  }, [mergedItems]);\n\n  // Reset current when reopen\n  React.useEffect(function () {\n    if (isShowPreview) {\n      if (!keepOpenIndex) {\n        setCurrent(0);\n      }\n    } else {\n      setKeepOpenIndex(false);\n    }\n  }, [isShowPreview]);\n\n  // ========================== Events ==========================\n  var onInternalChange = function onInternalChange(next, prev) {\n    setCurrent(next);\n    onChange === null || onChange === void 0 ? void 0 : onChange(next, prev);\n  };\n  var onPreviewClose = function onPreviewClose() {\n    setShowPreview(false);\n    setMousePosition(null);\n  };\n\n  // ========================= Context ==========================\n  var previewGroupContext = React.useMemo(function () {\n    return {\n      register: register,\n      onPreview: onPreviewFromImage\n    };\n  }, [register, onPreviewFromImage]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(PreviewGroupContext.Provider, {\n    value: previewGroupContext\n  }, children, /*#__PURE__*/React.createElement(Preview, _extends({\n    \"aria-hidden\": !isShowPreview,\n    movable: movable,\n    visible: isShowPreview,\n    prefixCls: previewPrefixCls,\n    closeIcon: closeIcon,\n    onClose: onPreviewClose,\n    mousePosition: mousePosition,\n    imgCommonProps: imgCommonProps,\n    src: src,\n    fallback: fallback,\n    icons: icons,\n    minScale: minScale,\n    maxScale: maxScale,\n    getContainer: getContainer,\n    current: current,\n    count: mergedItems.length,\n    countRender: countRender,\n    onTransform: onTransform,\n    toolbarRender: toolbarRender,\n    imageRender: imageRender,\n    onChange: onInternalChange\n  }, dialogProps)));\n};\nexport default Group;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}