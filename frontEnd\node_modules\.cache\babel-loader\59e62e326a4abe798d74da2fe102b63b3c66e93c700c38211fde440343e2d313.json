{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    containerRef = props.containerRef,\n    onMouseEnter = props.onMouseEnter,\n    onMouseOver = props.onMouseOver,\n    onMouseLeave = props.onMouseLeave,\n    onClick = props.onClick,\n    onKeyDown = props.onKeyDown,\n    onKeyUp = props.onKeyUp;\n  var eventHandlers = {\n    onMouseEnter: onMouseEnter,\n    onMouseOver: onMouseOver,\n    onMouseLeave: onMouseLeave,\n    onClick: onClick,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  };\n  // =============================== Render ===============================\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: _objectSpread({}, style),\n    \"aria-modal\": \"true\",\n    role: \"dialog\",\n    ref: containerRef\n  }, eventHandlers), children));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}