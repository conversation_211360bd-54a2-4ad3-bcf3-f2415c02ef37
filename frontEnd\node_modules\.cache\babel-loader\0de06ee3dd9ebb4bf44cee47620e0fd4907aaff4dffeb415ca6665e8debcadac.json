{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initFadeMotion } from '../../style/motion';\nconst uploadAnimateInlineIn = new Keyframes('uploadAnimateInlineIn', {\n  from: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\nconst uploadAnimateInlineOut = new Keyframes('uploadAnimateInlineOut', {\n  to: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\n// =========================== Motion ===========================\nconst genMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const inlineCls = `${componentCls}-animate-inline`;\n  return [{\n    [`${componentCls}-wrapper`]: {\n      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseInOutCirc,\n        animationFillMode: 'forwards'\n      },\n      [`${inlineCls}-appear, ${inlineCls}-enter`]: {\n        animationName: uploadAnimateInlineIn\n      },\n      [`${inlineCls}-leave`]: {\n        animationName: uploadAnimateInlineOut\n      }\n    }\n  }, {\n    [`${componentCls}-wrapper`]: initFadeMotion(token)\n  }, uploadAnimateInlineIn, uploadAnimateInlineOut];\n};\nexport default genMotionStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}