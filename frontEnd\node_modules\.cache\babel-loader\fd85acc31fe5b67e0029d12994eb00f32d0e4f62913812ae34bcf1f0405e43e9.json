{"ast": null, "code": "import * as React from 'react';\nexport default function useMergedConfig(propConfig, templateConfig) {\n  return React.useMemo(() => {\n    const support = !!propConfig;\n    return [support, Object.assign(Object.assign({}, templateConfig), support && typeof propConfig === 'object' ? propConfig : null)];\n  }, [propConfig]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}