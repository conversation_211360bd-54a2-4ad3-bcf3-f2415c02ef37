{"ast": null, "code": "import classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getTargetWaveColor } from './util';\nimport { TARGET_CLS } from './interface';\nfunction validateNum(value) {\n  return Number.isNaN(value) ? 0 : value;\n}\nconst WaveEffect = props => {\n  const {\n    className,\n    target,\n    component\n  } = props;\n  const divRef = React.useRef(null);\n  const [color, setWaveColor] = React.useState(null);\n  const [borderRadius, setBorderRadius] = React.useState([]);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const [enabled, setEnabled] = React.useState(false);\n  const waveStyle = {\n    left,\n    top,\n    width,\n    height,\n    borderRadius: borderRadius.map(radius => `${radius}px`).join(' ')\n  };\n  if (color) {\n    waveStyle['--wave-color'] = color;\n  }\n  function syncPos() {\n    const nodeStyle = getComputedStyle(target);\n    // Get wave color from target\n    setWaveColor(getTargetWaveColor(target));\n    const isStatic = nodeStyle.position === 'static';\n    // Rect\n    const {\n      borderLeftWidth,\n      borderTopWidth\n    } = nodeStyle;\n    setLeft(isStatic ? target.offsetLeft : validateNum(-parseFloat(borderLeftWidth)));\n    setTop(isStatic ? target.offsetTop : validateNum(-parseFloat(borderTopWidth)));\n    setWidth(target.offsetWidth);\n    setHeight(target.offsetHeight);\n    // Get border radius\n    const {\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius\n    } = nodeStyle;\n    setBorderRadius([borderTopLeftRadius, borderTopRightRadius, borderBottomRightRadius, borderBottomLeftRadius].map(radius => validateNum(parseFloat(radius))));\n  }\n  React.useEffect(() => {\n    if (target) {\n      // We need delay to check position here\n      // since UI may change after click\n      const id = raf(() => {\n        syncPos();\n        setEnabled(true);\n      });\n      // Add resize observer to follow size\n      let resizeObserver;\n      if (typeof ResizeObserver !== 'undefined') {\n        resizeObserver = new ResizeObserver(syncPos);\n        resizeObserver.observe(target);\n      }\n      return () => {\n        raf.cancel(id);\n        resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.disconnect();\n      };\n    }\n  }, []);\n  if (!enabled) {\n    return null;\n  }\n  const isSmallComponent = (component === 'Checkbox' || component === 'Radio') && (target === null || target === void 0 ? void 0 : target.classList.contains(TARGET_CLS));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionAppear: true,\n    motionName: \"wave-motion\",\n    motionDeadline: 5000,\n    onAppearEnd: (_, event) => {\n      var _a;\n      if (event.deadline || event.propertyName === 'opacity') {\n        const holder = (_a = divRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;\n        unmount(holder).then(() => {\n          holder === null || holder === void 0 ? void 0 : holder.remove();\n        });\n      }\n      return false;\n    }\n  }, _ref => {\n    let {\n      className: motionClassName\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: divRef,\n      className: classNames(className, {\n        'wave-quick': isSmallComponent\n      }, motionClassName),\n      style: waveStyle\n    });\n  });\n};\nconst showWaveEffect = (target, info) => {\n  var _a;\n  const {\n    component\n  } = info;\n  // Skip for unchecked checkbox\n  if (component === 'Checkbox' && !((_a = target.querySelector('input')) === null || _a === void 0 ? void 0 : _a.checked)) {\n    return;\n  }\n  // Create holder\n  const holder = document.createElement('div');\n  holder.style.position = 'absolute';\n  holder.style.left = '0px';\n  holder.style.top = '0px';\n  target === null || target === void 0 ? void 0 : target.insertBefore(holder, target === null || target === void 0 ? void 0 : target.firstChild);\n  render( /*#__PURE__*/React.createElement(WaveEffect, Object.assign({}, info, {\n    target: target\n  })), holder);\n};\nexport default showWaveEffect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}