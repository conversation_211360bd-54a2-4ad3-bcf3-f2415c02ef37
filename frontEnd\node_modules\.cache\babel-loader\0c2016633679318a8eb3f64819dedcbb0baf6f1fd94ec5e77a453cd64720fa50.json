{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport warning from \"rc-util/es/warning\";\nimport { isNil } from \"../utils/valueUtil\";\nexport default (function (treeData, fieldNames) {\n  return React.useMemo(function () {\n    var collection = convertDataToEntities(treeData, {\n      fieldNames: fieldNames,\n      initWrapper: function initWrapper(wrapper) {\n        return _objectSpread(_objectSpread({}, wrapper), {}, {\n          valueEntities: new Map()\n        });\n      },\n      processEntity: function processEntity(entity, wrapper) {\n        var val = entity.node[fieldNames.value];\n\n        // Check if exist same value\n        if (process.env.NODE_ENV !== 'production') {\n          var key = entity.node.key;\n          warning(!isNil(val), 'TreeNode `value` is invalidate: undefined');\n          warning(!wrapper.valueEntities.has(val), \"Same `value` exist in the tree: \".concat(val));\n          warning(!key || String(key) === String(val), \"`key` or `value` with TreeNode must be the same or you can remove one of them. key: \".concat(key, \", value: \").concat(val, \".\"));\n        }\n        wrapper.valueEntities.set(val, entity);\n      }\n    });\n    return collection;\n  }, [treeData, fieldNames]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}