{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"value\", \"valueIndex\", \"onStartMove\", \"style\", \"render\", \"dragging\", \"onOffsetChange\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport SliderContext from '../context';\nimport { getDirectionStyle, getIndex } from '../util';\nvar Handle = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _classNames, _getIndex;\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    valueIndex = props.valueIndex,\n    onStartMove = props.onStartMove,\n    style = props.style,\n    render = props.render,\n    dragging = props.dragging,\n    onOffsetChange = props.onOffsetChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    direction = _React$useContext.direction,\n    disabled = _React$useContext.disabled,\n    keyboard = _React$useContext.keyboard,\n    range = _React$useContext.range,\n    tabIndex = _React$useContext.tabIndex,\n    ariaLabelForHandle = _React$useContext.ariaLabelForHandle,\n    ariaLabelledByForHandle = _React$useContext.ariaLabelledByForHandle,\n    ariaValueTextFormatterForHandle = _React$useContext.ariaValueTextFormatterForHandle;\n  var handlePrefixCls = \"\".concat(prefixCls, \"-handle\");\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled) {\n      onStartMove(e, valueIndex);\n    }\n  };\n  // =========================== Keyboard ===========================\n  var onKeyDown = function onKeyDown(e) {\n    if (!disabled && keyboard) {\n      var offset = null;\n      // Change the value\n      switch (e.which || e.keyCode) {\n        case KeyCode.LEFT:\n          offset = direction === 'ltr' || direction === 'btt' ? -1 : 1;\n          break;\n        case KeyCode.RIGHT:\n          offset = direction === 'ltr' || direction === 'btt' ? 1 : -1;\n          break;\n        // Up is plus\n        case KeyCode.UP:\n          offset = direction !== 'ttb' ? 1 : -1;\n          break;\n        // Down is minus\n        case KeyCode.DOWN:\n          offset = direction !== 'ttb' ? -1 : 1;\n          break;\n        case KeyCode.HOME:\n          offset = 'min';\n          break;\n        case KeyCode.END:\n          offset = 'max';\n          break;\n        case KeyCode.PAGE_UP:\n          offset = 2;\n          break;\n        case KeyCode.PAGE_DOWN:\n          offset = -2;\n          break;\n      }\n      if (offset !== null) {\n        e.preventDefault();\n        onOffsetChange(offset, valueIndex);\n      }\n    }\n  };\n  // ============================ Offset ============================\n  var positionStyle = getDirectionStyle(direction, value, min, max);\n  // ============================ Render ============================\n  var handleNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    className: classNames(handlePrefixCls, (_classNames = {}, _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-\").concat(valueIndex + 1), range), _defineProperty(_classNames, \"\".concat(handlePrefixCls, \"-dragging\"), dragging), _classNames)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove,\n    onKeyDown: onKeyDown,\n    tabIndex: disabled ? null : getIndex(tabIndex, valueIndex),\n    role: \"slider\",\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-valuenow\": value,\n    \"aria-disabled\": disabled,\n    \"aria-label\": getIndex(ariaLabelForHandle, valueIndex),\n    \"aria-labelledby\": getIndex(ariaLabelledByForHandle, valueIndex),\n    \"aria-valuetext\": (_getIndex = getIndex(ariaValueTextFormatterForHandle, valueIndex)) === null || _getIndex === void 0 ? void 0 : _getIndex(value)\n  }, restProps));\n  // Customize\n  if (render) {\n    handleNode = render(handleNode, {\n      index: valueIndex,\n      prefixCls: prefixCls,\n      value: value,\n      dragging: dragging\n    });\n  }\n  return handleNode;\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handle.displayName = 'Handle';\n}\nexport default Handle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}