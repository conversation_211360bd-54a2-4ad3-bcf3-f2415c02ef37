{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState } from 'react';\nimport isMobile from \"../isMobile\";\nimport useLayoutEffect from \"./useLayoutEffect\";\n\n/**\n * Hook to detect if the user is on a mobile device\n * Notice that this hook will only detect the device type in effect, so it will always be false in server side\n */\nvar useMobile = function useMobile() {\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useLayoutEffect(function () {\n    setMobile(isMobile());\n  }, []);\n  return mobile;\n};\nexport default useMobile;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}