{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fallback\", \"src\", \"imgRef\"],\n  _excluded2 = [\"prefixCls\", \"src\", \"alt\", \"fallback\", \"movable\", \"onClose\", \"visible\", \"icons\", \"rootClassName\", \"closeIcon\", \"getContainer\", \"current\", \"count\", \"countRender\", \"scaleStep\", \"minScale\", \"maxScale\", \"transitionName\", \"maskTransitionName\", \"imageRender\", \"imgCommonProps\", \"toolbarRender\", \"onTransform\", \"onChange\"];\nimport classnames from 'classnames';\nimport Dialog from 'rc-dialog';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { warning } from \"rc-util/es/warning\";\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport getFixScaleEleTransPosition from \"./getFixScaleEleTransPosition\";\nimport useImageTransform from \"./hooks/useImageTransform\";\nimport useStatus from \"./hooks/useStatus\";\nimport Operations from \"./Operations\";\nimport { BASE_SCALE_RATIO, WHEEL_MAX_SCALE_RATIO } from \"./previewConfig\";\nvar PreviewImage = function PreviewImage(_ref) {\n  var fallback = _ref.fallback,\n    src = _ref.src,\n    imgRef = _ref.imgRef,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useStatus = useStatus({\n      src: src,\n      fallback: fallback\n    }),\n    _useStatus2 = _slicedToArray(_useStatus, 2),\n    getImgRef = _useStatus2[0],\n    srcAndOnload = _useStatus2[1];\n  return /*#__PURE__*/React.createElement(\"img\", _extends({\n    ref: function ref(_ref2) {\n      imgRef.current = _ref2;\n      getImgRef(_ref2);\n    }\n  }, props, srcAndOnload));\n};\nvar Preview = function Preview(props) {\n  var prefixCls = props.prefixCls,\n    src = props.src,\n    alt = props.alt,\n    fallback = props.fallback,\n    _props$movable = props.movable,\n    movable = _props$movable === void 0 ? true : _props$movable,\n    onClose = props.onClose,\n    visible = props.visible,\n    _props$icons = props.icons,\n    icons = _props$icons === void 0 ? {} : _props$icons,\n    rootClassName = props.rootClassName,\n    closeIcon = props.closeIcon,\n    getContainer = props.getContainer,\n    _props$current = props.current,\n    current = _props$current === void 0 ? 0 : _props$current,\n    _props$count = props.count,\n    count = _props$count === void 0 ? 1 : _props$count,\n    countRender = props.countRender,\n    _props$scaleStep = props.scaleStep,\n    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n    _props$minScale = props.minScale,\n    minScale = _props$minScale === void 0 ? 1 : _props$minScale,\n    _props$maxScale = props.maxScale,\n    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,\n    _props$transitionName = props.transitionName,\n    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,\n    _props$maskTransition = props.maskTransitionName,\n    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,\n    imageRender = props.imageRender,\n    imgCommonProps = props.imgCommonProps,\n    toolbarRender = props.toolbarRender,\n    onTransform = props.onTransform,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded2);\n  var imgRef = useRef();\n  var downPositionRef = useRef({\n    deltaX: 0,\n    deltaY: 0,\n    transformX: 0,\n    transformY: 0\n  });\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isMoving = _useState2[0],\n    setMoving = _useState2[1];\n  var groupContext = useContext(PreviewGroupContext);\n  var showLeftOrRightSwitches = groupContext && count > 1;\n  var showOperationsProgress = groupContext && count >= 1;\n  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),\n    transform = _useImageTransform.transform,\n    resetTransform = _useImageTransform.resetTransform,\n    updateTransform = _useImageTransform.updateTransform,\n    dispatchZoomChange = _useImageTransform.dispatchZoomChange;\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    enableTransition = _useState4[0],\n    setEnableTransition = _useState4[1];\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  useEffect(function () {\n    if (!enableTransition) {\n      setEnableTransition(true);\n    }\n  }, [enableTransition]);\n  var onAfterClose = function onAfterClose() {\n    resetTransform('close');\n  };\n  var onZoomIn = function onZoomIn() {\n    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');\n  };\n  var onZoomOut = function onZoomOut() {\n    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');\n  };\n  var onRotateRight = function onRotateRight() {\n    updateTransform({\n      rotate: rotate + 90\n    }, 'rotateRight');\n  };\n  var onRotateLeft = function onRotateLeft() {\n    updateTransform({\n      rotate: rotate - 90\n    }, 'rotateLeft');\n  };\n  var onFlipX = function onFlipX() {\n    updateTransform({\n      flipX: !transform.flipX\n    }, 'flipX');\n  };\n  var onFlipY = function onFlipY() {\n    updateTransform({\n      flipY: !transform.flipY\n    }, 'flipY');\n  };\n  var onSwitchLeft = function onSwitchLeft(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current > 0) {\n      setEnableTransition(false);\n      resetTransform('prev');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current - 1, current);\n    }\n  };\n  var onSwitchRight = function onSwitchRight(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current < count - 1) {\n      setEnableTransition(false);\n      resetTransform('next');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current + 1, current);\n    }\n  };\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      setMoving(false);\n      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */\n      var _downPositionRef$curr = downPositionRef.current,\n        transformX = _downPositionRef$curr.transformX,\n        transformY = _downPositionRef$curr.transformY;\n      var hasChangedPosition = x !== transformX && y !== transformY;\n      if (!hasChangedPosition) {\n        return;\n      }\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n      if (fixState) {\n        updateTransform(_objectSpread({}, fixState), 'dragRebound');\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (!movable || event.button !== 0) return;\n    event.preventDefault();\n    event.stopPropagation();\n    downPositionRef.current = {\n      deltaX: event.pageX - transform.x,\n      deltaY: event.pageY - transform.y,\n      transformX: transform.x,\n      transformY: transform.y\n    };\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      updateTransform({\n        x: event.pageX - downPositionRef.current.deltaX,\n        y: event.pageY - downPositionRef.current.deltaY\n      }, 'move');\n    }\n  };\n  var onWheel = function onWheel(event) {\n    if (!visible || event.deltaY == 0) return;\n    // Scale ratio depends on the deltaY size\n    var scaleRatio = Math.abs(event.deltaY / 100);\n    // Limit the maximum scale ratio\n    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);\n    // Scale the ratio each time\n    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;\n    if (event.deltaY > 0) {\n      ratio = BASE_SCALE_RATIO / ratio;\n    }\n    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    if (event.keyCode === KeyCode.LEFT) {\n      onSwitchLeft();\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      onSwitchRight();\n    }\n  };\n  var onDoubleClick = function onDoubleClick(event) {\n    if (visible) {\n      if (scale !== 1) {\n        updateTransform({\n          x: 0,\n          y: 0,\n          scale: 1\n        }, 'doubleClick');\n      } else {\n        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);\n      }\n    }\n  };\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener;\n    var onMouseMoveListener;\n    if (movable) {\n      onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n      onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n      try {\n        // Resolve if in iframe lost event\n        /* istanbul ignore next */\n        if (window.top !== window.self) {\n          onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n          onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n        }\n      } catch (error) {\n        /* istanbul ignore next */\n        warning(false, \"[rc-image] \".concat(error));\n      }\n    }\n    return function () {\n      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;\n      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 ? void 0 : _onMouseUpListener.remove();\n      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 ? void 0 : _onMouseMoveListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 ? void 0 : _onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 ? void 0 : _onTopMouseMoveListen.remove();\n    };\n  }, [visible, isMoving, x, y, rotate, movable]);\n  useEffect(function () {\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n    return function () {\n      onKeyDownListener.remove();\n    };\n  }, [visible, showLeftOrRightSwitches, current]);\n  var imgNode = /*#__PURE__*/React.createElement(PreviewImage, _extends({}, imgCommonProps, {\n    width: props.width,\n    height: props.height,\n    imgRef: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    alt: alt,\n    style: {\n      transform: \"translate3d(\".concat(transform.x, \"px, \").concat(transform.y, \"px, 0) scale3d(\").concat(transform.flipX ? '-' : '').concat(scale, \", \").concat(transform.flipY ? '-' : '').concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\"),\n      transitionDuration: !enableTransition && '0s'\n    },\n    fallback: fallback,\n    src: src,\n    onWheel: onWheel,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick\n  }));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: transitionName,\n    maskTransitionName: maskTransitionName,\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    visible: visible,\n    wrapClassName: wrapClassName,\n    rootClassName: rootClassName,\n    getContainer: getContainer\n  }, restProps, {\n    afterClose: onAfterClose\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\")\n  }, imageRender ? imageRender(imgNode, _objectSpread({\n    transform: transform\n  }, groupContext ? {\n    current: current\n  } : {})) : imgNode)), /*#__PURE__*/React.createElement(Operations, {\n    visible: visible,\n    transform: transform,\n    maskTransitionName: maskTransitionName,\n    closeIcon: closeIcon,\n    getContainer: getContainer,\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    icons: icons,\n    countRender: countRender,\n    showSwitch: showLeftOrRightSwitches,\n    showProgress: showOperationsProgress,\n    current: current,\n    count: count,\n    scale: scale,\n    minScale: minScale,\n    maxScale: maxScale,\n    toolbarRender: toolbarRender,\n    onSwitchLeft: onSwitchLeft,\n    onSwitchRight: onSwitchRight,\n    onZoomIn: onZoomIn,\n    onZoomOut: onZoomOut,\n    onRotateRight: onRotateRight,\n    onRotateLeft: onRotateLeft,\n    onFlipX: onFlipX,\n    onFlipY: onFlipY,\n    onClose: onClose\n  }));\n};\nexport default Preview;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}