{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calculateColor, calculateOffset } from \"../util\";\nimport Palette from \"./Palette\";\nimport Gradient from \"./Gradient\";\nimport Handler from \"./Handler\";\nimport Transform from \"./Transform\";\nvar Slider = function Slider(_ref) {\n  var gradientColors = _ref.gradientColors,\n    direction = _ref.direction,\n    _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'hue' : _ref$type,\n    color = _ref.color,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled,\n    prefixCls = _ref.prefixCls;\n  var sliderRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var _useColorDrag = useColorDrag({\n      color: color,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      calculate: function calculate(containerRef) {\n        return calculateOffset(containerRef, transformRef, color, type);\n      },\n      onDragChange: function onDragChange(offsetValue) {\n        var calcColor = calculateColor({\n          offset: offsetValue,\n          targetRef: transformRef,\n          containerRef: sliderRef,\n          color: color,\n          type: type\n        });\n        colorRef.current = calcColor;\n        onChange(calcColor);\n      },\n      onDragChangeComplete: function onDragChangeComplete() {\n        onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current, type);\n      },\n      direction: 'x',\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: sliderRef,\n    className: classNames(\"\".concat(prefixCls, \"-slider\"), \"\".concat(prefixCls, \"-slider-\").concat(type)),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    offset: offset,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    size: \"small\",\n    color: value,\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(Gradient, {\n    colors: gradientColors,\n    direction: direction,\n    type: type,\n    prefixCls: prefixCls\n  })));\n};\nexport default Slider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}