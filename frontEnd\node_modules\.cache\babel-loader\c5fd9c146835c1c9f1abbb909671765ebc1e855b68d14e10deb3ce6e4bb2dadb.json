{"ast": null, "code": "const genRadius = radiusBase => {\n  let radiusLG = radiusBase;\n  let radiusSM = radiusBase;\n  let radiusXS = radiusBase;\n  let radiusOuter = radiusBase;\n  // radiusLG\n  if (radiusBase < 6 && radiusBase >= 5) {\n    radiusLG = radiusBase + 1;\n  } else if (radiusBase < 16 && radiusBase >= 6) {\n    radiusLG = radiusBase + 2;\n  } else if (radiusBase >= 16) {\n    radiusLG = 16;\n  }\n  // radiusSM\n  if (radiusBase < 7 && radiusBase >= 5) {\n    radiusSM = 4;\n  } else if (radiusBase < 8 && radiusBase >= 7) {\n    radiusSM = 5;\n  } else if (radiusBase < 14 && radiusBase >= 8) {\n    radiusSM = 6;\n  } else if (radiusBase < 16 && radiusBase >= 14) {\n    radiusSM = 7;\n  } else if (radiusBase >= 16) {\n    radiusSM = 8;\n  }\n  // radiusXS\n  if (radiusBase < 6 && radiusBase >= 2) {\n    radiusXS = 1;\n  } else if (radiusBase >= 6) {\n    radiusXS = 2;\n  }\n  // radiusOuter\n  if (radiusBase > 4 && radiusBase < 8) {\n    radiusOuter = 4;\n  } else if (radiusBase >= 8) {\n    radiusOuter = 6;\n  }\n  return {\n    borderRadius: radiusBase > 16 ? 16 : radiusBase,\n    borderRadiusXS: radiusXS,\n    borderRadiusSM: radiusSM,\n    borderRadiusLG: radiusLG,\n    borderRadiusOuter: radiusOuter\n  };\n};\nexport default genRadius;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}