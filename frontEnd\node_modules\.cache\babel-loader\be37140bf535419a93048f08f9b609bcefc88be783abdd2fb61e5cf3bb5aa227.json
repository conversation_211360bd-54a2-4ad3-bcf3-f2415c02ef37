{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable default-case */\nimport classNames from 'classnames';\nimport { useBaseProps } from 'rc-select';\nimport * as React from 'react';\nimport CascaderContext from \"../context\";\nimport { getFullPathKeys, isLeaf, scrollIntoParentView, toPathKey, toPathKeys, toPathValueStr } from \"../utils/commonUtil\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport CacheContent from \"./CacheContent\";\nimport Column, { FIX_LABEL } from \"./Column\";\nimport useActive from \"./useActive\";\nimport useKeyboard from \"./useKeyboard\";\nvar RefOptionList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _optionColumns$, _optionColumns$$optio, _ref3, _classNames;\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    direction = _useBaseProps.direction,\n    open = _useBaseProps.open;\n  var containerRef = React.useRef();\n  var rtl = direction === 'rtl';\n  var _React$useContext = React.useContext(CascaderContext),\n    options = _React$useContext.options,\n    values = _React$useContext.values,\n    halfValues = _React$useContext.halfValues,\n    fieldNames = _React$useContext.fieldNames,\n    changeOnSelect = _React$useContext.changeOnSelect,\n    onSelect = _React$useContext.onSelect,\n    searchOptions = _React$useContext.searchOptions,\n    dropdownPrefixCls = _React$useContext.dropdownPrefixCls,\n    loadData = _React$useContext.loadData,\n    expandTrigger = _React$useContext.expandTrigger;\n  var mergedPrefixCls = dropdownPrefixCls || prefixCls;\n\n  // ========================= loadData =========================\n  var _React$useState = React.useState([]),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    loadingKeys = _React$useState2[0],\n    setLoadingKeys = _React$useState2[1];\n  var internalLoadData = function internalLoadData(valueCells) {\n    // Do not load when search\n    if (!loadData || searchValue) {\n      return;\n    }\n    var optionList = toPathOptions(valueCells, options, fieldNames);\n    var rawOptions = optionList.map(function (_ref) {\n      var option = _ref.option;\n      return option;\n    });\n    var lastOption = rawOptions[rawOptions.length - 1];\n    if (lastOption && !isLeaf(lastOption, fieldNames)) {\n      var pathKey = toPathKey(valueCells);\n      setLoadingKeys(function (keys) {\n        return [].concat(_toConsumableArray(keys), [pathKey]);\n      });\n      loadData(rawOptions);\n    }\n  };\n\n  // zombieJ: This is bad. We should make this same as `rc-tree` to use Promise instead.\n  React.useEffect(function () {\n    if (loadingKeys.length) {\n      loadingKeys.forEach(function (loadingKey) {\n        var valueStrCells = toPathValueStr(loadingKey);\n        var optionList = toPathOptions(valueStrCells, options, fieldNames, true).map(function (_ref2) {\n          var option = _ref2.option;\n          return option;\n        });\n        var lastOption = optionList[optionList.length - 1];\n        if (!lastOption || lastOption[fieldNames.children] || isLeaf(lastOption, fieldNames)) {\n          setLoadingKeys(function (keys) {\n            return keys.filter(function (key) {\n              return key !== loadingKey;\n            });\n          });\n        }\n      });\n    }\n  }, [options, loadingKeys, fieldNames]);\n\n  // ========================== Values ==========================\n  var checkedSet = React.useMemo(function () {\n    return new Set(toPathKeys(values));\n  }, [values]);\n  var halfCheckedSet = React.useMemo(function () {\n    return new Set(toPathKeys(halfValues));\n  }, [halfValues]);\n\n  // ====================== Accessibility =======================\n  var _useActive = useActive(),\n    _useActive2 = _slicedToArray(_useActive, 2),\n    activeValueCells = _useActive2[0],\n    setActiveValueCells = _useActive2[1];\n\n  // =========================== Path ===========================\n  var onPathOpen = function onPathOpen(nextValueCells) {\n    setActiveValueCells(nextValueCells);\n\n    // Trigger loadData\n    internalLoadData(nextValueCells);\n  };\n  var isSelectable = function isSelectable(option) {\n    var disabled = option.disabled;\n    var isMergedLeaf = isLeaf(option, fieldNames);\n    return !disabled && (isMergedLeaf || changeOnSelect || multiple);\n  };\n  var onPathSelect = function onPathSelect(valuePath, leaf) {\n    var fromKeyboard = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    onSelect(valuePath);\n    if (!multiple && (leaf || changeOnSelect && (expandTrigger === 'hover' || fromKeyboard))) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================== Option ==========================\n  var mergedOptions = React.useMemo(function () {\n    if (searchValue) {\n      return searchOptions;\n    }\n    return options;\n  }, [searchValue, searchOptions, options]);\n\n  // ========================== Column ==========================\n  var optionColumns = React.useMemo(function () {\n    var optionList = [{\n      options: mergedOptions\n    }];\n    var currentList = mergedOptions;\n    var fullPathKeys = getFullPathKeys(currentList, fieldNames);\n    var _loop = function _loop() {\n      var activeValueCell = activeValueCells[i];\n      var currentOption = currentList.find(function (option, index) {\n        return (fullPathKeys[index] ? toPathKey(fullPathKeys[index]) : option[fieldNames.value]) === activeValueCell;\n      });\n      var subOptions = currentOption === null || currentOption === void 0 ? void 0 : currentOption[fieldNames.children];\n      if (!(subOptions !== null && subOptions !== void 0 && subOptions.length)) {\n        return \"break\";\n      }\n      currentList = subOptions;\n      optionList.push({\n        options: subOptions\n      });\n    };\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _ret = _loop();\n      if (_ret === \"break\") break;\n    }\n    return optionList;\n  }, [mergedOptions, activeValueCells, fieldNames]);\n\n  // ========================= Keyboard =========================\n  var onKeyboardSelect = function onKeyboardSelect(selectValueCells, option) {\n    if (isSelectable(option)) {\n      onPathSelect(selectValueCells, isLeaf(option, fieldNames), true);\n    }\n  };\n  useKeyboard(ref, mergedOptions, fieldNames, activeValueCells, onPathOpen, onKeyboardSelect);\n\n  // >>>>> Active Scroll\n  React.useEffect(function () {\n    for (var i = 0; i < activeValueCells.length; i += 1) {\n      var _containerRef$current;\n      var cellPath = activeValueCells.slice(0, i + 1);\n      var cellKeyPath = toPathKey(cellPath);\n      var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelector(\"li[data-path-key=\\\"\".concat(cellKeyPath.replace(/\\\\{0,2}\"/g, '\\\\\"'), \"\\\"]\") // matches unescaped double quotes\n      );\n\n      if (ele) {\n        scrollIntoParentView(ele);\n      }\n    }\n  }, [activeValueCells]);\n\n  // ========================== Render ==========================\n  // >>>>> Empty\n  var isEmpty = !((_optionColumns$ = optionColumns[0]) !== null && _optionColumns$ !== void 0 && (_optionColumns$$optio = _optionColumns$.options) !== null && _optionColumns$$optio !== void 0 && _optionColumns$$optio.length);\n  var emptyList = [(_ref3 = {}, _defineProperty(_ref3, fieldNames.value, '__EMPTY__'), _defineProperty(_ref3, FIX_LABEL, notFoundContent), _defineProperty(_ref3, \"disabled\", true), _ref3)];\n  var columnProps = _objectSpread(_objectSpread({}, props), {}, {\n    multiple: !isEmpty && multiple,\n    onSelect: onPathSelect,\n    onActive: onPathOpen,\n    onToggleOpen: toggleOpen,\n    checkedSet: checkedSet,\n    halfCheckedSet: halfCheckedSet,\n    loadingKeys: loadingKeys,\n    isSelectable: isSelectable\n  });\n\n  // >>>>> Columns\n  var mergedOptionColumns = isEmpty ? [{\n    options: emptyList\n  }] : optionColumns;\n  var columnNodes = mergedOptionColumns.map(function (col, index) {\n    var prevValuePath = activeValueCells.slice(0, index);\n    var activeValue = activeValueCells[index];\n    return /*#__PURE__*/React.createElement(Column, _extends({\n      key: index\n    }, columnProps, {\n      searchValue: searchValue,\n      prefixCls: mergedPrefixCls,\n      options: col.options,\n      prevValuePath: prevValuePath,\n      activeValue: activeValue\n    }));\n  });\n\n  // >>>>> Render\n  return /*#__PURE__*/React.createElement(CacheContent, {\n    open: open\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(mergedPrefixCls, \"-menus\"), (_classNames = {}, _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-menu-empty\"), isEmpty), _defineProperty(_classNames, \"\".concat(mergedPrefixCls, \"-rtl\"), rtl), _classNames)),\n    ref: containerRef\n  }, columnNodes));\n});\nexport default RefOptionList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}