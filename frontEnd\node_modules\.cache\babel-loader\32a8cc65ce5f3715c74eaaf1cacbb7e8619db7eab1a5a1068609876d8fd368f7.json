{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport TableContext from \"../context/TableContext\";\n/** Check if cell is in hover range */\nfunction inHoverRange(cellStartRow, cellRowSpan, startRow, endRow) {\n  var cellEndRow = cellStartRow + cellRowSpan - 1;\n  return cellStartRow <= endRow && cellEndRow >= startRow;\n}\nexport default function useHoverState(rowIndex, rowSpan) {\n  return useContext(TableContext, function (ctx) {\n    var hovering = inHoverRange(rowIndex, rowSpan || 1, ctx.hoverStartRow, ctx.hoverEndRow);\n    return [hovering, ctx.onHover];\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}