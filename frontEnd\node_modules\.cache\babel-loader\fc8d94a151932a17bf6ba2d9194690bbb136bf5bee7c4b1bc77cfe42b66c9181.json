{"ast": null, "code": "import React, { useEffect, useState } from 'react';\nimport { generateColor, getAlphaColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [alphaValue, setAlphaValue] = useState(generateColor(value || '#000'));\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setAlphaValue(value);\n    }\n  }, [value]);\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    if (!value) {\n      setAlphaValue(genColor);\n    }\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getAlphaColor(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}