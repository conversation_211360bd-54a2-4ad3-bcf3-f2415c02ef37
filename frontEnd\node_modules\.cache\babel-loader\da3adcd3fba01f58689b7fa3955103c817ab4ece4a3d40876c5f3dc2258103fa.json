{"ast": null, "code": "import { lintWarning } from \"./utils\";\nfunction isConcatSelector(selector) {\n  var _selector$match;\n  var notContent = ((_selector$match = selector.match(/:not\\(([^)]*)\\)/)) === null || _selector$match === void 0 ? void 0 : _selector$match[1]) || '';\n\n  // split selector. e.g.\n  // `h1#a.b` => ['h1', #a', '.b']\n  var splitCells = notContent.split(/(\\[[^[]*])|(?=[.#])/).filter(function (str) {\n    return str;\n  });\n  return splitCells.length > 1;\n}\nfunction parsePath(info) {\n  return info.parentSelectors.reduce(function (prev, cur) {\n    if (!prev) {\n      return cur;\n    }\n    return cur.includes('&') ? cur.replace(/&/g, prev) : \"\".concat(prev, \" \").concat(cur);\n  }, '');\n}\nvar linter = function linter(key, value, info) {\n  var parentSelectorPath = parsePath(info);\n  var notList = parentSelectorPath.match(/:not\\([^)]*\\)/g) || [];\n  if (notList.length > 0 && notList.some(isConcatSelector)) {\n    lintWarning(\"Concat ':not' selector not support in legacy browsers.\", info);\n  }\n};\nexport default linter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}