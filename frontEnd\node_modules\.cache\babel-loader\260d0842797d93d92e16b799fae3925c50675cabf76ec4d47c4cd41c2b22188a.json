{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport RangeContext from \"../../RangeContext\";\nimport { formatValue, getWeekStartDate, isSameDate, isSameMonth, WEEK_DAY_COUNT } from \"../../utils/dateUtil\";\nimport PanelBody from \"../PanelBody\";\nfunction DateBody(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    prefixColumn = props.prefixColumn,\n    locale = props.locale,\n    rowCount = props.rowCount,\n    viewDate = props.viewDate,\n    value = props.value,\n    cellRender = props.cellRender,\n    isSameCell = props.isSameCell;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var baseDate = getWeekStartDate(locale.locale, generateConfig, viewDate);\n  var cellPrefixCls = \"\".concat(prefixCls, \"-cell\");\n  var weekFirstDay = generateConfig.locale.getWeekFirstDay(locale.locale);\n  var today = generateConfig.getNow();\n\n  // ============================== Header ==============================\n  var headerCells = [];\n  var weekDaysLocale = locale.shortWeekDays || (generateConfig.locale.getShortWeekDays ? generateConfig.locale.getShortWeekDays(locale.locale) : []);\n  if (prefixColumn) {\n    headerCells.push( /*#__PURE__*/React.createElement(\"th\", {\n      key: \"empty\",\n      \"aria-label\": \"empty cell\"\n    }));\n  }\n  for (var i = 0; i < WEEK_DAY_COUNT; i += 1) {\n    headerCells.push( /*#__PURE__*/React.createElement(\"th\", {\n      key: i\n    }, weekDaysLocale[(i + weekFirstDay) % WEEK_DAY_COUNT]));\n  }\n\n  // =============================== Body ===============================\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: cellPrefixCls,\n    today: today,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: prefixColumn ? null : rangedValue,\n    hoverRangedValue: prefixColumn ? null : hoverRangedValue,\n    isSameCell: isSameCell || function (current, target) {\n      return isSameDate(generateConfig, current, target);\n    },\n    isInView: function isInView(date) {\n      return isSameMonth(generateConfig, date, viewDate);\n    },\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addDate(date, offset);\n    }\n  });\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      today: today,\n      type: 'date',\n      locale: locale\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: rowCount,\n    colNum: WEEK_DAY_COUNT,\n    baseDate: baseDate,\n    getCellNode: getCellNode,\n    getCellText: generateConfig.getDate,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addDate,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY-MM-DD',\n        generateConfig: generateConfig\n      });\n    },\n    headerCells: headerCells\n  }));\n}\nexport default DateBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}