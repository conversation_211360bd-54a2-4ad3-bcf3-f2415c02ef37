{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"label\", \"children\", \"key\", \"type\"];\nimport * as React from 'react';\nimport MenuItemGroup from \"../MenuItemGroup\";\nimport SubMenu from \"../SubMenu\";\nimport Divider from \"../Divider\";\nimport MenuItem from \"../MenuItem\";\nimport { parseChildren } from \"./commonUtil\";\nfunction convertItemsToNodes(list) {\n  return (list || []).map(function (opt, index) {\n    if (opt && _typeof(opt) === 'object') {\n      var _ref = opt,\n        label = _ref.label,\n        children = _ref.children,\n        key = _ref.key,\n        type = _ref.type,\n        restProps = _objectWithoutProperties(_ref, _excluded);\n      var mergedKey = key !== null && key !== void 0 ? key : \"tmp-\".concat(index);\n\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          // Group\n          return /*#__PURE__*/React.createElement(MenuItemGroup, _extends({\n            key: mergedKey\n          }, restProps, {\n            title: label\n          }), convertItemsToNodes(children));\n        }\n\n        // Sub Menu\n        return /*#__PURE__*/React.createElement(SubMenu, _extends({\n          key: mergedKey\n        }, restProps, {\n          title: label\n        }), convertItemsToNodes(children));\n      }\n\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return /*#__PURE__*/React.createElement(Divider, _extends({\n          key: mergedKey\n        }, restProps));\n      }\n      return /*#__PURE__*/React.createElement(MenuItem, _extends({\n        key: mergedKey\n      }, restProps), label);\n    }\n    return null;\n  }).filter(function (opt) {\n    return opt;\n  });\n}\nexport function parseItems(children, items, keyPath) {\n  var childNodes = children;\n  if (items) {\n    childNodes = convertItemsToNodes(items);\n  }\n  return parseChildren(childNodes, keyPath);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}