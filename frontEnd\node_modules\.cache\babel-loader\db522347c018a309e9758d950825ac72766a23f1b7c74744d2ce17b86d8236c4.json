{"ast": null, "code": "import { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport theme from '../../theme';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nconst isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const {\n    token: {\n      colorBgElevated\n    }\n  } = theme.useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.map(preset => `panel-${preset.label}`), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map(preset => {\n    var _a;\n    return {\n      key: `panel-${preset.label}`,\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map(presetColor => /*#__PURE__*/React.createElement(ColorBlock, {\n        key: `preset-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      })) : /*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}