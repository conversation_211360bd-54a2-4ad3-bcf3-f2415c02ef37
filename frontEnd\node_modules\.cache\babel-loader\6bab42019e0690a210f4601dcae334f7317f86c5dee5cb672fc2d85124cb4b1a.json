{"ast": null, "code": "import * as React from 'react';\nimport SliderContext from '../context';\nimport Dot from './Dot';\nexport default function Steps(props) {\n  var prefixCls = props.prefixCls,\n    marks = props.marks,\n    dots = props.dots,\n    style = props.style,\n    activeStyle = props.activeStyle;\n  var _React$useContext = React.useContext(SliderContext),\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    step = _React$useContext.step;\n  var stepDots = React.useMemo(function () {\n    var dotSet = new Set();\n    // Add marks\n    marks.forEach(function (mark) {\n      dotSet.add(mark.value);\n    });\n    // Fill dots\n    if (dots && step !== null) {\n      var current = min;\n      while (current <= max) {\n        dotSet.add(current);\n        current += step;\n      }\n    }\n    return Array.from(dotSet);\n  }, [min, max, step, dots, marks]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-step\")\n  }, stepDots.map(function (dotValue) {\n    return /*#__PURE__*/React.createElement(Dot, {\n      prefixCls: prefixCls,\n      key: dotValue,\n      value: dotValue,\n      style: style,\n      activeStyle: activeStyle\n    });\n  }));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}