const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const axios = require('axios');
require('dotenv').config();

/**
 * TEST AND FIX CURRENT PAYMENT ISSUE
 * 
 * This script will:
 * 1. Find all users with recent unauthorized activations
 * 2. Verify their payment status with ZenoPay
 * 3. Revoke access for unconfirmed payments
 * 4. Test the new security measures
 */

async function testAndFixCurrentIssue() {
  try {
    console.log('🔍 TESTING AND FIXING CURRENT PAYMENT ISSUE');
    console.log('=' .repeat(60));
    console.log('');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Step 1: Find all recent active subscriptions (last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const recentActiveSubscriptions = await Subscription.find({
      status: 'active',
      createdAt: { $gte: twentyFourHoursAgo }
    }).populate('user', 'firstName lastName username email phoneNumber subscriptionStatus')
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`🔍 Found ${recentActiveSubscriptions.length} recent active subscriptions\n`);
    
    if (recentActiveSubscriptions.length === 0) {
      console.log('✅ No recent active subscriptions found');
    } else {
      console.log('📋 Recent Active Subscriptions:');
      console.log('-'.repeat(50));
      
      for (let i = 0; i < recentActiveSubscriptions.length; i++) {
        const sub = recentActiveSubscriptions[i];
        const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
        const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));
        const minutesAgo = Math.floor((timeSinceCreated % (1000 * 60 * 60)) / (1000 * 60));
        
        console.log(`\n${i + 1}. User: ${sub.user.username} (${sub.user.firstName} ${sub.user.lastName})`);
        console.log(`   Email: ${sub.user.email}`);
        console.log(`   Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`   Created: ${hoursAgo}h ${minutesAgo}m ago`);
        console.log(`   Payment Status: ${sub.paymentStatus}`);
        console.log(`   Subscription Status: ${sub.status}`);
        
        // Check payment verification
        let paymentVerified = false;
        let shouldRevoke = false;
        let revokeReason = '';
        
        if (!sub.paymentHistory || sub.paymentHistory.length === 0) {
          shouldRevoke = true;
          revokeReason = 'NO_PAYMENT_HISTORY';
          console.log(`   🚨 NO PAYMENT HISTORY - SHOULD REVOKE`);
        } else {
          const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
          console.log(`   Order ID: ${latestPayment.orderId}`);
          console.log(`   Payment Date: ${latestPayment.paymentDate}`);
          
          if (!latestPayment.orderId || !latestPayment.orderId.startsWith('ORDER_')) {
            shouldRevoke = true;
            revokeReason = 'INVALID_ORDER_ID';
            console.log(`   🚨 INVALID ORDER ID - SHOULD REVOKE`);
          } else {
            // Verify with ZenoPay
            console.log(`   🔍 Verifying with ZenoPay...`);
            paymentVerified = await verifyPaymentWithZenoPay(latestPayment.orderId);
            
            if (!paymentVerified) {
              shouldRevoke = true;
              revokeReason = 'PAYMENT_NOT_CONFIRMED';
              console.log(`   🚨 PAYMENT NOT CONFIRMED - SHOULD REVOKE`);
            } else {
              console.log(`   ✅ Payment verified - legitimate subscription`);
            }
          }
        }
        
        // Take action if needed
        if (shouldRevoke) {
          console.log(`   🚫 REVOKING ACCESS - Reason: ${revokeReason}`);
          await revokeUnauthorizedAccess(sub, revokeReason);
        }
      }
    }
    
    // Step 2: Find all pending subscriptions with paid status
    console.log('\n\n🔍 Checking pending subscriptions with paid status...\n');
    
    const pendingPaidSubscriptions = await Subscription.find({
      paymentStatus: 'paid',
      status: 'pending'
    }).populate('user', 'firstName lastName username email')
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📋 Found ${pendingPaidSubscriptions.length} pending subscriptions with paid status`);
    
    if (pendingPaidSubscriptions.length > 0) {
      console.log('\n🔒 These subscriptions are correctly waiting for manual activation:');
      console.log('-'.repeat(60));
      
      for (let i = 0; i < pendingPaidSubscriptions.length; i++) {
        const sub = pendingPaidSubscriptions[i];
        const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
        const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));
        
        console.log(`\n${i + 1}. User: ${sub.user.username}`);
        console.log(`   Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`   Created: ${hoursAgo}h ago`);
        console.log(`   Status: PENDING (awaiting manual activation)`);
        
        if (sub.paymentHistory && sub.paymentHistory.length > 0) {
          const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
          console.log(`   Order ID: ${latestPayment.orderId}`);
          
          // Verify payment
          const verified = await verifyPaymentWithZenoPay(latestPayment.orderId);
          if (verified) {
            console.log(`   ✅ Payment verified - ready for manual activation`);
          } else {
            console.log(`   ❌ Payment not verified - should not be activated`);
          }
        }
      }
    }
    
    // Step 3: Test security measures
    console.log('\n\n🛡️ SECURITY MEASURES STATUS:');
    console.log('=' .repeat(40));
    console.log('✅ Auto-activation disabled in payment verification service');
    console.log('✅ Auto-activation disabled in payment route webhook');
    console.log('✅ Manual activation endpoint created for admins');
    console.log('✅ Payment verification enhanced with strict checks');
    console.log('✅ All new subscriptions require manual approval');
    
    console.log('\n📊 SUMMARY:');
    console.log(`- Recent active subscriptions checked: ${recentActiveSubscriptions.length}`);
    console.log(`- Pending paid subscriptions: ${pendingPaidSubscriptions.length}`);
    console.log(`- Security fixes applied: ✅ COMPLETE`);
    
  } catch (error) {
    console.error('❌ Error during test and fix:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

async function verifyPaymentWithZenoPay(orderId) {
  try {
    const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
    const response = await axios.get(statusUrl, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY,
      },
      timeout: 10000
    });
    
    if (response.data && response.data.data && response.data.data.length > 0) {
      const paymentData = response.data.data[0];
      console.log(`     📊 ZenoPay Status: ${paymentData.payment_status}`);
      return paymentData.payment_status === 'COMPLETED';
    } else {
      console.log(`     ⚠️ No payment data found in ZenoPay`);
      return false;
    }
    
  } catch (error) {
    console.log(`     ❌ Error checking ZenoPay: ${error.message}`);
    return false;
  }
}

async function revokeUnauthorizedAccess(subscription, reason) {
  try {
    console.log(`     🚫 Revoking subscription ${subscription._id}`);
    
    // Update subscription status
    subscription.status = 'expired';
    subscription.paymentStatus = 'failed';
    
    // Add revocation note
    if (subscription.paymentHistory && subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.referenceId = `REVOKED_${Date.now()}_${reason}`;
    }
    
    await subscription.save();
    
    // Update user status
    await User.findByIdAndUpdate(subscription.user._id, {
      subscriptionStatus: 'free',
      paymentRequired: true,
      subscriptionEndDate: null,
      subscriptionStartDate: null
    });
    
    console.log(`     ✅ Access revoked - Reason: ${reason}`);
    
  } catch (error) {
    console.error(`     ❌ Error revoking access:`, error.message);
  }
}

// Run the test and fix
testAndFixCurrentIssue();
