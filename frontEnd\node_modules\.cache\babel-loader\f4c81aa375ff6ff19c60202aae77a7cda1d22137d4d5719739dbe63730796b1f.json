{"ast": null, "code": "import * as React from 'react';\nimport { toPathOptions } from \"../utils/treeUtil\";\nexport default (function (options, fieldNames) {\n  return React.useCallback(function (rawValues) {\n    var missingValues = [];\n    var existsValues = [];\n    rawValues.forEach(function (valueCell) {\n      var pathOptions = toPathOptions(valueCell, options, fieldNames);\n      if (pathOptions.every(function (opt) {\n        return opt.option;\n      })) {\n        existsValues.push(valueCell);\n      } else {\n        missingValues.push(valueCell);\n      }\n    });\n    return [existsValues, missingValues];\n  }, [options, fieldNames]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}