{"ast": null, "code": "const genStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper-rtl`]: {\n      direction: 'rtl',\n      table: {\n        direction: 'rtl'\n      },\n      [`${componentCls}-pagination-left`]: {\n        justifyContent: 'flex-end'\n      },\n      [`${componentCls}-pagination-right`]: {\n        justifyContent: 'flex-start'\n      },\n      [`${componentCls}-row-expand-icon`]: {\n        float: 'right',\n        '&::after': {\n          transform: 'rotate(-90deg)'\n        },\n        '&-collapsed::before': {\n          transform: 'rotate(180deg)'\n        },\n        '&-collapsed::after': {\n          transform: 'rotate(0deg)'\n        }\n      },\n      [`${componentCls}-container`]: {\n        '&::before': {\n          insetInlineStart: 'unset',\n          insetInlineEnd: 0\n        },\n        '&::after': {\n          insetInlineStart: 0,\n          insetInlineEnd: 'unset'\n        },\n        [`${componentCls}-row-indent`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}