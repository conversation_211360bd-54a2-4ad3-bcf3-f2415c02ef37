{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { YEAR_DECADE_COUNT } from \"./constant\";\nimport useCellClassName from \"../../hooks/useCellClassName\";\nimport { formatValue, isSameYear } from \"../../utils/dateUtil\";\nimport RangeContext from \"../../RangeContext\";\nimport PanelBody from \"../PanelBody\";\nexport var YEAR_COL_COUNT = 3;\nvar YEAR_ROW_COUNT = 4;\nfunction YearBody(props) {\n  var prefixCls = props.prefixCls,\n    value = props.value,\n    viewDate = props.viewDate,\n    locale = props.locale,\n    generateConfig = props.generateConfig,\n    cellRender = props.cellRender;\n  var _React$useContext = React.useContext(RangeContext),\n    rangedValue = _React$useContext.rangedValue,\n    hoverRangedValue = _React$useContext.hoverRangedValue;\n  var yearPrefixCls = \"\".concat(prefixCls, \"-cell\");\n\n  // =============================== Year ===============================\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;\n  var endYear = startYear + YEAR_DECADE_COUNT - 1;\n  var baseYear = generateConfig.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));\n  var today = generateConfig.getNow();\n  var isInView = function isInView(date) {\n    var currentYearNumber = generateConfig.getYear(date);\n    return startYear <= currentYearNumber && currentYearNumber <= endYear;\n  };\n  var getCellClassName = useCellClassName({\n    cellPrefixCls: yearPrefixCls,\n    value: value,\n    generateConfig: generateConfig,\n    rangedValue: rangedValue,\n    hoverRangedValue: hoverRangedValue,\n    isSameCell: function isSameCell(current, target) {\n      return isSameYear(generateConfig, current, target);\n    },\n    isInView: isInView,\n    offsetCell: function offsetCell(date, offset) {\n      return generateConfig.addYear(date, offset);\n    }\n  });\n  var getCellNode = cellRender ? function (date, wrapperNode) {\n    return cellRender(date, {\n      originNode: wrapperNode,\n      today: today,\n      type: 'year',\n      locale: locale\n    });\n  } : undefined;\n  return /*#__PURE__*/React.createElement(PanelBody, _extends({}, props, {\n    rowNum: YEAR_ROW_COUNT,\n    colNum: YEAR_COL_COUNT,\n    baseDate: baseYear,\n    getCellNode: getCellNode,\n    getCellText: generateConfig.getYear,\n    getCellClassName: getCellClassName,\n    getCellDate: generateConfig.addYear,\n    titleCell: function titleCell(date) {\n      return formatValue(date, {\n        locale: locale,\n        format: 'YYYY',\n        generateConfig: generateConfig\n      });\n    }\n  }));\n}\nexport default YearBody;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}