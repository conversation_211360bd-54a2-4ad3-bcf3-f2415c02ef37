{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nexport default (function (treeData, searchValue, _ref) {\n  var treeNodeFilterProp = _ref.treeNodeFilterProp,\n    filterTreeNode = _ref.filterTreeNode,\n    fieldNames = _ref.fieldNames;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc;\n    if (typeof filterTreeNode === 'function') {\n      filterOptionFunc = filterTreeNode;\n    } else {\n      var upperStr = searchValue.toUpperCase();\n      filterOptionFunc = function filterOptionFunc(_, dataNode) {\n        var value = dataNode[treeNodeFilterProp];\n        return String(value).toUpperCase().includes(upperStr);\n      };\n    }\n    function dig(list) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return list.reduce(function (total, dataNode) {\n        var children = dataNode[fieldChildren];\n        var match = keepAll || filterOptionFunc(searchValue, fillLegacyProps(dataNode));\n        var childList = dig(children || [], match);\n        if (match || childList.length) {\n          total.push(_objectSpread(_objectSpread({}, dataNode), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, childList)));\n        }\n        return total;\n      }, []);\n    }\n    return dig(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}