{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const moveDownIn = new Keyframes('antMoveDownIn', {\n  '0%': {\n    transform: 'translate3d(0, 100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveDownOut = new Keyframes('antMoveDownOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(0, 100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveLeftIn = new Keyframes('antMoveLeftIn', {\n  '0%': {\n    transform: 'translate3d(-100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveLeftOut = new Keyframes('antMoveLeftOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(-100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveRightIn = new Keyframes('antMoveRightIn', {\n  '0%': {\n    transform: 'translate3d(100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveRightOut = new Keyframes('antMoveRightOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(100%, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nexport const moveUpIn = new Keyframes('antMoveUpIn', {\n  '0%': {\n    transform: 'translate3d(0, -100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  }\n});\nexport const moveUpOut = new Keyframes('antMoveUpOut', {\n  '0%': {\n    transform: 'translate3d(0, 0, 0)',\n    transformOrigin: '0 0',\n    opacity: 1\n  },\n  '100%': {\n    transform: 'translate3d(0, -100%, 0)',\n    transformOrigin: '0 0',\n    opacity: 0\n  }\n});\nconst moveMotion = {\n  'move-up': {\n    inKeyframes: moveUpIn,\n    outKeyframes: moveUpOut\n  },\n  'move-down': {\n    inKeyframes: moveDownIn,\n    outKeyframes: moveDownOut\n  },\n  'move-left': {\n    inKeyframes: moveLeftIn,\n    outKeyframes: moveLeftOut\n  },\n  'move-right': {\n    inKeyframes: moveRightIn,\n    outKeyframes: moveRightOut\n  }\n};\nexport const initMoveMotion = (token, motionName) => {\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-${motionName}`;\n  const {\n    inKeyframes,\n    outKeyframes\n  } = moveMotion[motionName];\n  return [initMotion(motionCls, inKeyframes, outKeyframes, token.motionDurationMid), {\n    [`\n        ${motionCls}-enter,\n        ${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: token.motionEaseOutCirc\n    },\n    [`${motionCls}-leave`]: {\n      animationTimingFunction: token.motionEaseInOutCirc\n    }\n  }];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}