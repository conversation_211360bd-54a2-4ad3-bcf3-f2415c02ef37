{"ast": null, "code": "import * as React from 'react';\nexport default function DropIndicator(_ref) {\n  var dropPosition = _ref.dropPosition,\n    dropLevelOffset = _ref.dropLevelOffset,\n    indent = _ref.indent;\n  var style = {\n    pointerEvents: 'none',\n    position: 'absolute',\n    right: 0,\n    backgroundColor: 'red',\n    height: 2\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 1:\n      style.bottom = 0;\n      style.left = -dropLevelOffset * indent;\n      break;\n    case 0:\n      style.bottom = 0;\n      style.left = indent;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}