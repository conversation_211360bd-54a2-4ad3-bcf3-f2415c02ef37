{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { getValue, updateValues } from \"../utils/miscUtil\";\nimport { getClosingViewDate, isSameYear, isSameMonth, isSameDecade } from \"../utils/dateUtil\";\nfunction getStartEndDistance(startDate, endDate, picker, generateConfig) {\n  var startNext = getClosingViewDate(startDate, picker, generateConfig, 1);\n  function getDistance(compareFunc) {\n    if (compareFunc(startDate, endDate)) {\n      return 'same';\n    }\n    if (compareFunc(startNext, endDate)) {\n      return 'closing';\n    }\n    return 'far';\n  }\n  switch (picker) {\n    case 'year':\n      return getDistance(function (start, end) {\n        return isSameDecade(generateConfig, start, end);\n      });\n    case 'quarter':\n    case 'month':\n      return getDistance(function (start, end) {\n        return isSameYear(generateConfig, start, end);\n      });\n    default:\n      return getDistance(function (start, end) {\n        return isSameMonth(generateConfig, start, end);\n      });\n  }\n}\nfunction getRangeViewDate(values, index, picker, generateConfig) {\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  if (index === 0) {\n    return startDate;\n  }\n  if (startDate && endDate) {\n    var distance = getStartEndDistance(startDate, endDate, picker, generateConfig);\n    switch (distance) {\n      case 'same':\n        return startDate;\n      case 'closing':\n        return startDate;\n      default:\n        return getClosingViewDate(endDate, picker, generateConfig, -1);\n    }\n  }\n  return startDate;\n}\nexport default function useRangeViewDates(_ref) {\n  var values = _ref.values,\n    picker = _ref.picker,\n    defaultDates = _ref.defaultDates,\n    generateConfig = _ref.generateConfig;\n  var _React$useState = React.useState(function () {\n      return [getValue(defaultDates, 0), getValue(defaultDates, 1)];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    defaultViewDates = _React$useState2[0],\n    setDefaultViewDates = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    viewDates = _React$useState4[0],\n    setInternalViewDates = _React$useState4[1];\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  function getViewDate(index) {\n    // If set default view date, use it\n    if (defaultViewDates[index]) {\n      return defaultViewDates[index];\n    }\n    return getValue(viewDates, index) || getRangeViewDate(values, index, picker, generateConfig) || startDate || endDate || generateConfig.getNow();\n  }\n  function setViewDate(viewDate, index) {\n    if (viewDate) {\n      var newViewDates = updateValues(viewDates, viewDate, index);\n      // Set view date will clean up default one\n      setDefaultViewDates(\n      // Should always be an array\n      updateValues(defaultViewDates, null, index) || [null, null]);\n\n      // Reset another one when not have value\n      var anotherIndex = (index + 1) % 2;\n      if (!getValue(values, anotherIndex)) {\n        newViewDates = updateValues(newViewDates, viewDate, anotherIndex);\n      }\n      setInternalViewDates(newViewDates);\n    } else if (startDate || endDate) {\n      // Reset all when has values when `viewDate` is `null` which means from open trigger\n      setInternalViewDates(null);\n    }\n  }\n  return [getViewDate, setViewDate];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}