{"ast": null, "code": "import useMemo from \"rc-util/es/hooks/useMemo\";\nimport shallowEqual from \"rc-util/es/isEqual\";\nimport { formatValue, isEqual } from \"../utils/dateUtil\";\nexport default function useValueTexts(value, _ref) {\n  var formatList = _ref.formatList,\n    generateConfig = _ref.generateConfig,\n    locale = _ref.locale;\n  return useMemo(function () {\n    if (!value) {\n      return [[''], ''];\n    }\n\n    // We will convert data format back to first format\n    var firstValueText = '';\n    var fullValueTexts = [];\n    for (var i = 0; i < formatList.length; i += 1) {\n      var format = formatList[i];\n      var formatStr = formatValue(value, {\n        generateConfig: generateConfig,\n        locale: locale,\n        format: format\n      });\n      fullValueTexts.push(formatStr);\n      if (i === 0) {\n        firstValueText = formatStr;\n      }\n    }\n    return [fullValueTexts, firstValueText];\n  }, [value, formatList], function (prev, next) {\n    return (\n      // Not Same Date\n      !isEqual(generateConfig, prev[0], next[0]) ||\n      // Not Same format\n      !shallowEqual(prev[1], next[1], true)\n    );\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}