{"ast": null, "code": "import { addUniqueItem, removeItem } from '../../utils/array.mjs';\nclass NodeStack {\n  constructor() {\n    this.members = [];\n  }\n  add(node) {\n    addUniqueItem(this.members, node);\n    node.scheduleRender();\n  }\n  remove(node) {\n    removeItem(this.members, node);\n    if (node === this.prevLead) {\n      this.prevLead = undefined;\n    }\n    if (node === this.lead) {\n      const prevLead = this.members[this.members.length - 1];\n      if (prevLead) {\n        this.promote(prevLead);\n      }\n    }\n  }\n  relegate(node) {\n    const indexOfNode = this.members.findIndex(member => node === member);\n    if (indexOfNode === 0) return false;\n    /**\n     * Find the next projection node that is present\n     */\n    let prevLead;\n    for (let i = indexOfNode; i >= 0; i--) {\n      const member = this.members[i];\n      if (member.isPresent !== false) {\n        prevLead = member;\n        break;\n      }\n    }\n    if (prevLead) {\n      this.promote(prevLead);\n      return true;\n    } else {\n      return false;\n    }\n  }\n  promote(node, preserveFollowOpacity) {\n    const prevLead = this.lead;\n    if (node === prevLead) return;\n    this.prevLead = prevLead;\n    this.lead = node;\n    node.show();\n    if (prevLead) {\n      prevLead.instance && prevLead.scheduleRender();\n      node.scheduleRender();\n      node.resumeFrom = prevLead;\n      if (preserveFollowOpacity) {\n        node.resumeFrom.preserveOpacity = true;\n      }\n      if (prevLead.snapshot) {\n        node.snapshot = prevLead.snapshot;\n        node.snapshot.latestValues = prevLead.animationValues || prevLead.latestValues;\n      }\n      if (node.root && node.root.isUpdating) {\n        node.isLayoutDirty = true;\n      }\n      const {\n        crossfade\n      } = node.options;\n      if (crossfade === false) {\n        prevLead.hide();\n      }\n      /**\n       * TODO:\n       *   - Test border radius when previous node was deleted\n       *   - boxShadow mixing\n       *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n       *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n       *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n       * ---\n       *   - Crossfade opacity of root nodes\n       *   - layoutId changes after animation\n       *   - layoutId changes mid animation\n       */\n    }\n  }\n\n  exitAnimationComplete() {\n    this.members.forEach(node => {\n      const {\n        options,\n        resumingFrom\n      } = node;\n      options.onExitComplete && options.onExitComplete();\n      if (resumingFrom) {\n        resumingFrom.options.onExitComplete && resumingFrom.options.onExitComplete();\n      }\n    });\n  }\n  scheduleRender() {\n    this.members.forEach(node => {\n      node.instance && node.scheduleRender(false);\n    });\n  }\n  /**\n   * Clear any leads that have been removed this render to prevent them from being\n   * used in future animations and to prevent memory leaks\n   */\n  removeLeadSnapshot() {\n    if (this.lead && this.lead.snapshot) {\n      this.lead.snapshot = undefined;\n    }\n  }\n}\nexport { NodeStack };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}