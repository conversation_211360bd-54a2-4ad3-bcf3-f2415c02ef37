{"ast": null, "code": "// eslint-disable-next-line import/prefer-default-export\nexport const operationUnit = token => ({\n  // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.\n  // And Typography use this to generate link style which should not do this.\n  color: token.colorLink,\n  textDecoration: 'none',\n  outline: 'none',\n  cursor: 'pointer',\n  transition: `color ${token.motionDurationSlow}`,\n  '&:focus, &:hover': {\n    color: token.colorLinkHover\n  },\n  '&:active': {\n    color: token.colorLinkActive\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}