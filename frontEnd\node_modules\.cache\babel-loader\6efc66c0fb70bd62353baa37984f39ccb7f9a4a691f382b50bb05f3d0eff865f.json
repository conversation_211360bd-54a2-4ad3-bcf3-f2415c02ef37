{"ast": null, "code": "const genSizeStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${paddingVertical}px ${paddingHorizontal}px`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: `-${paddingHorizontal / 2}px`\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `-${paddingVertical}px -${paddingHorizontal}px`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: `-${paddingVertical}px`,\n          marginInline: `${token.tableExpandColumnWidth - paddingHorizontal}px -${paddingHorizontal}px`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-column`]: {\n        paddingInlineStart: `${paddingHorizontal / 4}px`\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}