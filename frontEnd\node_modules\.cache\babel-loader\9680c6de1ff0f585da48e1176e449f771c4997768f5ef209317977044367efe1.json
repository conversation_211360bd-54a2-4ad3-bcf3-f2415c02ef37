{"ast": null, "code": "import { animateStyle } from './index.mjs';\nimport { isWaapiSupportedEasing } from './easing.mjs';\nimport { getFinalKeyframe } from './utils/get-final-keyframe.mjs';\nimport { animateValue } from '../js/index.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { memo } from '../../../utils/memo.mjs';\nimport { noop } from '../../../utils/noop.mjs';\nimport { frame, cancelFrame } from '../../../frameloop/frame.mjs';\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\", \"backgroundColor\"]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" || valueName === \"backgroundColor\" || !isWaapiSupportedEasing(options.ease);\nfunction createAcceleratedAnimation(value, valueName, {\n  onUpdate,\n  onComplete,\n  ...options\n}) {\n  const canAccelerateAnimation = supportsWaapi() && acceleratedValues.has(valueName) && !options.repeatDelay && options.repeatType !== \"mirror\" && options.damping !== 0 && options.type !== \"inertia\";\n  if (!canAccelerateAnimation) return false;\n  /**\n   * TODO: Unify with js/index\n   */\n  let hasStopped = false;\n  let resolveFinishedPromise;\n  let currentFinishedPromise;\n  /**\n   * Cancelling an animation will write to the DOM. For safety we want to defer\n   * this until the next `update` frame lifecycle. This flag tracks whether we\n   * have a pending cancel, if so we shouldn't allow animations to finish.\n   */\n  let pendingCancel = false;\n  /**\n   * Resolve the current Promise every time we enter the\n   * finished state. This is WAAPI-compatible behaviour.\n   */\n  const updateFinishedPromise = () => {\n    currentFinishedPromise = new Promise(resolve => {\n      resolveFinishedPromise = resolve;\n    });\n  };\n  // Create the first finished promise\n  updateFinishedPromise();\n  let {\n    keyframes,\n    duration = 300,\n    ease,\n    times\n  } = options;\n  /**\n   * If this animation needs pre-generated keyframes then generate.\n   */\n  if (requiresPregeneratedKeyframes(valueName, options)) {\n    const sampleAnimation = animateValue({\n      ...options,\n      repeat: 0,\n      delay: 0\n    });\n    let state = {\n      done: false,\n      value: keyframes[0]\n    };\n    const pregeneratedKeyframes = [];\n    /**\n     * Bail after 20 seconds of pre-generated keyframes as it's likely\n     * we're heading for an infinite loop.\n     */\n    let t = 0;\n    while (!state.done && t < maxDuration) {\n      state = sampleAnimation.sample(t);\n      pregeneratedKeyframes.push(state.value);\n      t += sampleDelta;\n    }\n    times = undefined;\n    keyframes = pregeneratedKeyframes;\n    duration = t - sampleDelta;\n    ease = \"linear\";\n  }\n  const animation = animateStyle(value.owner.current, valueName, keyframes, {\n    ...options,\n    duration,\n    /**\n     * This function is currently not called if ease is provided\n     * as a function so the cast is safe.\n     *\n     * However it would be possible for a future refinement to port\n     * in easing pregeneration from Motion One for browsers that\n     * support the upcoming `linear()` easing function.\n     */\n    ease: ease,\n    times\n  });\n  const cancelAnimation = () => {\n    pendingCancel = false;\n    animation.cancel();\n  };\n  const safeCancel = () => {\n    pendingCancel = true;\n    frame.update(cancelAnimation);\n    resolveFinishedPromise();\n    updateFinishedPromise();\n  };\n  /**\n   * Prefer the `onfinish` prop as it's more widely supported than\n   * the `finished` promise.\n   *\n   * Here, we synchronously set the provided MotionValue to the end\n   * keyframe. If we didn't, when the WAAPI animation is finished it would\n   * be removed from the element which would then revert to its old styles.\n   */\n  animation.onfinish = () => {\n    if (pendingCancel) return;\n    value.set(getFinalKeyframe(keyframes, options));\n    onComplete && onComplete();\n    safeCancel();\n  };\n  /**\n   * Animation interrupt callback.\n   */\n  const controls = {\n    then(resolve, reject) {\n      return currentFinishedPromise.then(resolve, reject);\n    },\n    attachTimeline(timeline) {\n      animation.timeline = timeline;\n      animation.onfinish = null;\n      return noop;\n    },\n    get time() {\n      return millisecondsToSeconds(animation.currentTime || 0);\n    },\n    set time(newTime) {\n      animation.currentTime = secondsToMilliseconds(newTime);\n    },\n    get speed() {\n      return animation.playbackRate;\n    },\n    set speed(newSpeed) {\n      animation.playbackRate = newSpeed;\n    },\n    get duration() {\n      return millisecondsToSeconds(duration);\n    },\n    play: () => {\n      if (hasStopped) return;\n      animation.play();\n      /**\n       * Cancel any pending cancel tasks\n       */\n      cancelFrame(cancelAnimation);\n    },\n    pause: () => animation.pause(),\n    stop: () => {\n      hasStopped = true;\n      if (animation.playState === \"idle\") return;\n      /**\n       * WAAPI doesn't natively have any interruption capabilities.\n       *\n       * Rather than read commited styles back out of the DOM, we can\n       * create a renderless JS animation and sample it twice to calculate\n       * its current value, \"previous\" value, and therefore allow\n       * Motion to calculate velocity for any subsequent animation.\n       */\n      const {\n        currentTime\n      } = animation;\n      if (currentTime) {\n        const sampleAnimation = animateValue({\n          ...options,\n          autoplay: false\n        });\n        value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n      }\n      safeCancel();\n    },\n    complete: () => {\n      if (pendingCancel) return;\n      animation.finish();\n    },\n    cancel: safeCancel\n  };\n  return controls;\n}\nexport { createAcceleratedAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}