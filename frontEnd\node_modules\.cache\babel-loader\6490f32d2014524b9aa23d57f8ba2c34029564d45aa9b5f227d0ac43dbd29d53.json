{"ast": null, "code": "function buildProjectionTransform(delta, treeScale, latestTransform) {\n  let transform = \"\";\n  /**\n   * The translations we use to calculate are always relative to the viewport coordinate space.\n   * But when we apply scales, we also scale the coordinate space of an element and its children.\n   * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n   * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n   */\n  const xTranslate = delta.x.translate / treeScale.x;\n  const yTranslate = delta.y.translate / treeScale.y;\n  if (xTranslate || yTranslate) {\n    transform = `translate3d(${xTranslate}px, ${yTranslate}px, 0) `;\n  }\n  /**\n   * Apply scale correction for the tree transform.\n   * This will apply scale to the screen-orientated axes.\n   */\n  if (treeScale.x !== 1 || treeScale.y !== 1) {\n    transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n  }\n  if (latestTransform) {\n    const {\n      rotate,\n      rotateX,\n      rotateY\n    } = latestTransform;\n    if (rotate) transform += `rotate(${rotate}deg) `;\n    if (rotateX) transform += `rotateX(${rotateX}deg) `;\n    if (rotateY) transform += `rotateY(${rotateY}deg) `;\n  }\n  /**\n   * Apply scale to match the size of the element to the size we want it.\n   * This will apply scale to the element-orientated axes.\n   */\n  const elementScaleX = delta.x.scale * treeScale.x;\n  const elementScaleY = delta.y.scale * treeScale.y;\n  if (elementScaleX !== 1 || elementScaleY !== 1) {\n    transform += `scale(${elementScaleX}, ${elementScaleY})`;\n  }\n  return transform || \"none\";\n}\nexport { buildProjectionTransform };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}