{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport Header from \"../Header\";\nimport { DECADE_DISTANCE_COUNT } from \"./constant\";\nimport PanelContext from \"../../PanelContext\";\nfunction DecadeHeader(props) {\n  var prefixCls = props.prefixCls,\n    generateConfig = props.generateConfig,\n    viewDate = props.viewDate,\n    onPrevDecades = props.onPrevDecades,\n    onNextDecades = props.onNextDecades;\n  var _React$useContext = React.useContext(PanelContext),\n    hideHeader = _React$useContext.hideHeader;\n  if (hideHeader) {\n    return null;\n  }\n  var headerPrefixCls = \"\".concat(prefixCls, \"-header\");\n  var yearNumber = generateConfig.getYear(viewDate);\n  var startYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;\n  var endYear = startYear + DECADE_DISTANCE_COUNT - 1;\n  return /*#__PURE__*/React.createElement(Header, _extends({}, props, {\n    prefixCls: headerPrefixCls,\n    onSuperPrev: onPrevDecades,\n    onSuperNext: onNextDecades\n  }), startYear, \"-\", endYear);\n}\nexport default DecadeHeader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}