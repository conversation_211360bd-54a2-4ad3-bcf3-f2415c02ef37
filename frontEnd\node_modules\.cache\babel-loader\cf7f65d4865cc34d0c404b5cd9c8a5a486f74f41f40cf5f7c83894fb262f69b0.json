{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nvar htmlClassList = {};\nvar docBodyClassList = {};\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction removeClass(at, cls) {\n  at.classList.remove(cls);\n}\n\n/* istanbul ignore next */\nfunction resetState() {\n  var htmlElement = document.getElementsByTagName(\"html\")[0];\n  for (var cls in htmlClassList) {\n    removeClass(htmlElement, htmlClassList[cls]);\n  }\n  var body = document.body;\n  for (var _cls in docBodyClassList) {\n    removeClass(body, docBodyClassList[_cls]);\n  }\n  htmlClassList = {};\n  docBodyClassList = {};\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    var classes = document.getElementsByTagName(\"html\")[0].className;\n    var buffer = \"Show tracked classes:\\n\\n\";\n    buffer += \"<html /> (\" + classes + \"):\\n  \";\n    for (var x in htmlClassList) {\n      buffer += \"  \" + x + \" \" + htmlClassList[x] + \"\\n  \";\n    }\n    classes = document.body.className;\n    buffer += \"\\n\\ndoc.body (\" + classes + \"):\\n  \";\n    for (var _x in docBodyClassList) {\n      buffer += \"  \" + _x + \" \" + docBodyClassList[_x] + \"\\n  \";\n    }\n    buffer += \"\\n\";\n    console.log(buffer);\n  }\n}\n/* eslint-enable no-console */\n\n/**\n * Track the number of reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar incrementReference = function incrementReference(poll, className) {\n  if (!poll[className]) {\n    poll[className] = 0;\n  }\n  poll[className] += 1;\n  return className;\n};\n\n/**\n * Drop the reference of a class.\n * @param {object} poll The poll to receive the reference.\n * @param {string} className The class name.\n * @return {string}\n */\nvar decrementReference = function decrementReference(poll, className) {\n  if (poll[className]) {\n    poll[className] -= 1;\n  }\n  return className;\n};\n\n/**\n * Track a class and add to the given class list.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be tracked.\n */\nvar trackClass = function trackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    incrementReference(poll, className);\n    classListRef.add(className);\n  });\n};\n\n/**\n * Untrack a class and remove from the given class list if the reference\n * reaches 0.\n * @param {Object} classListRef A class list of an element.\n * @param {Object} poll         The poll to be used.\n * @param {Array}  classes      The list of classes to be untracked.\n */\nvar untrackClass = function untrackClass(classListRef, poll, classes) {\n  classes.forEach(function (className) {\n    decrementReference(poll, className);\n    poll[className] === 0 && classListRef.remove(className);\n  });\n};\n\n/**\n * Public inferface to add classes to the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar add = exports.add = function add(element, classString) {\n  return trackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};\n\n/**\n * Public inferface to remove classes from the document.body.\n * @param {string} bodyClass The class string to be added.\n *                           It may contain more then one class\n *                           with ' ' as separator.\n */\nvar remove = exports.remove = function remove(element, classString) {\n  return untrackClass(element.classList, element.nodeName.toLowerCase() == \"html\" ? htmlClassList : docBodyClassList, classString.split(\" \"));\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}