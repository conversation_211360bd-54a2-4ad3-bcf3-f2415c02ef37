{"ast": null, "code": "import { animateValue } from './js/index.mjs';\nimport { noop } from '../../utils/noop.mjs';\nfunction createInstantAnimation({\n  keyframes,\n  delay,\n  onUpdate,\n  onComplete\n}) {\n  const setValue = () => {\n    onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n    onComplete && onComplete();\n    /**\n     * TODO: As this API grows it could make sense to always return\n     * animateValue. This will be a bigger project as animateValue\n     * is frame-locked whereas this function resolves instantly.\n     * This is a behavioural change and also has ramifications regarding\n     * assumptions within tests.\n     */\n    return {\n      time: 0,\n      speed: 1,\n      duration: 0,\n      play: noop,\n      pause: noop,\n      stop: noop,\n      then: resolve => {\n        resolve();\n        return Promise.resolve();\n      },\n      cancel: noop,\n      complete: noop\n    };\n  };\n  return delay ? animateValue({\n    keyframes: [0, 1],\n    duration: 0,\n    delay,\n    onComplete: setValue\n  }) : setValue();\n}\nexport { createInstantAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}