{"ast": null, "code": "import { TinyColor } from '@ctrl/tinycolor';\nimport { clearFix, resetComponent } from '../../style';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport genBorderedStyle from './bordered';\nimport genEllipsisStyle from './ellipsis';\nimport genEmptyStyle from './empty';\nimport genExpandStyle from './expand';\nimport genFilterStyle from './filter';\nimport genFixedStyle from './fixed';\nimport genPaginationStyle from './pagination';\nimport genRadiusStyle from './radius';\nimport genRtlStyle from './rtl';\nimport genSelectionStyle from './selection';\nimport genSizeStyle from './size';\nimport genSorterStyle from './sorter';\nimport genStickyStyle from './sticky';\nimport genSummaryStyle from './summary';\nconst genTableStyle = token => {\n  const {\n    componentCls,\n    fontWeightStrong,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableFontSize,\n    tableBg,\n    tableRadius,\n    tableHeaderTextColor,\n    motionDurationMid,\n    tableHeaderBg,\n    tableHeaderCellSplitColor,\n    tableRowHoverBg,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableFooterTextColor,\n    tableFooterBg,\n    paddingContentVerticalLG\n  } = token;\n  const tableBorder = `${lineWidth}px ${lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({\n      clear: 'both',\n      maxWidth: '100%'\n    }, clearFix()), {\n      [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n        fontSize: tableFontSize,\n        background: tableBg,\n        borderRadius: `${tableRadius}px ${tableRadius}px 0 0`\n      }),\n      // https://github.com/ant-design/ant-design/issues/17611\n      table: {\n        width: '100%',\n        textAlign: 'start',\n        borderRadius: `${tableRadius}px ${tableRadius}px 0 0`,\n        borderCollapse: 'separate',\n        borderSpacing: 0\n      },\n      // ============================= Cell =============================\n      [`\n          ${componentCls}-thead > tr > th,\n          ${componentCls}-tbody > tr > th,\n          ${componentCls}-tbody > tr > td,\n          tfoot > tr > th,\n          tfoot > tr > td\n        `]: {\n        position: 'relative',\n        padding: `${paddingContentVerticalLG}px ${tablePaddingHorizontal}px`,\n        overflowWrap: 'break-word'\n      },\n      // ============================ Title =============================\n      [`${componentCls}-title`]: {\n        padding: `${tablePaddingVertical}px ${tablePaddingHorizontal}px`\n      },\n      // ============================ Header ============================\n      [`${componentCls}-thead`]: {\n        [`\n          > tr > th,\n          > tr > td\n        `]: {\n          position: 'relative',\n          color: tableHeaderTextColor,\n          fontWeight: fontWeightStrong,\n          textAlign: 'start',\n          background: tableHeaderBg,\n          borderBottom: tableBorder,\n          transition: `background ${motionDurationMid} ease`,\n          \"&[colspan]:not([colspan='1'])\": {\n            textAlign: 'center'\n          },\n          [`&:not(:last-child):not(${componentCls}-selection-column):not(${componentCls}-row-expand-icon-cell):not([colspan])::before`]: {\n            position: 'absolute',\n            top: '50%',\n            insetInlineEnd: 0,\n            width: 1,\n            height: '1.6em',\n            backgroundColor: tableHeaderCellSplitColor,\n            transform: 'translateY(-50%)',\n            transition: `background-color ${motionDurationMid}`,\n            content: '\"\"'\n          }\n        },\n        '> tr:not(:last-child) > th[colspan]': {\n          borderBottom: 0\n        }\n      },\n      // ============================ Body ============================\n      [`${componentCls}-tbody`]: {\n        '> tr': {\n          [`> th, > td`]: {\n            transition: `background ${motionDurationMid}, border-color ${motionDurationMid}`,\n            borderBottom: tableBorder,\n            // ========================= Nest Table ===========================\n            [`\n              > ${componentCls}-wrapper:only-child,\n              > ${componentCls}-expanded-row-fixed > ${componentCls}-wrapper:only-child\n            `]: {\n              [componentCls]: {\n                marginBlock: `-${tablePaddingVertical}px`,\n                marginInline: `${token.tableExpandColumnWidth - tablePaddingHorizontal}px -${tablePaddingHorizontal}px`,\n                [`${componentCls}-tbody > tr:last-child > td`]: {\n                  borderBottom: 0,\n                  '&:first-child, &:last-child': {\n                    borderRadius: 0\n                  }\n                }\n              }\n            }\n          },\n          '> th': {\n            position: 'relative',\n            color: tableHeaderTextColor,\n            fontWeight: fontWeightStrong,\n            textAlign: 'start',\n            background: tableHeaderBg,\n            borderBottom: tableBorder,\n            transition: `background ${motionDurationMid} ease`\n          },\n          [`\n            &${componentCls}-row:hover > th,\n            &${componentCls}-row:hover > td,\n            > th${componentCls}-cell-row-hover,\n            > td${componentCls}-cell-row-hover\n          `]: {\n            background: tableRowHoverBg\n          },\n          [`&${componentCls}-row-selected`]: {\n            [`> th, > td`]: {\n              background: tableSelectedRowBg\n            },\n            [`&:hover > th, &:hover > td`]: {\n              background: tableSelectedRowHoverBg\n            }\n          }\n        }\n      },\n      // ============================ Footer ============================\n      [`${componentCls}-footer`]: {\n        padding: `${tablePaddingVertical}px ${tablePaddingHorizontal}px`,\n        color: tableFooterTextColor,\n        background: tableFooterBg\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Table', token => {\n  const {\n    controlItemBgActive,\n    controlItemBgActiveHover,\n    colorTextPlaceholder,\n    colorTextHeading,\n    colorSplit,\n    colorBorderSecondary,\n    fontSize,\n    padding,\n    paddingXS,\n    paddingSM,\n    controlHeight,\n    colorFillAlter,\n    colorIcon,\n    colorIconHover,\n    opacityLoading,\n    colorBgContainer,\n    borderRadiusLG,\n    colorFillContent,\n    colorFillSecondary,\n    controlInteractiveSize: checkboxSize\n  } = token;\n  const baseColorAction = new TinyColor(colorIcon);\n  const baseColorActionHover = new TinyColor(colorIconHover);\n  const tableSelectedRowBg = controlItemBgActive;\n  const zIndexTableFixed = 2;\n  const colorFillSecondarySolid = new TinyColor(colorFillSecondary).onBackground(colorBgContainer).toHexShortString();\n  const colorFillContentSolid = new TinyColor(colorFillContent).onBackground(colorBgContainer).toHexShortString();\n  const colorFillAlterSolid = new TinyColor(colorFillAlter).onBackground(colorBgContainer).toHexShortString();\n  const tableToken = mergeToken(token, {\n    tableFontSize: fontSize,\n    tableBg: colorBgContainer,\n    tableRadius: borderRadiusLG,\n    tablePaddingVertical: padding,\n    tablePaddingHorizontal: padding,\n    tablePaddingVerticalMiddle: paddingSM,\n    tablePaddingHorizontalMiddle: paddingXS,\n    tablePaddingVerticalSmall: paddingXS,\n    tablePaddingHorizontalSmall: paddingXS,\n    tableBorderColor: colorBorderSecondary,\n    tableHeaderTextColor: colorTextHeading,\n    tableHeaderBg: colorFillAlterSolid,\n    tableFooterTextColor: colorTextHeading,\n    tableFooterBg: colorFillAlterSolid,\n    tableHeaderCellSplitColor: colorBorderSecondary,\n    tableHeaderSortBg: colorFillSecondarySolid,\n    tableHeaderSortHoverBg: colorFillContentSolid,\n    tableHeaderIconColor: baseColorAction.clone().setAlpha(baseColorAction.getAlpha() * opacityLoading).toRgbString(),\n    tableHeaderIconColorHover: baseColorActionHover.clone().setAlpha(baseColorActionHover.getAlpha() * opacityLoading).toRgbString(),\n    tableBodySortBg: colorFillAlterSolid,\n    tableFixedHeaderSortActiveBg: colorFillSecondarySolid,\n    tableHeaderFilterActiveBg: colorFillContent,\n    tableFilterDropdownBg: colorBgContainer,\n    tableRowHoverBg: colorFillAlterSolid,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg: controlItemBgActiveHover,\n    zIndexTableFixed,\n    zIndexTableSticky: zIndexTableFixed + 1,\n    tableFontSizeMiddle: fontSize,\n    tableFontSizeSmall: fontSize,\n    tableSelectionColumnWidth: controlHeight,\n    tableExpandIconBg: colorBgContainer,\n    tableExpandColumnWidth: checkboxSize + 2 * token.padding,\n    tableExpandedRowBg: colorFillAlter,\n    // Dropdown\n    tableFilterDropdownWidth: 120,\n    tableFilterDropdownHeight: 264,\n    tableFilterDropdownSearchWidth: 140,\n    // Virtual Scroll Bar\n    tableScrollThumbSize: 8,\n    tableScrollThumbBg: colorTextPlaceholder,\n    tableScrollThumbBgHover: colorTextHeading,\n    tableScrollBg: colorSplit\n  });\n  return [genTableStyle(tableToken), genPaginationStyle(tableToken), genSummaryStyle(tableToken), genSorterStyle(tableToken), genFilterStyle(tableToken), genBorderedStyle(tableToken), genRadiusStyle(tableToken), genExpandStyle(tableToken), genSummaryStyle(tableToken), genEmptyStyle(tableToken), genSelectionStyle(tableToken), genFixedStyle(tableToken), genStickyStyle(tableToken), genEllipsisStyle(tableToken), genSizeStyle(tableToken), genRtlStyle(tableToken)];\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}