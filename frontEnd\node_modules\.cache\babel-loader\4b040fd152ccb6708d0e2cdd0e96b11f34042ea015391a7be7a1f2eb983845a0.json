{"ast": null, "code": "import * as React from 'react';\nimport { Item } from '../Item';\nexport default function useChildren(list, startIndex, endIndex, setNodeRef, renderFunc, _ref) {\n  var getKey = _ref.getKey;\n  return list.slice(startIndex, endIndex + 1).map(function (item, index) {\n    var eleIndex = startIndex + index;\n    var node = renderFunc(item, eleIndex, {\n      // style: status === 'MEASURE_START' ? { visibility: 'hidden' } : {},\n    });\n    var key = getKey(item);\n    return /*#__PURE__*/React.createElement(Item, {\n      key: key,\n      setRef: function setRef(ele) {\n        return setNodeRef(item, ele);\n      }\n    }, node);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}