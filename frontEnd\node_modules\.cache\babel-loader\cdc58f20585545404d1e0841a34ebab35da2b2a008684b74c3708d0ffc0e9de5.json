{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nvar scrollIds = new Map();\n\n/** Trigger when element is visible in view */\nexport function waitElementReady(element, callback) {\n  var id;\n  function tryOrNextFrame() {\n    if (isVisible(element)) {\n      callback();\n    } else {\n      id = raf(function () {\n        tryOrNextFrame();\n      });\n    }\n  }\n  tryOrNextFrame();\n  return function () {\n    raf.cancel(id);\n  };\n}\n\n/* eslint-disable no-param-reassign */\nexport function scrollTo(element, to, duration) {\n  if (scrollIds.get(element)) {\n    cancelAnimationFrame(scrollIds.get(element));\n  }\n\n  // jump to target if duration zero\n  if (duration <= 0) {\n    scrollIds.set(element, requestAnimationFrame(function () {\n      element.scrollTop = to;\n    }));\n    return;\n  }\n  var difference = to - element.scrollTop;\n  var perTick = difference / duration * 10;\n  scrollIds.set(element, requestAnimationFrame(function () {\n    element.scrollTop += perTick;\n    if (element.scrollTop !== to) {\n      scrollTo(element, to, duration - 10);\n    }\n  }));\n}\n/* eslint-enable */\n\nexport function createKeyDownHandler(event, _ref) {\n  var onLeftRight = _ref.onLeftRight,\n    onCtrlLeftRight = _ref.onCtrlLeftRight,\n    onUpDown = _ref.onUpDown,\n    onPageUpDown = _ref.onPageUpDown,\n    onEnter = _ref.onEnter;\n  var which = event.which,\n    ctrlKey = event.ctrlKey,\n    metaKey = event.metaKey;\n  switch (which) {\n    case KeyCode.LEFT:\n      if (ctrlKey || metaKey) {\n        if (onCtrlLeftRight) {\n          onCtrlLeftRight(-1);\n          return true;\n        }\n      } else if (onLeftRight) {\n        onLeftRight(-1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.RIGHT:\n      if (ctrlKey || metaKey) {\n        if (onCtrlLeftRight) {\n          onCtrlLeftRight(1);\n          return true;\n        }\n      } else if (onLeftRight) {\n        onLeftRight(1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.UP:\n      if (onUpDown) {\n        onUpDown(-1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.DOWN:\n      if (onUpDown) {\n        onUpDown(1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.PAGE_UP:\n      if (onPageUpDown) {\n        onPageUpDown(-1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.PAGE_DOWN:\n      if (onPageUpDown) {\n        onPageUpDown(1);\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n    case KeyCode.ENTER:\n      if (onEnter) {\n        onEnter();\n        return true;\n      }\n      /* istanbul ignore next */\n      break;\n  }\n  return false;\n}\n\n// ===================== Format =====================\nexport function getDefaultFormat(format, picker, showTime, use12Hours) {\n  var mergedFormat = format;\n  if (!mergedFormat) {\n    switch (picker) {\n      case 'time':\n        mergedFormat = use12Hours ? 'hh:mm:ss a' : 'HH:mm:ss';\n        break;\n      case 'week':\n        mergedFormat = 'gggg-wo';\n        break;\n      case 'month':\n        mergedFormat = 'YYYY-MM';\n        break;\n      case 'quarter':\n        mergedFormat = 'YYYY-[Q]Q';\n        break;\n      case 'year':\n        mergedFormat = 'YYYY';\n        break;\n      default:\n        mergedFormat = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';\n    }\n  }\n  return mergedFormat;\n}\nexport function getInputSize(picker, format, generateConfig) {\n  var defaultSize = picker === 'time' ? 8 : 10;\n  var length = typeof format === 'function' ? format(generateConfig.getNow()).length : format.length;\n  return Math.max(defaultSize, length) + 2;\n}\n\n// ===================== Window =====================\n\nvar globalClickFunc = null;\nvar clickCallbacks = new Set();\nexport function addGlobalMouseDownEvent(callback) {\n  if (!globalClickFunc && typeof window !== 'undefined' && window.addEventListener) {\n    globalClickFunc = function globalClickFunc(e) {\n      // Clone a new list to avoid repeat trigger events\n      _toConsumableArray(clickCallbacks).forEach(function (queueFunc) {\n        queueFunc(e);\n      });\n    };\n    window.addEventListener('mousedown', globalClickFunc);\n  }\n  clickCallbacks.add(callback);\n  return function () {\n    clickCallbacks.delete(callback);\n    if (clickCallbacks.size === 0) {\n      window.removeEventListener('mousedown', globalClickFunc);\n      globalClickFunc = null;\n    }\n  };\n}\nexport function getTargetFromEvent(e) {\n  var target = e.target;\n\n  // get target if in shadow dom\n  if (e.composed && target.shadowRoot) {\n    var _e$composedPath;\n    return ((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath.call(e)[0]) || target;\n  }\n  return target;\n}\n\n// ====================== Mode ======================\nvar getYearNextMode = function getYearNextMode(next) {\n  if (next === 'month' || next === 'date') {\n    return 'year';\n  }\n  return next;\n};\nvar getMonthNextMode = function getMonthNextMode(next) {\n  if (next === 'date') {\n    return 'month';\n  }\n  return next;\n};\nvar getQuarterNextMode = function getQuarterNextMode(next) {\n  if (next === 'month' || next === 'date') {\n    return 'quarter';\n  }\n  return next;\n};\nvar getWeekNextMode = function getWeekNextMode(next) {\n  if (next === 'date') {\n    return 'week';\n  }\n  return next;\n};\nexport var PickerModeMap = {\n  year: getYearNextMode,\n  month: getMonthNextMode,\n  quarter: getQuarterNextMode,\n  week: getWeekNextMode,\n  time: null,\n  date: null\n};\nexport function elementsContains(elements, target) {\n  return elements.some(function (ele) {\n    return ele && ele.contains(target);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}