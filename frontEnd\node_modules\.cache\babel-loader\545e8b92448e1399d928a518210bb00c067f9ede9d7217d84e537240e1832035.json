{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction parseSimpleTreeData(treeData, _ref) {\n  var id = _ref.id,\n    pId = _ref.pId,\n    rootPId = _ref.rootPId;\n  var keyNodes = {};\n  var rootNodeList = [];\n\n  // Fill in the map\n  var nodeList = treeData.map(function (node) {\n    var clone = _objectSpread({}, node);\n    var key = clone[id];\n    keyNodes[key] = clone;\n    clone.key = clone.key || key;\n    return clone;\n  });\n\n  // Connect tree\n  nodeList.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = keyNodes[parentKey];\n\n    // Fill parent\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    }\n\n    // Fill root tree node\n    if (parentKey === rootPId || !parent && rootPId === null) {\n      rootNodeList.push(node);\n    }\n  });\n  return rootNodeList;\n}\n\n/**\n * Convert `treeData` or `children` into formatted `treeData`.\n * Will not re-calculate if `treeData` or `children` not change.\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      return simpleMode ? parseSimpleTreeData(treeData, _objectSpread({\n        id: 'id',\n        pId: 'pId',\n        rootPId: null\n      }, simpleMode !== true ? simpleMode : {})) : treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}