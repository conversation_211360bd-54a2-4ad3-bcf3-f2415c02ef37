{"ast": null, "code": "import { textEllipsis } from '../../style';\nconst genEllipsisStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-cell-ellipsis`]: Object.assign(Object.assign({}, textEllipsis), {\n        wordBreak: 'keep-all',\n        // Fixed first or last should special process\n        [`\n          &${componentCls}-cell-fix-left-last,\n          &${componentCls}-cell-fix-right-first\n        `]: {\n          overflow: 'visible',\n          [`${componentCls}-cell-content`]: {\n            display: 'block',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          }\n        },\n        [`${componentCls}-column-title`]: {\n          overflow: 'hidden',\n          textOverflow: 'ellipsis',\n          wordBreak: 'keep-all'\n        }\n      })\n    }\n  };\n};\nexport default genEllipsisStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}