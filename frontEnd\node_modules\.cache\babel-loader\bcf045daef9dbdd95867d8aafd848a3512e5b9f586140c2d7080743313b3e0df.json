{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nconst TransButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const onKeyDown = event => {\n    const {\n      keyCode\n    } = event;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  const onKeyUp = event => {\n    const {\n      keyCode\n    } = event;\n    const {\n      onClick\n    } = props;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  const {\n      style,\n      noStyle,\n      disabled\n    } = props,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  let mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = Object.assign({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = Object.assign(Object.assign({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}