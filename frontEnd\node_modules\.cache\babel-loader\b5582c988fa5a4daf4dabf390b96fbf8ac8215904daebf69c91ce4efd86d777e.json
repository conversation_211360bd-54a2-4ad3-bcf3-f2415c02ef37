{"ast": null, "code": "import * as React from 'react';\nimport { executeValue } from \"./utils/miscUtil\";\nexport default function PresetPanel(props) {\n  var prefixCls = props.prefixCls,\n    presets = props.presets,\n    _onClick = props.onClick,\n    onHover = props.onHover;\n  if (!presets.length) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-presets\")\n  }, /*#__PURE__*/React.createElement(\"ul\", null, presets.map(function (_ref, index) {\n    var label = _ref.label,\n      value = _ref.value;\n    return /*#__PURE__*/React.createElement(\"li\", {\n      key: index,\n      onClick: function onClick() {\n        return _onClick === null || _onClick === void 0 ? void 0 : _onClick(executeValue(value));\n      },\n      onMouseEnter: function onMouseEnter() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(executeValue(value));\n      },\n      onMouseLeave: function onMouseLeave() {\n        return onHover === null || onHover === void 0 ? void 0 : onHover(null);\n      }\n    }, label);\n  })));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}