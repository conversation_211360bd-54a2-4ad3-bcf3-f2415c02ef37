{"ast": null, "code": "import React from 'react';\nexport function useCellRender(_ref) {\n  var cellRender = _ref.cellRender,\n    monthCellRender = _ref.monthCellRender,\n    dateRender = _ref.dateRender;\n  var mergedCellRender = React.useMemo(function () {\n    if (cellRender) return cellRender;\n    if (!monthCellRender && !dateRender) return undefined;\n    return function (current, info) {\n      var date = current;\n      if (dateRender && info.type === 'date') {\n        return dateRender(date, info.today);\n      }\n      if (monthCellRender && info.type === 'month') {\n        return monthCellRender(date, info.locale);\n      }\n      return info.originNode;\n    };\n  }, [cellRender, monthCellRender, dateRender]);\n  return mergedCellRender;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}