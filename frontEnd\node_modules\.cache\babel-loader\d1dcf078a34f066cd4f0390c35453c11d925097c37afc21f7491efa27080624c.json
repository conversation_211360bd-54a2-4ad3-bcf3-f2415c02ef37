{"ast": null, "code": "import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  return function (deltaY) {\n    var smoothOffset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var originScroll =\n    // Pass origin wheel when on the top\n    deltaY < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    deltaY > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}