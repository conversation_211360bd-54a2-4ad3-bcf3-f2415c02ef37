{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport SliderContext from '../context';\nimport { getOffset } from '../util';\nexport default function Track(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    start = props.start,\n    end = props.end,\n    index = props.index,\n    onStartMove = props.onStartMove;\n  var _React$useContext = React.useContext(SliderContext),\n    direction = _React$useContext.direction,\n    min = _React$useContext.min,\n    max = _React$useContext.max,\n    disabled = _React$useContext.disabled,\n    range = _React$useContext.range;\n  var trackPrefixCls = \"\".concat(prefixCls, \"-track\");\n  var offsetStart = getOffset(start, min, max);\n  var offsetEnd = getOffset(end, min, max);\n  // ============================ Events ============================\n  var onInternalStartMove = function onInternalStartMove(e) {\n    if (!disabled && onStartMove) {\n      onStartMove(e, -1);\n    }\n  };\n  // ============================ Render ============================\n  var positionStyle = {};\n  switch (direction) {\n    case 'rtl':\n      positionStyle.right = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'btt':\n      positionStyle.bottom = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    case 'ttb':\n      positionStyle.top = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.height = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n      break;\n    default:\n      positionStyle.left = \"\".concat(offsetStart * 100, \"%\");\n      positionStyle.width = \"\".concat(offsetEnd * 100 - offsetStart * 100, \"%\");\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(trackPrefixCls, range && \"\".concat(trackPrefixCls, \"-\").concat(index + 1)),\n    style: _objectSpread(_objectSpread({}, positionStyle), style),\n    onMouseDown: onInternalStartMove,\n    onTouchStart: onInternalStartMove\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}