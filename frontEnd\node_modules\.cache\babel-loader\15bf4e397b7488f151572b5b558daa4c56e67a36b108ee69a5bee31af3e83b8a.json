{"ast": null, "code": "import DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport TransButton from '../_util/transButton';\nimport Checkbox from '../checkbox';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nconst ListItem = props => {\n  const {\n    renderedText,\n    renderedEl,\n    item,\n    checked,\n    disabled,\n    prefixCls,\n    onClick,\n    onRemove,\n    showRemove\n  } = props;\n  const className = classNames(`${prefixCls}-content-item`, {\n    [`${prefixCls}-content-item-disabled`]: disabled || item.disabled,\n    [`${prefixCls}-content-item-checked`]: checked\n  });\n  let title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const liProps = {\n    className,\n    title\n  };\n  const labelNode = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-item-text`\n  }, renderedEl);\n  if (showRemove) {\n    return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), labelNode, /*#__PURE__*/React.createElement(TransButton, {\n      disabled: disabled || item.disabled,\n      className: `${prefixCls}-content-item-remove`,\n      \"aria-label\": contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.remove,\n      onClick: () => {\n        onRemove === null || onRemove === void 0 ? void 0 : onRemove(item);\n      }\n    }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n  }\n  // Default click to select\n  liProps.onClick = disabled || item.disabled ? undefined : () => onClick(item);\n  return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n    className: `${prefixCls}-checkbox`,\n    checked: checked,\n    disabled: disabled || item.disabled\n  }), labelNode);\n};\nexport default /*#__PURE__*/React.memo(ListItem);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}