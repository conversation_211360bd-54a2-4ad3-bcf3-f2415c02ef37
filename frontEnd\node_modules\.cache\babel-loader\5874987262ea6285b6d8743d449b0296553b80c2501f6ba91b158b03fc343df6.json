{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nexport default (function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n    var fileName = file.name || '';\n    var mimeType = file.type || '';\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim(); // This is something like */*,*  allow all files\n\n      if (/^\\*(\\/\\*)?$/.test(type)) {\n        return true;\n      } // like .jpg, .png\n\n      if (validType.charAt(0) === '.') {\n        var lowerFileName = fileName.toLowerCase();\n        var lowerType = validType.toLowerCase();\n        var affixList = [lowerType];\n        if (lowerType === '.jpg' || lowerType === '.jpeg') {\n          affixList = ['.jpg', '.jpeg'];\n        }\n        return affixList.some(function (affix) {\n          return lowerFileName.endsWith(affix);\n        });\n      } // This is something like a image/* mime type\n\n      if (/\\/\\*$/.test(validType)) {\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      } // Full match\n\n      if (mimeType === validType) {\n        return true;\n      } // Invalidate type should skip\n\n      if (/^\\w+$/.test(validType)) {\n        warning(false, \"Upload takes an invalidate 'accept' type '\".concat(validType, \"'.Skip for check.\"));\n        return true;\n      }\n      return false;\n    });\n  }\n  return true;\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}