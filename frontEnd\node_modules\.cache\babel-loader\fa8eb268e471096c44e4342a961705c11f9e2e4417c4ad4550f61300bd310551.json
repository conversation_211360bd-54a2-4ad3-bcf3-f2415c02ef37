{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    let {\n      prefixCls,\n      onExpand,\n      record,\n      expanded,\n      expandable\n    } = _ref;\n    const iconPrefix = `${prefixCls}-row-expand-icon`;\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, {\n        [`${iconPrefix}-spaced`]: !expandable,\n        [`${iconPrefix}-expanded`]: expandable && expanded,\n        [`${iconPrefix}-collapsed`]: expandable && !expanded\n      }),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}